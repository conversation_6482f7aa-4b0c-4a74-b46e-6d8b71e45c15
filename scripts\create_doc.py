#!/usr/bin/env python3
"""
API文档创建工具

使用方法:
    python create_doc.py --title "API文档" --url "https://api.example.com" --description "描述" --tags "tag1,tag2"

环境变量:
    API_DOCS_SERVER: API文档服务器地址
    API_DOCS_TOKEN: API认证令牌
"""

# Import built-in modules
import argparse
import json
import os
import sys
from typing import List
from typing import Optional
import urllib.error
from urllib.parse import urljoin
import urllib.request


def create_doc(
    title: str,
    url: str,
    description: Optional[str] = None,
    tags: Optional[List[str]] = None,
    server_url: Optional[str] = None,
    token: Optional[str] = None,
) -> dict:
    """
    创建API文档

    Args:
        title: 文档标题
        url: 文档URL
        description: 文档描述
        tags: 标签列表
        server_url: API服务器地址
        token: API认证令牌

    Returns:
        dict: 创建的文档信息
    """
    # 获取服务器地址和认证令牌
    server_url = server_url or os.environ.get("API_DOCS_SERVER", "https://api-docs.woa.com")
    token = token or os.environ.get("API_DOCS_TOKEN")

    # 构建请求数据
    data = {
        "title": title,
        "url": url,
        "description": description,
        "tags": tags or [],
    }

    # 构建请求
    api_url = urljoin(server_url, "/docs")  # 不需要 /api 前缀，因为 server_url 已经包含了
    headers = {
        "Content-Type": "application/json",
    }

    if token:
        headers["Authorization"] = f"Bearer {token}"

    try:
        # 发送请求
        request = urllib.request.Request(
            api_url,
            data=json.dumps(data).encode("utf-8"),
            headers=headers,
            method="POST",
        )

        # 处理响应
        with urllib.request.urlopen(request) as response:
            result = json.loads(response.read().decode("utf-8"))
            print(f"文档创建成功: {result['title']} (ID: {result['id']})")
            return result

    except urllib.error.HTTPError as e:
        error_message = e.read().decode("utf-8")
        print(f"错误: HTTP {e.code} - {error_message}", file=sys.stderr)
        sys.exit(1)
    except urllib.error.URLError as e:
        print(f"错误: 无法连接到服务器 - {str(e)}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"错误: {str(e)}", file=sys.stderr)
        sys.exit(1)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="创建API文档")

    # 必需参数
    parser.add_argument("--title", required=True, help="文档标题")
    parser.add_argument("--url", required=True, help="文档URL")

    # 可选参数
    parser.add_argument("--description", help="文档描述")
    parser.add_argument("--tags", help="标签列表，用逗号分隔")
    parser.add_argument("--server", help="API服务器地址")
    parser.add_argument("--token", help="API认证令牌")

    # 解析参数
    args = parser.parse_args()

    # 处理标签
    tags = args.tags.split(",") if args.tags else []

    # 创建文档
    create_doc(
        title=args.title,
        url=args.url,
        description=args.description,
        tags=tags,
        server_url=args.server,
        token=args.token,
    )


if __name__ == "__main__":
    main()
