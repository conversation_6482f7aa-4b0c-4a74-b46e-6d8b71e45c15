#!/usr/bin/env python3
"""
API 文档服务器的客户端库
这个库提供了与后端 API 直接交互的功能，供其他开发者使用
"""

import json
import os
import urllib.error
import urllib.request
from typing import Dict, List, Optional
from urllib.parse import urljoin


class ApiClient:
    """API 客户端类"""

    def __init__(self, server_url: Optional[str] = None, token: Optional[str] = None):
        """
        初始化 API 客户端

        Args:
            server_url: API 服务器地址，默认从环境变量获取
            token: API 认证令牌，默认从环境变量获取
        """
        self.server_url = server_url or os.environ.get("API_DOCS_SERVER", "https://api-docs.woa.com")
        self.token = token or os.environ.get("API_DOCS_TOKEN")

    def _make_request(self, path: str, method: str = "GET", data: Optional[Dict] = None) -> Dict:
        """
        发送 HTTP 请求

        Args:
            path: API 路径
            method: HTTP 方法
            data: 请求数据

        Returns:
            Dict: 响应数据
        """
        url = urljoin(self.server_url, path)
        headers = {"Content-Type": "application/json"}

        if self.token:
            headers["Authorization"] = f"Bearer {self.token}"

        request_data = json.dumps(data).encode("utf-8") if data else None

        try:
            request = urllib.request.Request(
                url,
                data=request_data,
                headers=headers,
                method=method
            )

            with urllib.request.urlopen(request) as response:
                return json.loads(response.read().decode("utf-8"))

        except urllib.error.HTTPError as e:
            error_message = e.read().decode("utf-8")
            raise ApiError(f"HTTP {e.code} - {error_message}")
        except urllib.error.URLError as e:
            raise ApiError(f"连接错误 - {str(e)}")
        except Exception as e:
            raise ApiError(str(e))

    def create_doc(self, title: str, url: str, description: Optional[str] = None,
                  tags: Optional[List[str]] = None) -> Dict:
        """
        创建新文档

        Args:
            title: 文档标题
            url: 文档URL
            description: 文档描述
            tags: 标签列表

        Returns:
            Dict: 创建的文档信息
        """
        data = {
            "title": title,
            "url": url,
            "description": description,
            "tags": tags or [],
        }

        doc = self._make_request("/docs", "POST", data)
        
        # 发送刷新通知
        try:
            self.notify_refresh()
        except ApiError as e:
            print(f"警告: 发送刷新通知失败 - {str(e)}")
        
        return doc

    def get_docs(self) -> List[Dict]:
        """
        获取所有文档

        Returns:
            List[Dict]: 文档列表
        """
        return self._make_request("/docs")

    def search_docs(self, query: str) -> List[Dict]:
        """
        搜索文档

        Args:
            query: 搜索关键词

        Returns:
            List[Dict]: 搜索结果
        """
        return self._make_request(f"/docs/search?q={query}")

    def notify_refresh(self) -> Dict:
        """
        发送刷新通知

        Returns:
            Dict: 通知结果
        """
        return self._make_request("/notify/refresh", "POST")


class ApiError(Exception):
    """API 错误"""
    pass
