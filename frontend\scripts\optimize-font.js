import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import Fontmin from 'fontmin';
import chalk from 'chalk';
import ora from 'ora';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const FONTS_DIR = path.join(__dirname, '../src/assets/fonts');
const TEMP_DIR = path.join(__dirname, '../.temp/fonts');

// 日志工具
const log = {
    info: (msg) => console.log(chalk.blue('ℹ'), chalk.blue(msg)),
    success: (msg) => console.log(chalk.green('✓'), chalk.green(msg)),
    warn: (msg) => console.log(chalk.yellow('⚠'), chalk.yellow(msg)),
    error: (msg) => console.log(chalk.red('✖'), chalk.red(msg))
};

// 确保目录存在
async function ensureDir(dir) {
    await fs.mkdir(dir, { recursive: true });
}

// 获取文件大小的可读字符串
function formatFileSize(bytes) {
    const units = ['B', 'KB', 'MB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
}

// 优化字体文件
async function optimizeFont(inputFile) {
    const fontName = path.basename(inputFile);
    const outputFile = path.join(TEMP_DIR, fontName);
    
    // 创建临时目录
    await ensureDir(TEMP_DIR);
    
    const spinner = ora({
        text: `Optimizing ${fontName}...`,
        spinner: 'dots'
    }).start();
    
    return new Promise((resolve, reject) => {
        // 配置 fontmin
        const fontmin = new Fontmin()
            .src(inputFile)
            .dest(TEMP_DIR)
            // 只保留常用字符
            .use(Fontmin.glyph({
                text: `
                    abcdefghijklmnopqrstuvwxyz
                    ABCDEFGHIJKLMNOPQRSTUVWXYZ
                    0123456789
                    ,.?!@#$%^&*()_+-=[]{}|;:'"<>/\\
                    中文示例文本：欢迎使用腾讯字体
                    这是一段用于测试的中文文本
                    包含常用汉字和标点符号
                `.replace(/\s+/g, '')
            }));

        fontmin.run(async (err, files) => {
            if (err) {
                spinner.fail(`Failed to optimize ${fontName}: ${err.message}`);
                reject(err);
                return;
            }

            try {
                // 获取原始文件大小
                const originalStats = await fs.stat(inputFile);
                const originalSize = originalStats.size;

                // 获取优化后的文件大小
                const optimizedStats = await fs.stat(outputFile);
                const optimizedSize = optimizedStats.size;

                // 计算优化比例
                const reduction = ((originalSize - optimizedSize) / originalSize * 100).toFixed(2);

                // 复制优化后的文件回原目录
                await fs.copyFile(outputFile, inputFile);

                // 清理临时文件
                await fs.rm(TEMP_DIR, { recursive: true, force: true });

                spinner.succeed(
                    `Optimized ${fontName}: ` +
                    `${formatFileSize(originalSize)} → ${formatFileSize(optimizedSize)} ` +
                    `(${reduction}% reduction)`
                );

                resolve(true);
            } catch (error) {
                spinner.fail(`Error processing ${fontName}: ${error.message}`);
                reject(error);
            }
        });
    });
}

// 优化所有字体文件
async function optimizeAllFonts() {
    // 确保字体目录存在
    await ensureDir(FONTS_DIR);

    // 获取所有字体文件
    const files = await fs.readdir(FONTS_DIR);
    const fontFiles = files.filter(file => /\.(otf|ttf|woff2?)$/i.test(file))
                         .map(file => path.join(FONTS_DIR, file));

    if (fontFiles.length === 0) {
        log.warn('No font files found in fonts directory');
        return;
    }

    log.info(`Found ${fontFiles.length} font file(s) to optimize`);

    try {
        await Promise.all(fontFiles.map(optimizeFont));
        log.success('Font optimization completed');
    } catch (error) {
        log.error(`Font optimization failed: ${error.message}`);
        process.exit(1);
    }
}

// 主函数
async function main() {
    try {
        if (process.argv[2]) {
            await optimizeFont(process.argv[2]);
        } else {
            await optimizeAllFonts();
        }
    } catch (error) {
        log.error(`Unexpected error: ${error.message}`);
        process.exit(1);
    }
}

main();
