{"version": 3, "sources": ["../../@vuelidate/core/dist/index.mjs"], "sourcesContent": ["import { isReactive, isReadonly, computed, unref, ref, watch, isRef, reactive, nextTick, inject, provide, getCurrentInstance, onBeforeMount, onBeforeUnmount } from 'vue-demi';\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n\n  return target;\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction unwrapObj(obj) {\n  let ignoreKeys = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  return Object.keys(obj).reduce((o, k) => {\n    if (ignoreKeys.includes(k)) return o;\n    o[k] = unref(obj[k]);\n    return o;\n  }, {});\n}\nfunction isFunction(val) {\n  return typeof val === 'function';\n}\nfunction isProxy(value) {\n  return isReactive(value) || isReadonly(value);\n}\nfunction get(obj, stringPath, def) {\n  let current = obj;\n  const path = stringPath.split('.');\n\n  for (let i = 0; i < path.length; i++) {\n    if (!current[path[i]]) return def;\n    current = current[path[i]];\n  }\n\n  return current;\n}\nfunction gatherBooleanGroupProperties(group, nestedResults, property) {\n  return computed(() => {\n    return group.some(path => {\n      return get(nestedResults, path, {\n        [property]: false\n      })[property];\n    });\n  });\n}\nfunction gatherArrayGroupProperties(group, nestedResults, property) {\n  return computed(() => {\n    return group.reduce((all, path) => {\n      const fetchedProperty = get(nestedResults, path, {\n        [property]: false\n      })[property] || [];\n      return all.concat(fetchedProperty);\n    }, []);\n  });\n}\n\nfunction callRule(rule, value, siblingState, instance) {\n  return rule.call(instance, unref(value), unref(siblingState), instance);\n}\n\nfunction normalizeValidatorResponse(result) {\n  return result.$valid !== undefined ? !result.$valid : !result;\n}\n\nfunction createAsyncResult(rule, model, $pending, $dirty, _ref, $response, instance) {\n  let {\n    $lazy,\n    $rewardEarly\n  } = _ref;\n  let watchTargets = arguments.length > 7 && arguments[7] !== undefined ? arguments[7] : [];\n  let siblingState = arguments.length > 8 ? arguments[8] : undefined;\n  let $lastInvalidState = arguments.length > 9 ? arguments[9] : undefined;\n  let $lastCommittedOn = arguments.length > 10 ? arguments[10] : undefined;\n  const $invalid = ref(!!$dirty.value);\n  const $pendingCounter = ref(0);\n  $pending.value = false;\n  const $unwatch = watch([model, $dirty].concat(watchTargets, $lastCommittedOn), () => {\n    if ($lazy && !$dirty.value || $rewardEarly && !$lastInvalidState.value && !$pending.value) {\n      return;\n    }\n\n    let ruleResult;\n\n    try {\n      ruleResult = callRule(rule, model, siblingState, instance);\n    } catch (err) {\n      ruleResult = Promise.reject(err);\n    }\n\n    $pendingCounter.value++;\n    $pending.value = !!$pendingCounter.value;\n    $invalid.value = false;\n    Promise.resolve(ruleResult).then(data => {\n      $pendingCounter.value--;\n      $pending.value = !!$pendingCounter.value;\n      $response.value = data;\n      $invalid.value = normalizeValidatorResponse(data);\n    }).catch(error => {\n      $pendingCounter.value--;\n      $pending.value = !!$pendingCounter.value;\n      $response.value = error;\n      $invalid.value = true;\n    });\n  }, {\n    immediate: true,\n    deep: typeof model === 'object'\n  });\n  return {\n    $invalid,\n    $unwatch\n  };\n}\n\nfunction createSyncResult(rule, model, $dirty, _ref2, $response, instance, siblingState, $lastInvalidState) {\n  let {\n    $lazy,\n    $rewardEarly\n  } = _ref2;\n\n  const $unwatch = () => ({});\n\n  const $invalid = computed(() => {\n    if ($lazy && !$dirty.value || $rewardEarly && !$lastInvalidState.value) {\n      return false;\n    }\n\n    let returnValue = true;\n\n    try {\n      const result = callRule(rule, model, siblingState, instance);\n      $response.value = result;\n      returnValue = normalizeValidatorResponse(result);\n    } catch (err) {\n      $response.value = err;\n    }\n\n    return returnValue;\n  });\n  return {\n    $unwatch,\n    $invalid\n  };\n}\n\nfunction createValidatorResult(rule, model, $dirty, config, instance, validatorName, propertyKey, propertyPath, siblingState, $lastInvalidState, $lastCommittedOn) {\n  const $pending = ref(false);\n  const $params = rule.$params || {};\n  const $response = ref(null);\n  let $invalid;\n  let $unwatch;\n\n  if (rule.$async) {\n    ({\n      $invalid,\n      $unwatch\n    } = createAsyncResult(rule.$validator, model, $pending, $dirty, config, $response, instance, rule.$watchTargets, siblingState, $lastInvalidState, $lastCommittedOn));\n  } else {\n    ({\n      $invalid,\n      $unwatch\n    } = createSyncResult(rule.$validator, model, $dirty, config, $response, instance, siblingState, $lastInvalidState));\n  }\n\n  const message = rule.$message;\n  const $message = isFunction(message) ? computed(() => message(unwrapObj({\n    $pending,\n    $invalid,\n    $params: unwrapObj($params),\n    $model: model,\n    $response,\n    $validator: validatorName,\n    $propertyPath: propertyPath,\n    $property: propertyKey\n  }))) : message || '';\n  return {\n    $message,\n    $params,\n    $pending,\n    $invalid,\n    $response,\n    $unwatch\n  };\n}\n\nfunction sortValidations() {\n  let validationsRaw = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const validations = unref(validationsRaw);\n  const validationKeys = Object.keys(validations);\n  const rules = {};\n  const nestedValidators = {};\n  const config = {};\n  let validationGroups = null;\n  validationKeys.forEach(key => {\n    const v = validations[key];\n\n    switch (true) {\n      case isFunction(v.$validator):\n        rules[key] = v;\n        break;\n\n      case isFunction(v):\n        rules[key] = {\n          $validator: v\n        };\n        break;\n\n      case key === '$validationGroups':\n        validationGroups = v;\n        break;\n\n      case key.startsWith('$'):\n        config[key] = v;\n        break;\n\n      default:\n        nestedValidators[key] = v;\n    }\n  });\n  return {\n    rules,\n    nestedValidators,\n    config,\n    validationGroups\n  };\n}\n\nconst ROOT_PATH = '__root';\n\nfunction createValidationResults(rules, model, key, resultsCache, path, config, instance, externalResults, siblingState) {\n  const ruleKeys = Object.keys(rules);\n  const cachedResult = resultsCache.get(path, rules);\n  const $dirty = ref(false);\n  const $lastInvalidState = ref(false);\n  const $lastCommittedOn = ref(0);\n\n  if (cachedResult) {\n    if (!cachedResult.$partial) return cachedResult;\n    cachedResult.$unwatch();\n    $dirty.value = cachedResult.$dirty.value;\n  }\n\n  const result = {\n    $dirty,\n    $path: path,\n    $touch: () => {\n      if (!$dirty.value) $dirty.value = true;\n    },\n    $reset: () => {\n      if ($dirty.value) $dirty.value = false;\n    },\n    $commit: () => {}\n  };\n\n  if (!ruleKeys.length) {\n    cachedResult && resultsCache.set(path, rules, result);\n    return result;\n  }\n\n  ruleKeys.forEach(ruleKey => {\n    result[ruleKey] = createValidatorResult(rules[ruleKey], model, result.$dirty, config, instance, ruleKey, key, path, siblingState, $lastInvalidState, $lastCommittedOn);\n  });\n  result.$externalResults = computed(() => {\n    if (!externalResults.value) return [];\n    return [].concat(externalResults.value).map((stringError, index) => ({\n      $propertyPath: path,\n      $property: key,\n      $validator: '$externalResults',\n      $uid: `${path}-externalResult-${index}`,\n      $message: stringError,\n      $params: {},\n      $response: null,\n      $pending: false\n    }));\n  });\n  result.$invalid = computed(() => {\n    const r = ruleKeys.some(ruleKey => unref(result[ruleKey].$invalid));\n    $lastInvalidState.value = r;\n    return !!result.$externalResults.value.length || r;\n  });\n  result.$pending = computed(() => ruleKeys.some(ruleKey => unref(result[ruleKey].$pending)));\n  result.$error = computed(() => result.$dirty.value ? result.$pending.value || result.$invalid.value : false);\n  result.$silentErrors = computed(() => ruleKeys.filter(ruleKey => unref(result[ruleKey].$invalid)).map(ruleKey => {\n    const res = result[ruleKey];\n    return reactive({\n      $propertyPath: path,\n      $property: key,\n      $validator: ruleKey,\n      $uid: `${path}-${ruleKey}`,\n      $message: res.$message,\n      $params: res.$params,\n      $response: res.$response,\n      $pending: res.$pending\n    });\n  }).concat(result.$externalResults.value));\n  result.$errors = computed(() => result.$dirty.value ? result.$silentErrors.value : []);\n\n  result.$unwatch = () => ruleKeys.forEach(ruleKey => {\n    result[ruleKey].$unwatch();\n  });\n\n  result.$commit = () => {\n    $lastInvalidState.value = true;\n    $lastCommittedOn.value = Date.now();\n  };\n\n  resultsCache.set(path, rules, result);\n  return result;\n}\n\nfunction collectNestedValidationResults(validations, nestedState, path, resultsCache, config, instance, nestedExternalResults) {\n  const nestedValidationKeys = Object.keys(validations);\n  if (!nestedValidationKeys.length) return {};\n  return nestedValidationKeys.reduce((results, nestedKey) => {\n    results[nestedKey] = setValidations({\n      validations: validations[nestedKey],\n      state: nestedState,\n      key: nestedKey,\n      parentKey: path,\n      resultsCache,\n      globalConfig: config,\n      instance,\n      externalResults: nestedExternalResults\n    });\n    return results;\n  }, {});\n}\n\nfunction createMetaFields(results, nestedResults, childResults) {\n  const allResults = computed(() => [nestedResults, childResults].filter(res => res).reduce((allRes, res) => {\n    return allRes.concat(Object.values(unref(res)));\n  }, []));\n  const $dirty = computed({\n    get() {\n      return results.$dirty.value || (allResults.value.length ? allResults.value.every(r => r.$dirty) : false);\n    },\n\n    set(v) {\n      results.$dirty.value = v;\n    }\n\n  });\n  const $silentErrors = computed(() => {\n    const modelErrors = unref(results.$silentErrors) || [];\n    const nestedErrors = allResults.value.filter(result => (unref(result).$silentErrors || []).length).reduce((errors, result) => {\n      return errors.concat(...result.$silentErrors);\n    }, []);\n    return modelErrors.concat(nestedErrors);\n  });\n  const $errors = computed(() => {\n    const modelErrors = unref(results.$errors) || [];\n    const nestedErrors = allResults.value.filter(result => (unref(result).$errors || []).length).reduce((errors, result) => {\n      return errors.concat(...result.$errors);\n    }, []);\n    return modelErrors.concat(nestedErrors);\n  });\n  const $invalid = computed(() => allResults.value.some(r => r.$invalid) || unref(results.$invalid) || false);\n  const $pending = computed(() => allResults.value.some(r => unref(r.$pending)) || unref(results.$pending) || false);\n  const $anyDirty = computed(() => allResults.value.some(r => r.$dirty) || allResults.value.some(r => r.$anyDirty) || $dirty.value);\n  const $error = computed(() => $dirty.value ? $pending.value || $invalid.value : false);\n\n  const $touch = () => {\n    results.$touch();\n    allResults.value.forEach(result => {\n      result.$touch();\n    });\n  };\n\n  const $commit = () => {\n    results.$commit();\n    allResults.value.forEach(result => {\n      result.$commit();\n    });\n  };\n\n  const $reset = () => {\n    results.$reset();\n    allResults.value.forEach(result => {\n      result.$reset();\n    });\n  };\n\n  if (allResults.value.length && allResults.value.every(nr => nr.$dirty)) $touch();\n  return {\n    $dirty,\n    $errors,\n    $invalid,\n    $anyDirty,\n    $error,\n    $pending,\n    $touch,\n    $reset,\n    $silentErrors,\n    $commit\n  };\n}\n\nfunction setValidations(_ref) {\n  let {\n    validations,\n    state,\n    key,\n    parentKey,\n    childResults,\n    resultsCache,\n    globalConfig = {},\n    instance,\n    externalResults\n  } = _ref;\n  const path = parentKey ? `${parentKey}.${key}` : key;\n  const {\n    rules,\n    nestedValidators,\n    config,\n    validationGroups\n  } = sortValidations(validations);\n\n  const mergedConfig = _objectSpread2(_objectSpread2({}, globalConfig), config);\n\n  const nestedState = key ? computed(() => {\n    const s = unref(state);\n    return s ? unref(s[key]) : undefined;\n  }) : state;\n\n  const cachedExternalResults = _objectSpread2({}, unref(externalResults) || {});\n\n  const nestedExternalResults = computed(() => {\n    const results = unref(externalResults);\n    if (!key) return results;\n    return results ? unref(results[key]) : undefined;\n  });\n  const results = createValidationResults(rules, nestedState, key, resultsCache, path, mergedConfig, instance, nestedExternalResults, state);\n  const nestedResults = collectNestedValidationResults(nestedValidators, nestedState, path, resultsCache, mergedConfig, instance, nestedExternalResults);\n  const $validationGroups = {};\n\n  if (validationGroups) {\n    Object.entries(validationGroups).forEach(_ref2 => {\n      let [key, group] = _ref2;\n      $validationGroups[key] = {\n        $invalid: gatherBooleanGroupProperties(group, nestedResults, '$invalid'),\n        $error: gatherBooleanGroupProperties(group, nestedResults, '$error'),\n        $pending: gatherBooleanGroupProperties(group, nestedResults, '$pending'),\n        $errors: gatherArrayGroupProperties(group, nestedResults, '$errors'),\n        $silentErrors: gatherArrayGroupProperties(group, nestedResults, '$silentErrors')\n      };\n    });\n  }\n\n  const {\n    $dirty,\n    $errors,\n    $invalid,\n    $anyDirty,\n    $error,\n    $pending,\n    $touch,\n    $reset,\n    $silentErrors,\n    $commit\n  } = createMetaFields(results, nestedResults, childResults);\n  const $model = key ? computed({\n    get: () => unref(nestedState),\n    set: val => {\n      $dirty.value = true;\n      const s = unref(state);\n      const external = unref(externalResults);\n\n      if (external) {\n        external[key] = cachedExternalResults[key];\n      }\n\n      if (isRef(s[key])) {\n        s[key].value = val;\n      } else {\n        s[key] = val;\n      }\n    }\n  }) : null;\n\n  if (key && mergedConfig.$autoDirty) {\n    watch(nestedState, () => {\n      if (!$dirty.value) $touch();\n      const external = unref(externalResults);\n\n      if (external) {\n        external[key] = cachedExternalResults[key];\n      }\n    }, {\n      flush: 'sync'\n    });\n  }\n\n  async function $validate() {\n    $touch();\n\n    if (mergedConfig.$rewardEarly) {\n      $commit();\n      await nextTick();\n    }\n\n    await nextTick();\n    return new Promise(resolve => {\n      if (!$pending.value) return resolve(!$invalid.value);\n      const unwatch = watch($pending, () => {\n        resolve(!$invalid.value);\n        unwatch();\n      });\n    });\n  }\n\n  function $getResultsForChild(key) {\n    return (childResults.value || {})[key];\n  }\n\n  function $clearExternalResults() {\n    if (isRef(externalResults)) {\n      externalResults.value = cachedExternalResults;\n    } else {\n      if (Object.keys(cachedExternalResults).length === 0) {\n        Object.keys(externalResults).forEach(k => {\n          delete externalResults[k];\n        });\n      } else {\n        Object.assign(externalResults, cachedExternalResults);\n      }\n    }\n  }\n\n  return reactive(_objectSpread2(_objectSpread2(_objectSpread2({}, results), {}, {\n    $model,\n    $dirty,\n    $error,\n    $errors,\n    $invalid,\n    $anyDirty,\n    $pending,\n    $touch,\n    $reset,\n    $path: path || ROOT_PATH,\n    $silentErrors,\n    $validate,\n    $commit\n  }, childResults && {\n    $getResultsForChild,\n    $clearExternalResults,\n    $validationGroups\n  }), nestedResults));\n}\n\nclass ResultsStorage {\n  constructor() {\n    this.storage = new Map();\n  }\n\n  set(path, rules, result) {\n    this.storage.set(path, {\n      rules,\n      result\n    });\n  }\n\n  checkRulesValidity(path, rules, storedRules) {\n    const storedRulesKeys = Object.keys(storedRules);\n    const newRulesKeys = Object.keys(rules);\n    if (newRulesKeys.length !== storedRulesKeys.length) return false;\n    const hasAllValidators = newRulesKeys.every(ruleKey => storedRulesKeys.includes(ruleKey));\n    if (!hasAllValidators) return false;\n    return newRulesKeys.every(ruleKey => {\n      if (!rules[ruleKey].$params) return true;\n      return Object.keys(rules[ruleKey].$params).every(paramKey => {\n        return unref(storedRules[ruleKey].$params[paramKey]) === unref(rules[ruleKey].$params[paramKey]);\n      });\n    });\n  }\n\n  get(path, rules) {\n    const storedRuleResultPair = this.storage.get(path);\n    if (!storedRuleResultPair) return undefined;\n    const {\n      rules: storedRules,\n      result\n    } = storedRuleResultPair;\n    const isValidCache = this.checkRulesValidity(path, rules, storedRules);\n    const $unwatch = result.$unwatch ? result.$unwatch : () => ({});\n    if (!isValidCache) return {\n      $dirty: result.$dirty,\n      $partial: true,\n      $unwatch\n    };\n    return result;\n  }\n\n}\n\nconst CollectFlag = {\n  COLLECT_ALL: true,\n  COLLECT_NONE: false\n};\nconst VuelidateInjectChildResults = Symbol('vuelidate#injectChildResults');\nconst VuelidateRemoveChildResults = Symbol('vuelidate#removeChildResults');\nfunction nestedValidations(_ref) {\n  let {\n    $scope,\n    instance\n  } = _ref;\n  const childResultsRaw = {};\n  const childResultsKeys = ref([]);\n  const childResults = computed(() => childResultsKeys.value.reduce((results, key) => {\n    results[key] = unref(childResultsRaw[key]);\n    return results;\n  }, {}));\n\n  function injectChildResultsIntoParent(results, _ref2) {\n    let {\n      $registerAs: key,\n      $scope: childScope,\n      $stopPropagation\n    } = _ref2;\n    if ($stopPropagation || $scope === CollectFlag.COLLECT_NONE || childScope === CollectFlag.COLLECT_NONE || $scope !== CollectFlag.COLLECT_ALL && $scope !== childScope) return;\n    childResultsRaw[key] = results;\n    childResultsKeys.value.push(key);\n  }\n\n  instance.__vuelidateInjectInstances = [].concat(instance.__vuelidateInjectInstances || [], injectChildResultsIntoParent);\n\n  function removeChildResultsFromParent(key) {\n    childResultsKeys.value = childResultsKeys.value.filter(childKey => childKey !== key);\n    delete childResultsRaw[key];\n  }\n\n  instance.__vuelidateRemoveInstances = [].concat(instance.__vuelidateRemoveInstances || [], removeChildResultsFromParent);\n  const sendValidationResultsToParent = inject(VuelidateInjectChildResults, []);\n  provide(VuelidateInjectChildResults, instance.__vuelidateInjectInstances);\n  const removeValidationResultsFromParent = inject(VuelidateRemoveChildResults, []);\n  provide(VuelidateRemoveChildResults, instance.__vuelidateRemoveInstances);\n  return {\n    childResults,\n    sendValidationResultsToParent,\n    removeValidationResultsFromParent\n  };\n}\n\nfunction ComputedProxyFactory(target) {\n  return new Proxy(target, {\n    get(target, prop) {\n      return typeof target[prop] === 'object' ? ComputedProxyFactory(target[prop]) : computed(() => target[prop]);\n    }\n\n  });\n}\n\nlet uid = 0;\nfunction useVuelidate(validations, state) {\n  var _getCurrentInstance;\n\n  let globalConfig = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n\n  if (arguments.length === 1) {\n    globalConfig = validations;\n    validations = undefined;\n    state = undefined;\n  }\n\n  let {\n    $registerAs,\n    $scope = CollectFlag.COLLECT_ALL,\n    $stopPropagation,\n    $externalResults,\n    currentVueInstance\n  } = globalConfig;\n  const instance = currentVueInstance || ((_getCurrentInstance = getCurrentInstance()) === null || _getCurrentInstance === void 0 ? void 0 : _getCurrentInstance.proxy);\n  const componentOptions = instance ? instance.$options : {};\n\n  if (!$registerAs) {\n    uid += 1;\n    $registerAs = `_vuelidate_${uid}`;\n  }\n\n  const validationResults = ref({});\n  const resultsCache = new ResultsStorage();\n  const {\n    childResults,\n    sendValidationResultsToParent,\n    removeValidationResultsFromParent\n  } = instance ? nestedValidations({\n    $scope,\n    instance\n  }) : {\n    childResults: ref({})\n  };\n\n  if (!validations && componentOptions.validations) {\n    const rules = componentOptions.validations;\n    state = ref({});\n    onBeforeMount(() => {\n      state.value = instance;\n      watch(() => isFunction(rules) ? rules.call(state.value, new ComputedProxyFactory(state.value)) : rules, validations => {\n        validationResults.value = setValidations({\n          validations,\n          state,\n          childResults,\n          resultsCache,\n          globalConfig,\n          instance,\n          externalResults: $externalResults || instance.vuelidateExternalResults\n        });\n      }, {\n        immediate: true\n      });\n    });\n    globalConfig = componentOptions.validationsConfig || globalConfig;\n  } else {\n    const validationsWatchTarget = isRef(validations) || isProxy(validations) ? validations : reactive(validations || {});\n    watch(validationsWatchTarget, newValidationRules => {\n      validationResults.value = setValidations({\n        validations: newValidationRules,\n        state,\n        childResults,\n        resultsCache,\n        globalConfig,\n        instance: instance !== null && instance !== void 0 ? instance : {},\n        externalResults: $externalResults\n      });\n    }, {\n      immediate: true\n    });\n  }\n\n  if (instance) {\n    sendValidationResultsToParent.forEach(f => f(validationResults, {\n      $registerAs,\n      $scope,\n      $stopPropagation\n    }));\n    onBeforeUnmount(() => removeValidationResultsFromParent.forEach(f => f($registerAs)));\n  }\n\n  return computed(() => {\n    return _objectSpread2(_objectSpread2({}, unref(validationResults.value)), childResults.value);\n  });\n}\n\nexport { CollectFlag, useVuelidate as default, useVuelidate };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAEA,SAAS,QAAQ,QAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAK,MAAM;AAE7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsB,MAAM;AACjD,uBAAmB,UAAU,QAAQ,OAAO,SAAU,KAAK;AACzD,aAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,IACtD,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EACpC;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AACpD,QAAI,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAE,EAAE,QAAQ,SAAU,KAAK;AACzD,sBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,IAC1C,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC,IAAI,QAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AACjK,aAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,IACjF,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAEA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AAEA,SAAO;AACT;AAEA,SAAS,UAAU,KAAK;AACtB,MAAI,aAAa,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACtF,SAAO,OAAO,KAAK,GAAG,EAAE,OAAO,CAAC,GAAG,MAAM;AACvC,QAAI,WAAW,SAAS,CAAC,EAAG,QAAO;AACnC,MAAE,CAAC,IAAI,MAAM,IAAI,CAAC,CAAC;AACnB,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AACA,SAAS,WAAW,KAAK;AACvB,SAAO,OAAO,QAAQ;AACxB;AACA,SAAS,QAAQ,OAAO;AACtB,SAAO,WAAW,KAAK,KAAK,WAAW,KAAK;AAC9C;AACA,SAAS,IAAI,KAAK,YAAY,KAAK;AACjC,MAAI,UAAU;AACd,QAAM,OAAO,WAAW,MAAM,GAAG;AAEjC,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,QAAI,CAAC,QAAQ,KAAK,CAAC,CAAC,EAAG,QAAO;AAC9B,cAAU,QAAQ,KAAK,CAAC,CAAC;AAAA,EAC3B;AAEA,SAAO;AACT;AACA,SAAS,6BAA6B,OAAO,eAAe,UAAU;AACpE,SAAO,SAAS,MAAM;AACpB,WAAO,MAAM,KAAK,UAAQ;AACxB,aAAO,IAAI,eAAe,MAAM;AAAA,QAC9B,CAAC,QAAQ,GAAG;AAAA,MACd,CAAC,EAAE,QAAQ;AAAA,IACb,CAAC;AAAA,EACH,CAAC;AACH;AACA,SAAS,2BAA2B,OAAO,eAAe,UAAU;AAClE,SAAO,SAAS,MAAM;AACpB,WAAO,MAAM,OAAO,CAAC,KAAK,SAAS;AACjC,YAAM,kBAAkB,IAAI,eAAe,MAAM;AAAA,QAC/C,CAAC,QAAQ,GAAG;AAAA,MACd,CAAC,EAAE,QAAQ,KAAK,CAAC;AACjB,aAAO,IAAI,OAAO,eAAe;AAAA,IACnC,GAAG,CAAC,CAAC;AAAA,EACP,CAAC;AACH;AAEA,SAAS,SAAS,MAAM,OAAO,cAAc,UAAU;AACrD,SAAO,KAAK,KAAK,UAAU,MAAM,KAAK,GAAG,MAAM,YAAY,GAAG,QAAQ;AACxE;AAEA,SAAS,2BAA2B,QAAQ;AAC1C,SAAO,OAAO,WAAW,SAAY,CAAC,OAAO,SAAS,CAAC;AACzD;AAEA,SAAS,kBAAkB,MAAM,OAAO,UAAU,QAAQ,MAAM,WAAW,UAAU;AACnF,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,eAAe,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACxF,MAAI,eAAe,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AACzD,MAAI,oBAAoB,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AAC9D,MAAI,mBAAmB,UAAU,SAAS,KAAK,UAAU,EAAE,IAAI;AAC/D,QAAM,WAAW,IAAI,CAAC,CAAC,OAAO,KAAK;AACnC,QAAM,kBAAkB,IAAI,CAAC;AAC7B,WAAS,QAAQ;AACjB,QAAM,WAAW,MAAM,CAAC,OAAO,MAAM,EAAE,OAAO,cAAc,gBAAgB,GAAG,MAAM;AACnF,QAAI,SAAS,CAAC,OAAO,SAAS,gBAAgB,CAAC,kBAAkB,SAAS,CAAC,SAAS,OAAO;AACzF;AAAA,IACF;AAEA,QAAI;AAEJ,QAAI;AACF,mBAAa,SAAS,MAAM,OAAO,cAAc,QAAQ;AAAA,IAC3D,SAAS,KAAK;AACZ,mBAAa,QAAQ,OAAO,GAAG;AAAA,IACjC;AAEA,oBAAgB;AAChB,aAAS,QAAQ,CAAC,CAAC,gBAAgB;AACnC,aAAS,QAAQ;AACjB,YAAQ,QAAQ,UAAU,EAAE,KAAK,UAAQ;AACvC,sBAAgB;AAChB,eAAS,QAAQ,CAAC,CAAC,gBAAgB;AACnC,gBAAU,QAAQ;AAClB,eAAS,QAAQ,2BAA2B,IAAI;AAAA,IAClD,CAAC,EAAE,MAAM,WAAS;AAChB,sBAAgB;AAChB,eAAS,QAAQ,CAAC,CAAC,gBAAgB;AACnC,gBAAU,QAAQ;AAClB,eAAS,QAAQ;AAAA,IACnB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,WAAW;AAAA,IACX,MAAM,OAAO,UAAU;AAAA,EACzB,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,iBAAiB,MAAM,OAAO,QAAQ,OAAO,WAAW,UAAU,cAAc,mBAAmB;AAC1G,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,QAAM,WAAW,OAAO,CAAC;AAEzB,QAAM,WAAW,SAAS,MAAM;AAC9B,QAAI,SAAS,CAAC,OAAO,SAAS,gBAAgB,CAAC,kBAAkB,OAAO;AACtE,aAAO;AAAA,IACT;AAEA,QAAI,cAAc;AAElB,QAAI;AACF,YAAM,SAAS,SAAS,MAAM,OAAO,cAAc,QAAQ;AAC3D,gBAAU,QAAQ;AAClB,oBAAc,2BAA2B,MAAM;AAAA,IACjD,SAAS,KAAK;AACZ,gBAAU,QAAQ;AAAA,IACpB;AAEA,WAAO;AAAA,EACT,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,sBAAsB,MAAM,OAAO,QAAQ,QAAQ,UAAU,eAAe,aAAa,cAAc,cAAc,mBAAmB,kBAAkB;AACjK,QAAM,WAAW,IAAI,KAAK;AAC1B,QAAM,UAAU,KAAK,WAAW,CAAC;AACjC,QAAM,YAAY,IAAI,IAAI;AAC1B,MAAI;AACJ,MAAI;AAEJ,MAAI,KAAK,QAAQ;AACf,KAAC;AAAA,MACC;AAAA,MACA;AAAA,IACF,IAAI,kBAAkB,KAAK,YAAY,OAAO,UAAU,QAAQ,QAAQ,WAAW,UAAU,KAAK,eAAe,cAAc,mBAAmB,gBAAgB;AAAA,EACpK,OAAO;AACL,KAAC;AAAA,MACC;AAAA,MACA;AAAA,IACF,IAAI,iBAAiB,KAAK,YAAY,OAAO,QAAQ,QAAQ,WAAW,UAAU,cAAc,iBAAiB;AAAA,EACnH;AAEA,QAAM,UAAU,KAAK;AACrB,QAAM,WAAW,WAAW,OAAO,IAAI,SAAS,MAAM,QAAQ,UAAU;AAAA,IACtE;AAAA,IACA;AAAA,IACA,SAAS,UAAU,OAAO;AAAA,IAC1B,QAAQ;AAAA,IACR;AAAA,IACA,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,WAAW;AAAA,EACb,CAAC,CAAC,CAAC,IAAI,WAAW;AAClB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB;AACzB,MAAI,iBAAiB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAC1F,QAAM,cAAc,MAAM,cAAc;AACxC,QAAM,iBAAiB,OAAO,KAAK,WAAW;AAC9C,QAAM,QAAQ,CAAC;AACf,QAAM,mBAAmB,CAAC;AAC1B,QAAM,SAAS,CAAC;AAChB,MAAI,mBAAmB;AACvB,iBAAe,QAAQ,SAAO;AAC5B,UAAM,IAAI,YAAY,GAAG;AAEzB,YAAQ,MAAM;AAAA,MACZ,KAAK,WAAW,EAAE,UAAU;AAC1B,cAAM,GAAG,IAAI;AACb;AAAA,MAEF,KAAK,WAAW,CAAC;AACf,cAAM,GAAG,IAAI;AAAA,UACX,YAAY;AAAA,QACd;AACA;AAAA,MAEF,KAAK,QAAQ;AACX,2BAAmB;AACnB;AAAA,MAEF,KAAK,IAAI,WAAW,GAAG;AACrB,eAAO,GAAG,IAAI;AACd;AAAA,MAEF;AACE,yBAAiB,GAAG,IAAI;AAAA,IAC5B;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,YAAY;AAElB,SAAS,wBAAwB,OAAO,OAAO,KAAK,cAAc,MAAM,QAAQ,UAAU,iBAAiB,cAAc;AACvH,QAAM,WAAW,OAAO,KAAK,KAAK;AAClC,QAAM,eAAe,aAAa,IAAI,MAAM,KAAK;AACjD,QAAM,SAAS,IAAI,KAAK;AACxB,QAAM,oBAAoB,IAAI,KAAK;AACnC,QAAM,mBAAmB,IAAI,CAAC;AAE9B,MAAI,cAAc;AAChB,QAAI,CAAC,aAAa,SAAU,QAAO;AACnC,iBAAa,SAAS;AACtB,WAAO,QAAQ,aAAa,OAAO;AAAA,EACrC;AAEA,QAAM,SAAS;AAAA,IACb;AAAA,IACA,OAAO;AAAA,IACP,QAAQ,MAAM;AACZ,UAAI,CAAC,OAAO,MAAO,QAAO,QAAQ;AAAA,IACpC;AAAA,IACA,QAAQ,MAAM;AACZ,UAAI,OAAO,MAAO,QAAO,QAAQ;AAAA,IACnC;AAAA,IACA,SAAS,MAAM;AAAA,IAAC;AAAA,EAClB;AAEA,MAAI,CAAC,SAAS,QAAQ;AACpB,oBAAgB,aAAa,IAAI,MAAM,OAAO,MAAM;AACpD,WAAO;AAAA,EACT;AAEA,WAAS,QAAQ,aAAW;AAC1B,WAAO,OAAO,IAAI,sBAAsB,MAAM,OAAO,GAAG,OAAO,OAAO,QAAQ,QAAQ,UAAU,SAAS,KAAK,MAAM,cAAc,mBAAmB,gBAAgB;AAAA,EACvK,CAAC;AACD,SAAO,mBAAmB,SAAS,MAAM;AACvC,QAAI,CAAC,gBAAgB,MAAO,QAAO,CAAC;AACpC,WAAO,CAAC,EAAE,OAAO,gBAAgB,KAAK,EAAE,IAAI,CAAC,aAAa,WAAW;AAAA,MACnE,eAAe;AAAA,MACf,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,MAAM,GAAG,IAAI,mBAAmB,KAAK;AAAA,MACrC,UAAU;AAAA,MACV,SAAS,CAAC;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,IACZ,EAAE;AAAA,EACJ,CAAC;AACD,SAAO,WAAW,SAAS,MAAM;AAC/B,UAAM,IAAI,SAAS,KAAK,aAAW,MAAM,OAAO,OAAO,EAAE,QAAQ,CAAC;AAClE,sBAAkB,QAAQ;AAC1B,WAAO,CAAC,CAAC,OAAO,iBAAiB,MAAM,UAAU;AAAA,EACnD,CAAC;AACD,SAAO,WAAW,SAAS,MAAM,SAAS,KAAK,aAAW,MAAM,OAAO,OAAO,EAAE,QAAQ,CAAC,CAAC;AAC1F,SAAO,SAAS,SAAS,MAAM,OAAO,OAAO,QAAQ,OAAO,SAAS,SAAS,OAAO,SAAS,QAAQ,KAAK;AAC3G,SAAO,gBAAgB,SAAS,MAAM,SAAS,OAAO,aAAW,MAAM,OAAO,OAAO,EAAE,QAAQ,CAAC,EAAE,IAAI,aAAW;AAC/G,UAAM,MAAM,OAAO,OAAO;AAC1B,WAAO,SAAS;AAAA,MACd,eAAe;AAAA,MACf,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,MAAM,GAAG,IAAI,IAAI,OAAO;AAAA,MACxB,UAAU,IAAI;AAAA,MACd,SAAS,IAAI;AAAA,MACb,WAAW,IAAI;AAAA,MACf,UAAU,IAAI;AAAA,IAChB,CAAC;AAAA,EACH,CAAC,EAAE,OAAO,OAAO,iBAAiB,KAAK,CAAC;AACxC,SAAO,UAAU,SAAS,MAAM,OAAO,OAAO,QAAQ,OAAO,cAAc,QAAQ,CAAC,CAAC;AAErF,SAAO,WAAW,MAAM,SAAS,QAAQ,aAAW;AAClD,WAAO,OAAO,EAAE,SAAS;AAAA,EAC3B,CAAC;AAED,SAAO,UAAU,MAAM;AACrB,sBAAkB,QAAQ;AAC1B,qBAAiB,QAAQ,KAAK,IAAI;AAAA,EACpC;AAEA,eAAa,IAAI,MAAM,OAAO,MAAM;AACpC,SAAO;AACT;AAEA,SAAS,+BAA+B,aAAa,aAAa,MAAM,cAAc,QAAQ,UAAU,uBAAuB;AAC7H,QAAM,uBAAuB,OAAO,KAAK,WAAW;AACpD,MAAI,CAAC,qBAAqB,OAAQ,QAAO,CAAC;AAC1C,SAAO,qBAAqB,OAAO,CAAC,SAAS,cAAc;AACzD,YAAQ,SAAS,IAAI,eAAe;AAAA,MAClC,aAAa,YAAY,SAAS;AAAA,MAClC,OAAO;AAAA,MACP,KAAK;AAAA,MACL,WAAW;AAAA,MACX;AAAA,MACA,cAAc;AAAA,MACd;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAEA,SAAS,iBAAiB,SAAS,eAAe,cAAc;AAC9D,QAAM,aAAa,SAAS,MAAM,CAAC,eAAe,YAAY,EAAE,OAAO,SAAO,GAAG,EAAE,OAAO,CAAC,QAAQ,QAAQ;AACzG,WAAO,OAAO,OAAO,OAAO,OAAO,MAAM,GAAG,CAAC,CAAC;AAAA,EAChD,GAAG,CAAC,CAAC,CAAC;AACN,QAAM,SAAS,SAAS;AAAA,IACtB,MAAM;AACJ,aAAO,QAAQ,OAAO,UAAU,WAAW,MAAM,SAAS,WAAW,MAAM,MAAM,OAAK,EAAE,MAAM,IAAI;AAAA,IACpG;AAAA,IAEA,IAAI,GAAG;AACL,cAAQ,OAAO,QAAQ;AAAA,IACzB;AAAA,EAEF,CAAC;AACD,QAAM,gBAAgB,SAAS,MAAM;AACnC,UAAM,cAAc,MAAM,QAAQ,aAAa,KAAK,CAAC;AACrD,UAAM,eAAe,WAAW,MAAM,OAAO,aAAW,MAAM,MAAM,EAAE,iBAAiB,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,QAAQ,WAAW;AAC5H,aAAO,OAAO,OAAO,GAAG,OAAO,aAAa;AAAA,IAC9C,GAAG,CAAC,CAAC;AACL,WAAO,YAAY,OAAO,YAAY;AAAA,EACxC,CAAC;AACD,QAAM,UAAU,SAAS,MAAM;AAC7B,UAAM,cAAc,MAAM,QAAQ,OAAO,KAAK,CAAC;AAC/C,UAAM,eAAe,WAAW,MAAM,OAAO,aAAW,MAAM,MAAM,EAAE,WAAW,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,QAAQ,WAAW;AACtH,aAAO,OAAO,OAAO,GAAG,OAAO,OAAO;AAAA,IACxC,GAAG,CAAC,CAAC;AACL,WAAO,YAAY,OAAO,YAAY;AAAA,EACxC,CAAC;AACD,QAAM,WAAW,SAAS,MAAM,WAAW,MAAM,KAAK,OAAK,EAAE,QAAQ,KAAK,MAAM,QAAQ,QAAQ,KAAK,KAAK;AAC1G,QAAM,WAAW,SAAS,MAAM,WAAW,MAAM,KAAK,OAAK,MAAM,EAAE,QAAQ,CAAC,KAAK,MAAM,QAAQ,QAAQ,KAAK,KAAK;AACjH,QAAM,YAAY,SAAS,MAAM,WAAW,MAAM,KAAK,OAAK,EAAE,MAAM,KAAK,WAAW,MAAM,KAAK,OAAK,EAAE,SAAS,KAAK,OAAO,KAAK;AAChI,QAAM,SAAS,SAAS,MAAM,OAAO,QAAQ,SAAS,SAAS,SAAS,QAAQ,KAAK;AAErF,QAAM,SAAS,MAAM;AACnB,YAAQ,OAAO;AACf,eAAW,MAAM,QAAQ,YAAU;AACjC,aAAO,OAAO;AAAA,IAChB,CAAC;AAAA,EACH;AAEA,QAAM,UAAU,MAAM;AACpB,YAAQ,QAAQ;AAChB,eAAW,MAAM,QAAQ,YAAU;AACjC,aAAO,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH;AAEA,QAAM,SAAS,MAAM;AACnB,YAAQ,OAAO;AACf,eAAW,MAAM,QAAQ,YAAU;AACjC,aAAO,OAAO;AAAA,IAChB,CAAC;AAAA,EACH;AAEA,MAAI,WAAW,MAAM,UAAU,WAAW,MAAM,MAAM,QAAM,GAAG,MAAM,EAAG,QAAO;AAC/E,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,eAAe,MAAM;AAC5B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,eAAe,CAAC;AAAA,IAChB;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,OAAO,YAAY,GAAG,SAAS,IAAI,GAAG,KAAK;AACjD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,gBAAgB,WAAW;AAE/B,QAAM,eAAe,eAAe,eAAe,CAAC,GAAG,YAAY,GAAG,MAAM;AAE5E,QAAM,cAAc,MAAM,SAAS,MAAM;AACvC,UAAM,IAAI,MAAM,KAAK;AACrB,WAAO,IAAI,MAAM,EAAE,GAAG,CAAC,IAAI;AAAA,EAC7B,CAAC,IAAI;AAEL,QAAM,wBAAwB,eAAe,CAAC,GAAG,MAAM,eAAe,KAAK,CAAC,CAAC;AAE7E,QAAM,wBAAwB,SAAS,MAAM;AAC3C,UAAMA,WAAU,MAAM,eAAe;AACrC,QAAI,CAAC,IAAK,QAAOA;AACjB,WAAOA,WAAU,MAAMA,SAAQ,GAAG,CAAC,IAAI;AAAA,EACzC,CAAC;AACD,QAAM,UAAU,wBAAwB,OAAO,aAAa,KAAK,cAAc,MAAM,cAAc,UAAU,uBAAuB,KAAK;AACzI,QAAM,gBAAgB,+BAA+B,kBAAkB,aAAa,MAAM,cAAc,cAAc,UAAU,qBAAqB;AACrJ,QAAM,oBAAoB,CAAC;AAE3B,MAAI,kBAAkB;AACpB,WAAO,QAAQ,gBAAgB,EAAE,QAAQ,WAAS;AAChD,UAAI,CAACC,MAAK,KAAK,IAAI;AACnB,wBAAkBA,IAAG,IAAI;AAAA,QACvB,UAAU,6BAA6B,OAAO,eAAe,UAAU;AAAA,QACvE,QAAQ,6BAA6B,OAAO,eAAe,QAAQ;AAAA,QACnE,UAAU,6BAA6B,OAAO,eAAe,UAAU;AAAA,QACvE,SAAS,2BAA2B,OAAO,eAAe,SAAS;AAAA,QACnE,eAAe,2BAA2B,OAAO,eAAe,eAAe;AAAA,MACjF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,SAAS,eAAe,YAAY;AACzD,QAAM,SAAS,MAAM,SAAS;AAAA,IAC5B,KAAK,MAAM,MAAM,WAAW;AAAA,IAC5B,KAAK,SAAO;AACV,aAAO,QAAQ;AACf,YAAM,IAAI,MAAM,KAAK;AACrB,YAAM,WAAW,MAAM,eAAe;AAEtC,UAAI,UAAU;AACZ,iBAAS,GAAG,IAAI,sBAAsB,GAAG;AAAA,MAC3C;AAEA,UAAI,MAAM,EAAE,GAAG,CAAC,GAAG;AACjB,UAAE,GAAG,EAAE,QAAQ;AAAA,MACjB,OAAO;AACL,UAAE,GAAG,IAAI;AAAA,MACX;AAAA,IACF;AAAA,EACF,CAAC,IAAI;AAEL,MAAI,OAAO,aAAa,YAAY;AAClC,UAAM,aAAa,MAAM;AACvB,UAAI,CAAC,OAAO,MAAO,QAAO;AAC1B,YAAM,WAAW,MAAM,eAAe;AAEtC,UAAI,UAAU;AACZ,iBAAS,GAAG,IAAI,sBAAsB,GAAG;AAAA,MAC3C;AAAA,IACF,GAAG;AAAA,MACD,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AAEA,iBAAe,YAAY;AACzB,WAAO;AAEP,QAAI,aAAa,cAAc;AAC7B,cAAQ;AACR,YAAM,SAAS;AAAA,IACjB;AAEA,UAAM,SAAS;AACf,WAAO,IAAI,QAAQ,aAAW;AAC5B,UAAI,CAAC,SAAS,MAAO,QAAO,QAAQ,CAAC,SAAS,KAAK;AACnD,YAAM,UAAU,MAAM,UAAU,MAAM;AACpC,gBAAQ,CAAC,SAAS,KAAK;AACvB,gBAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,WAAS,oBAAoBA,MAAK;AAChC,YAAQ,aAAa,SAAS,CAAC,GAAGA,IAAG;AAAA,EACvC;AAEA,WAAS,wBAAwB;AAC/B,QAAI,MAAM,eAAe,GAAG;AAC1B,sBAAgB,QAAQ;AAAA,IAC1B,OAAO;AACL,UAAI,OAAO,KAAK,qBAAqB,EAAE,WAAW,GAAG;AACnD,eAAO,KAAK,eAAe,EAAE,QAAQ,OAAK;AACxC,iBAAO,gBAAgB,CAAC;AAAA,QAC1B,CAAC;AAAA,MACH,OAAO;AACL,eAAO,OAAO,iBAAiB,qBAAqB;AAAA,MACtD;AAAA,IACF;AAAA,EACF;AAEA,SAAO,SAAS,eAAe,eAAe,eAAe,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG;AAAA,IAC7E;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,QAAQ;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,gBAAgB;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,GAAG,aAAa,CAAC;AACpB;AAEA,IAAM,iBAAN,MAAqB;AAAA,EACnB,cAAc;AACZ,SAAK,UAAU,oBAAI,IAAI;AAAA,EACzB;AAAA,EAEA,IAAI,MAAM,OAAO,QAAQ;AACvB,SAAK,QAAQ,IAAI,MAAM;AAAA,MACrB;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,mBAAmB,MAAM,OAAO,aAAa;AAC3C,UAAM,kBAAkB,OAAO,KAAK,WAAW;AAC/C,UAAM,eAAe,OAAO,KAAK,KAAK;AACtC,QAAI,aAAa,WAAW,gBAAgB,OAAQ,QAAO;AAC3D,UAAM,mBAAmB,aAAa,MAAM,aAAW,gBAAgB,SAAS,OAAO,CAAC;AACxF,QAAI,CAAC,iBAAkB,QAAO;AAC9B,WAAO,aAAa,MAAM,aAAW;AACnC,UAAI,CAAC,MAAM,OAAO,EAAE,QAAS,QAAO;AACpC,aAAO,OAAO,KAAK,MAAM,OAAO,EAAE,OAAO,EAAE,MAAM,cAAY;AAC3D,eAAO,MAAM,YAAY,OAAO,EAAE,QAAQ,QAAQ,CAAC,MAAM,MAAM,MAAM,OAAO,EAAE,QAAQ,QAAQ,CAAC;AAAA,MACjG,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EAEA,IAAI,MAAM,OAAO;AACf,UAAM,uBAAuB,KAAK,QAAQ,IAAI,IAAI;AAClD,QAAI,CAAC,qBAAsB,QAAO;AAClC,UAAM;AAAA,MACJ,OAAO;AAAA,MACP;AAAA,IACF,IAAI;AACJ,UAAM,eAAe,KAAK,mBAAmB,MAAM,OAAO,WAAW;AACrE,UAAM,WAAW,OAAO,WAAW,OAAO,WAAW,OAAO,CAAC;AAC7D,QAAI,CAAC,aAAc,QAAO;AAAA,MACxB,QAAQ,OAAO;AAAA,MACf,UAAU;AAAA,MACV;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEF;AAEA,IAAM,cAAc;AAAA,EAClB,aAAa;AAAA,EACb,cAAc;AAChB;AACA,IAAM,8BAA8B,OAAO,8BAA8B;AACzE,IAAM,8BAA8B,OAAO,8BAA8B;AACzE,SAAS,kBAAkB,MAAM;AAC/B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,kBAAkB,CAAC;AACzB,QAAM,mBAAmB,IAAI,CAAC,CAAC;AAC/B,QAAM,eAAe,SAAS,MAAM,iBAAiB,MAAM,OAAO,CAAC,SAAS,QAAQ;AAClF,YAAQ,GAAG,IAAI,MAAM,gBAAgB,GAAG,CAAC;AACzC,WAAO;AAAA,EACT,GAAG,CAAC,CAAC,CAAC;AAEN,WAAS,6BAA6B,SAAS,OAAO;AACpD,QAAI;AAAA,MACF,aAAa;AAAA,MACb,QAAQ;AAAA,MACR;AAAA,IACF,IAAI;AACJ,QAAI,oBAAoB,WAAW,YAAY,gBAAgB,eAAe,YAAY,gBAAgB,WAAW,YAAY,eAAe,WAAW,WAAY;AACvK,oBAAgB,GAAG,IAAI;AACvB,qBAAiB,MAAM,KAAK,GAAG;AAAA,EACjC;AAEA,WAAS,6BAA6B,CAAC,EAAE,OAAO,SAAS,8BAA8B,CAAC,GAAG,4BAA4B;AAEvH,WAAS,6BAA6B,KAAK;AACzC,qBAAiB,QAAQ,iBAAiB,MAAM,OAAO,cAAY,aAAa,GAAG;AACnF,WAAO,gBAAgB,GAAG;AAAA,EAC5B;AAEA,WAAS,6BAA6B,CAAC,EAAE,OAAO,SAAS,8BAA8B,CAAC,GAAG,4BAA4B;AACvH,QAAM,gCAAgC,OAAO,6BAA6B,CAAC,CAAC;AAC5E,UAAQ,6BAA6B,SAAS,0BAA0B;AACxE,QAAM,oCAAoC,OAAO,6BAA6B,CAAC,CAAC;AAChF,UAAQ,6BAA6B,SAAS,0BAA0B;AACxE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,qBAAqB,QAAQ;AACpC,SAAO,IAAI,MAAM,QAAQ;AAAA,IACvB,IAAIC,SAAQ,MAAM;AAChB,aAAO,OAAOA,QAAO,IAAI,MAAM,WAAW,qBAAqBA,QAAO,IAAI,CAAC,IAAI,SAAS,MAAMA,QAAO,IAAI,CAAC;AAAA,IAC5G;AAAA,EAEF,CAAC;AACH;AAEA,IAAI,MAAM;AACV,SAAS,aAAa,aAAa,OAAO;AACxC,MAAI;AAEJ,MAAI,eAAe,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAExF,MAAI,UAAU,WAAW,GAAG;AAC1B,mBAAe;AACf,kBAAc;AACd,YAAQ;AAAA,EACV;AAEA,MAAI;AAAA,IACF;AAAA,IACA,SAAS,YAAY;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,wBAAwB,sBAAsB,mBAAmB,OAAO,QAAQ,wBAAwB,SAAS,SAAS,oBAAoB;AAC/J,QAAM,mBAAmB,WAAW,SAAS,WAAW,CAAC;AAEzD,MAAI,CAAC,aAAa;AAChB,WAAO;AACP,kBAAc,cAAc,GAAG;AAAA,EACjC;AAEA,QAAM,oBAAoB,IAAI,CAAC,CAAC;AAChC,QAAM,eAAe,IAAI,eAAe;AACxC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,WAAW,kBAAkB;AAAA,IAC/B;AAAA,IACA;AAAA,EACF,CAAC,IAAI;AAAA,IACH,cAAc,IAAI,CAAC,CAAC;AAAA,EACtB;AAEA,MAAI,CAAC,eAAe,iBAAiB,aAAa;AAChD,UAAM,QAAQ,iBAAiB;AAC/B,YAAQ,IAAI,CAAC,CAAC;AACd,kBAAc,MAAM;AAClB,YAAM,QAAQ;AACd,YAAM,MAAM,WAAW,KAAK,IAAI,MAAM,KAAK,MAAM,OAAO,IAAI,qBAAqB,MAAM,KAAK,CAAC,IAAI,OAAO,CAAAC,iBAAe;AACrH,0BAAkB,QAAQ,eAAe;AAAA,UACvC,aAAAA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,iBAAiB,oBAAoB,SAAS;AAAA,QAChD,CAAC;AAAA,MACH,GAAG;AAAA,QACD,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AACD,mBAAe,iBAAiB,qBAAqB;AAAA,EACvD,OAAO;AACL,UAAM,yBAAyB,MAAM,WAAW,KAAK,QAAQ,WAAW,IAAI,cAAc,SAAS,eAAe,CAAC,CAAC;AACpH,UAAM,wBAAwB,wBAAsB;AAClD,wBAAkB,QAAQ,eAAe;AAAA,QACvC,aAAa;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAU,aAAa,QAAQ,aAAa,SAAS,WAAW,CAAC;AAAA,QACjE,iBAAiB;AAAA,MACnB,CAAC;AAAA,IACH,GAAG;AAAA,MACD,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AAEA,MAAI,UAAU;AACZ,kCAA8B,QAAQ,OAAK,EAAE,mBAAmB;AAAA,MAC9D;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,CAAC;AACF,oBAAgB,MAAM,kCAAkC,QAAQ,OAAK,EAAE,WAAW,CAAC,CAAC;AAAA,EACtF;AAEA,SAAO,SAAS,MAAM;AACpB,WAAO,eAAe,eAAe,CAAC,GAAG,MAAM,kBAAkB,KAAK,CAAC,GAAG,aAAa,KAAK;AAAA,EAC9F,CAAC;AACH;", "names": ["results", "key", "target", "validations"]}