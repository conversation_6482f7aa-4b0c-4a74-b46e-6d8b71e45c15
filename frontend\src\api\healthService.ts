import axiosInstance from './axios';
import { API_ENDPOINTS } from './axios';
import type { HealthCheckResponse } from '../types/api';

/**
 * 健康检查服务类
 */
export class HealthService {
  /**
   * 获取服务健康状态
   * @param signal 请求信号
   * @returns 健康状态信息
   */
  static async checkHealth(signal?: AbortSignal): Promise<HealthCheckResponse> {
    try {
      const { data } = await axiosInstance.get<HealthCheckResponse>(API_ENDPOINTS.HEALTH, {
        signal,
      });
      return data;
    } catch (error) {
      console.error('Health check failed:', error);
      throw error;
    }
  }
}

export default HealthService;
