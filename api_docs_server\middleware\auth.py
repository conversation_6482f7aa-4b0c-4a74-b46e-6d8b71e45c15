"""Authentication middleware for <PERSON><PERSON> account."""
from fastapi import Request, HTTPException, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import httpx
import logging
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logger = logging.getLogger(__name__)

# 创建安全依赖
security = HTTPBearer(auto_error=False)

async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """验证访问令牌."""
    if not credentials:
        logger.warning("No authentication credentials provided")
        raise HTTPException(status_code=401, detail="Authentication required")
    
    token = credentials.credentials
    
    try:
        # 获取环境变量或使用默认值
        auth_url = os.getenv("TAIHU_AUTH_URL", "https://test-odc.it.woa.com/api/auth-center/oauth2")
        
        # 调用太湖验证接口验证令牌
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{auth_url}/verify",
                json={"token": token},
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            data = response.json()
            
            # 检查验证结果
            if data.get("active", False):
                return data
            else:
                logger.error(f"Invalid token: {data}")
                raise HTTPException(status_code=401, detail="Invalid or expired token")
                
    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error during token verification: {str(e)}")
        raise HTTPException(status_code=401, detail="Failed to verify token")
    except Exception as e:
        logger.error(f"Unexpected error during token verification: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

class TaihuAuthMiddleware:
    """Taihu认证中间件."""
    
    def __init__(self, app):
        self.app = app
        
    async def __call__(self, request: Request, call_next):
        # 排除不需要认证的路径
        excluded_paths = [
            "/api/health",
            "/api/auth/login",
            "/api/auth/callback",
            "/docs",
            "/redoc",
            "/openapi.json"
        ]
        
        path = request.url.path
        
        # 如果是排除的路径，直接放行
        if any(path.startswith(excluded) for excluded in excluded_paths):
            return await call_next(request)
            
        # 获取认证头
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            logger.warning(f"Missing or invalid Authorization header for path: {path}")
            return HTTPException(status_code=401, detail="Authentication required")
            
        token = auth_header.replace("Bearer ", "")
        
        try:
            # 验证令牌
            auth_url = os.getenv("TAIHU_AUTH_URL", "https://test-odc.it.woa.com/api/auth-center/oauth2")
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{auth_url}/verify",
                    json={"token": token},
                    headers={"Content-Type": "application/json"}
                )
                response.raise_for_status()
                data = response.json()
                
                # 检查验证结果
                if not data.get("active", False):
                    logger.error(f"Invalid token: {data}")
                    return HTTPException(status_code=401, detail="Invalid or expired token")
                    
            # 令牌有效，继续处理请求
            return await call_next(request)
                    
        except Exception as e:
            logger.error(f"Error during token verification: {str(e)}")
            return HTTPException(status_code=401, detail="Authentication failed")
