"""add_icon_and_metadata_fields

Revision ID: 79499a4b96f3
Revises: 
Create Date: 2025-01-23 22:23:56.902407

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '79499a4b96f3'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('api_docs', sa.Column('icon_url', sa.String(length=2048), nullable=True))
    op.add_column('api_docs', sa.Column('icon_data', sa.Text(), nullable=True))
    op.add_column('api_docs', sa.Column('auto_metadata', sa.<PERSON>(), nullable=False, server_default='true'))
    op.add_column('api_docs', sa.Column('metadata_updated_at', sa.DateTime(), nullable=True))
    op.add_column('api_docs', sa.Column('view_count', sa.Integer(), nullable=False, server_default='0'))
    
    # 修改ID字段类型为UUID
    op.alter_column('api_docs', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.String(length=36),
               existing_nullable=False)
    
    # 修改title为可空
    op.alter_column('api_docs', 'title',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    
    # 修改时间戳字段，保持时区支持
    op.alter_column('api_docs', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('api_docs', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    
    # 更新索引
    op.drop_index('ix_api_docs_id', table_name='api_docs')
    op.create_index(op.f('ix_api_docs_url'), 'api_docs', ['url'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_api_docs_url'), table_name='api_docs')
    op.create_index('ix_api_docs_id', 'api_docs', ['id'], unique=False)
    
    # 恢复时间戳字段
    op.alter_column('api_docs', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('api_docs', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    
    # 恢复title为非空
    op.alter_column('api_docs', 'title',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    
    # 恢复ID字段类型
    op.alter_column('api_docs', 'id',
               existing_type=sa.String(length=36),
               type_=sa.INTEGER(),
               existing_nullable=False)
    
    # 删除新增的列
    op.drop_column('api_docs', 'view_count')
    op.drop_column('api_docs', 'metadata_updated_at')
    op.drop_column('api_docs', 'auto_metadata')
    op.drop_column('api_docs', 'icon_data')
    op.drop_column('api_docs', 'icon_url')
    # ### end Alembic commands ###
