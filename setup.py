"""Describe our module distribution to Distutils."""

# Import future modules
from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

# Import third-party modules
from setuptools import find_packages
from setuptools import setup

setup(
    name="api_docs_server",
    author="<PERSON><PERSON>",
    author_email="<PERSON><EMAIL>",
    url="https://git.woa.com/lightbox/microservices/api_docs_server",
    package_dir={"": "."},
    packages=find_packages("."),
    description="Collection of API docs.",
    entry_points={},
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "Operating System :: OS Independent",
        "Programming Language :: Python",
    ],
    use_scm_version=True,
    setup_requires=["setuptools_scm"],
)
