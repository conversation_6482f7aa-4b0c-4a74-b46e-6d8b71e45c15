import { ref, computed } from 'vue'
import type { ApiDoc } from '@/types'
import { ApiDocService, HealthService } from '@/api/apiService'
import { useToast } from 'primevue/usetoast'

// 创建单例状态
const docs = ref<ApiDoc[]>([])
const isLoading = ref(false)
const lastRefreshTime = ref<number>(0)
const pendingRefresh = ref<number | null>(null)
const refreshInterval = 5000 // 5秒的刷新间隔
const healthCheckController = ref<AbortController | null>(null)

export function useApiDocs() {
  const toast = useToast()

  // 按访问量和字母顺序排序的文档列表
  const sortedDocs = computed(() => {
    return [...docs.value].sort((a, b) => {
      // 先按访问次数降序
      if (a.view_count !== b.view_count) {
        return (b.view_count || 0) - (a.view_count || 0)
      }
      // 再按标题字母升序，处理 null 或 undefined 的情况
      const titleA = a.title || ''
      const titleB = b.title || ''
      return titleA.localeCompare(titleB, 'zh-Hans-CN')
    })
  })

  // 搜索文档
  const searchDocs = (query: string) => {
    if (!query) return sortedDocs.value
    
    query = query.toLowerCase()
    return sortedDocs.value.filter(doc => {
      // 确保所有字段都存在再进行搜索
      const title = (doc.title || '').toLowerCase()
      const description = (doc.description || '').toLowerCase()
      const url = doc.url.toLowerCase()
      
      return title.includes(query) ||
             description.includes(query) ||
             url.includes(query)
    }).sort((a, b) => {
      // 计算匹配分数
      const scoreA = getMatchScore(a, query)
      const scoreB = getMatchScore(b, query)
      if (scoreA !== scoreB) return scoreB - scoreA
      
      // 分数相同则按默认排序
      return sortedDocs.value.indexOf(a) - sortedDocs.value.indexOf(b)
    })
  }

  // 计算搜索匹配分数
  const getMatchScore = (doc: ApiDoc, query: string): number => {
    let score = 0
    const title = (doc.title || '').toLowerCase()
    const description = (doc.description || '').toLowerCase()
    const url = doc.url.toLowerCase()

    if (title.includes(query)) {
      score += 100
      if (title === query) score += 50
      if (title.startsWith(query)) score += 30
    }
    if (description.includes(query)) score += 50
    if (url.includes(query)) score += 30
    
    return score
  }

  // 智能刷新：避免频繁请求
  const smartRefresh = async (immediate = false) => {
    // 取消之前的延迟刷新
    if (pendingRefresh.value) {
      clearTimeout(pendingRefresh.value)
      pendingRefresh.value = null
    }

    // 取消之前的健康检查
    if (healthCheckController.value) {
      healthCheckController.value.abort()
      healthCheckController.value = null
    }

    // 检查是否需要立即刷新
    const now = Date.now()
    const shouldRefreshNow = immediate || (now - lastRefreshTime.value > refreshInterval)

    if (shouldRefreshNow && !isLoading.value) {
      await refreshDocs()
    } else if (!pendingRefresh.value) {
      // 创建新的健康检查控制器
      healthCheckController.value = new AbortController()
      
      try {
        // 执行健康检查
        await HealthService.checkHealth(healthCheckController.value.signal)
        
        // 设置延迟刷新
        pendingRefresh.value = window.setTimeout(() => {
          refreshDocs()
          pendingRefresh.value = null
        }, refreshInterval)
      } catch (error) {
        if (error instanceof Error && error.name === 'AbortError') {
          // 忽略取消的请求
          console.debug('Health check aborted')
        } else {
          console.error('Health check failed:', error)
        }
      }
    }
  }

  // 刷新文档列表
  const refreshDocs = async () => {
    if (isLoading.value) return

    isLoading.value = true
    try {
      const newDocs = await ApiDocService.getDocs()
      docs.value = newDocs
      lastRefreshTime.value = Date.now()
    } catch (error) {
      console.error('Failed to fetch docs:', error)
      toast.add({
        severity: 'error',
        summary: '加载失败',
        detail: '无法获取文档列表',
        life: 3000
      })
    } finally {
      isLoading.value = false
    }
  }

  // 乐观更新：增加访问次数
  const incrementViewCount = async (docId: string) => {
    const doc = docs.value.find(d => d.id === docId)
    if (!doc) return

    // 乐观更新
    const updatedDoc = {
      ...doc,
      view_count: (doc.view_count || 0) + 1
    }
    optimisticUpdate(updatedDoc)

    try {
      // 发送API请求
      const result = await ApiDocService.incrementViewCount(docId)
      // 使用服务器返回的数据更新
      optimisticUpdate(result)
    } catch (error) {
      console.error('Failed to increment view count:', error)
      // 发生错误时回滚
      optimisticUpdate(doc)
    }
  }

  // 乐观更新：添加文档
  const optimisticAdd = (doc: ApiDoc) => {
    docs.value = [doc, ...docs.value]
  }

  // 乐观更新：更新文档
  const optimisticUpdate = (doc: ApiDoc) => {
    const index = docs.value.findIndex(d => d.id === doc.id)
    if (index !== -1) {
      docs.value[index] = doc
    }
  }

  // 乐观更新：删除文档
  const optimisticDelete = (docId: string) => {
    docs.value = docs.value.filter(d => d.id !== docId)
  }

  return {
    docs: sortedDocs,
    isLoading,
    searchDocs,
    smartRefresh,
    incrementViewCount,
    optimisticAdd,
    optimisticUpdate,
    optimisticDelete
  }
}
