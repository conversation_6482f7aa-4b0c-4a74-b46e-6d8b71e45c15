# 启动后端服务
$backendProcess = Start-Process -FilePath "python" -ArgumentList "-m", "uvicorn", "api_docs_server.main:app", "--reload", "--host", "localhost", "--port", "8000" -WindowStyle Normal -PassThru

Write-Host "后端服务已启动，PID: $($backendProcess.Id)"

# 等待后端服务启动
Start-Sleep -Seconds 2

# 保存原始工作目录和脚本目录
$originalPath = Get-Location
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path

# 切换到脚本目录
Set-Location $scriptPath

function Stop-Services {
    param (
        [int]$backendPid,
        [int]$frontendPid
    )

    Write-Host "`n正在停止服务..." -ForegroundColor Yellow

    # 停止后端服务及其子进程
    if ($backendPid -and (Get-Process -Id $backendPid -ErrorAction SilentlyContinue)) {
        Get-Process | Where-Object { $_.Parent.Id -eq $backendPid -or $_.Id -eq $backendPid } | Stop-Process -Force
        Write-Host "已停止后端服务 (PID: $backendPid)" -ForegroundColor Green
    }

    # 停止前端服务及其子进程
    if ($frontendPid -and (Get-Process -Id $frontendPid -ErrorAction SilentlyContinue)) {
        Get-Process | Where-Object { $_.Parent.Id -eq $frontendPid -or $_.Id -eq $frontendPid } | Stop-Process -Force
        Write-Host "已停止前端服务 (PID: $frontendPid)" -ForegroundColor Green
    }

    # 清理可能残留的 Python 和 bun 进程
    Get-Process | Where-Object { $_.ProcessName -match 'python|bun' } | Stop-Process -Force -ErrorAction SilentlyContinue
}

function Test-ServiceHealth {
    param (
        [string]$url,
        [int]$retries = 5,
        [int]$delaySeconds = 2
    )

    for ($i = 1; $i -le $retries; $i++) {
        try {
            $response = Invoke-WebRequest -Uri $url -Method HEAD -UseBasicParsing -TimeoutSec 5
            if ($response.StatusCode -eq 200) {
                return $true
            }
        }
        catch {
            Write-Host "等待服务启动中... (尝试 $i/$retries)" -ForegroundColor Yellow
            Start-Sleep -Seconds $delaySeconds
        }
    }
    return $false
}

try {
    # 启动后端服务
    Write-Host "`n正在启动后端服务..." -ForegroundColor Cyan
    $backendProcess = Start-Process -FilePath "python" -ArgumentList "-m", "uvicorn", "api_docs_server.main:app", "--reload", "--host", "localhost", "--port", "8000" -WindowStyle Normal -PassThru

    if (-not (Test-ServiceHealth -url "http://localhost:8000/docs")) {
        throw "后端服务启动失败"
    }
    Write-Host "后端服务已启动 (PID: $($backendProcess.Id))" -ForegroundColor Green

    # 启动前端服务
    Write-Host "`n正在启动前端服务..." -ForegroundColor Cyan
    Push-Location "frontend"
    # 检查是否安装了依赖
    if (-not (Test-Path "node_modules")) {
        Write-Host "正在安装前端依赖..." -ForegroundColor Yellow
        & bun install
        if ($LASTEXITCODE -ne 0) {
            throw "前端依赖安装失败"
        }
    }
    $frontendProcess = Start-Process -FilePath "bun" -ArgumentList "run", "dev" -WindowStyle Normal -PassThru
    Pop-Location

    if (-not (Test-ServiceHealth -url "http://localhost:5173")) {
        throw "前端服务启动失败"
    }
    Write-Host "前端服务已启动 (PID: $($frontendProcess.Id))" -ForegroundColor Green

    # 显示服务信息
    Write-Host "`n服务已成功启动:" -ForegroundColor Green
    Write-Host "前端: http://localhost:5173" -ForegroundColor Cyan
    Write-Host "后端: http://localhost:8000" -ForegroundColor Cyan
    Write-Host "API文档: http://localhost:8000/docs" -ForegroundColor Cyan
    Write-Host "`n按 Enter 键停止所有服务..." -ForegroundColor Yellow

    # 等待用户按 Enter
    $null = Read-Host
}
catch {
    Write-Host "`n错误: $_" -ForegroundColor Red
}
finally {
    # 停止所有服务
    Stop-Services -backendPid $backendProcess.Id -frontendPid $frontendProcess.Id

    # 返回到用户原始目录
    Set-Location $originalPath

    Write-Host "`n所有服务已停止，脚本执行完毕" -ForegroundColor Green
}
