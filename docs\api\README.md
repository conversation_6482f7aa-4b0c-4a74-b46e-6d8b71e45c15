# API Documentation Server 接口文档

## 基础信息

- 基础URL: `http://localhost:8000`
- 响应格式: JSON
- 认证方式: 暂无认证要求

## API 端点

### 1. 健康检查

#### 获取服务健康状态
```http
GET /api/health
```

**响应示例:**
```json
{
    "status": "ok",
    "start_time": "2025-01-23T20:15:51.739861",
    "uptime_seconds": 12.022321
}
```

### 2. 文档管理

#### 2.1 获取文档列表
```http
GET /api/docs
```

**查询参数:**
- `skip` (整数, 可选): 跳过的记录数，默认为0
- `limit` (整数, 可选): 返回的最大记录数，默认为100

**响应示例:**
```json
[
    {
        "id": 1,
        "url": "https://api.github.com",
        "title": "GitHub API",
        "description": "GitHub REST API documentation",
        "created_at": "2025-01-23T20:15:51.739861",
        "updated_at": "2025-01-23T20:15:51.739861"
    }
]
```

#### 2.2 创建新文档
```http
POST /api/docs
```

**请求体:**
```json
{
    "url": "https://api.github.com",
    "title": "GitHub API",
    "description": "GitHub REST API documentation"
}
```

**字段说明:**
- `url` (字符串, 必填): API文档URL
- `title` (字符串, 可选): 文档标题
- `description` (字符串, 可选): 文档描述

**响应示例:**
```json
{
    "id": 1,
    "url": "https://api.github.com",
    "title": "GitHub API",
    "description": "GitHub REST API documentation",
    "created_at": "2025-01-23T20:15:51.739861",
    "updated_at": "2025-01-23T20:15:51.739861"
}
```

#### 2.3 获取单个文档
```http
GET /api/docs/{id}
```

**路径参数:**
- `id` (整数): 文档ID

**响应示例:**
```json
{
    "id": 1,
    "url": "https://api.github.com",
    "title": "GitHub API",
    "description": "GitHub REST API documentation",
    "created_at": "2025-01-23T20:15:51.739861",
    "updated_at": "2025-01-23T20:15:51.739861"
}
```

#### 2.4 更新文档
```http
PUT /api/docs/{id}
```

**路径参数:**
- `id` (整数): 文档ID

**请求体:**
```json
{
    "title": "Updated GitHub API",
    "description": "Updated description"
}
```

**响应示例:**
```json
{
    "id": 1,
    "url": "https://api.github.com",
    "title": "Updated GitHub API",
    "description": "Updated description",
    "created_at": "2025-01-23T20:15:51.739861",
    "updated_at": "2025-01-23T20:15:51.739861"
}
```

#### 2.5 删除文档
```http
DELETE /api/docs/{id}
```

**路径参数:**
- `id` (整数): 文档ID

**响应:**
- 状态码: 204 No Content

### 3. URL 验证

#### 验证URL可访问性
```http
POST /api/docs/url/validate
```

**请求体:**
```json
{
    "url": "https://api.github.com"
}
```

**响应示例:**
```json
{
    "valid": true
}
```

### 4. API 文档界面

#### Swagger UI
```http
GET /docs
```

#### ReDoc
```http
GET /redoc
```

#### OpenAPI 规范
```http
GET /openapi.json
```

## 错误处理

所有API端点在发生错误时都会返回一个包含错误详情的JSON响应。

### 错误响应格式
```json
{
    "detail": "错误信息"
}
```

### 常见状态码
- 200: 请求成功
- 201: 创建成功
- 204: 删除成功
- 400: 请求参数错误
- 404: 资源不存在
- 422: 请求体验证失败
- 500: 服务器内部错误

## 开发环境设置

1. 启动开发服务器:
```bash
python -m uvicorn api_docs_server.main:app --reload
```

2. 访问API文档:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 注意事项

1. 所有时间字段均使用ISO 8601格式
2. URL验证会检查URL的格式和可访问性
3. 文档创建时会自动验证URL
4. 分页参数(skip/limit)用于控制返回的数据量
