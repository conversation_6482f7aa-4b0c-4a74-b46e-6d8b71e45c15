import axiosInstance from './axios';
import type {
  ApiDoc,
  CreateDocRequest,
  UpdateDocRequest,
  ValidateUrlRequest,
  ValidateUrlResponse,
  PaginationParams,
  HealthCheckResponse,
} from '../types/api';
import { API_ENDPOINTS } from './axios';

/**
 * API文档服务类
 */
export class ApiDocService {
  /**
   * 获取文档列表
   * @param params 分页参数
   * @returns 文档列表
   */
  static async getDocs(params?: PaginationParams): Promise<ApiDoc[]> {
    try {
      const { data } = await axiosInstance.get<ApiDoc[]>(API_ENDPOINTS.DOCS, {
        params,
      });
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error('Error fetching docs:', error);
      throw error;
    }
  }

  /**
   * 获取单个文档
   * @param id 文档ID (UUID)
   * @returns 文档信息
   */
  static async getDoc(id: string): Promise<ApiDoc> {
    try {
      const { data } = await axiosInstance.get<ApiDoc>(`${API_ENDPOINTS.DOCS}/${id}`);
      return data;
    } catch (error) {
      console.error(`Error fetching doc ${id}:`, error);
      throw error;
    }
  }

  /**
   * 创建新文档
   * @param doc 文档信息
   * @returns 创建的文档
   */
  static async createDoc(doc: CreateDocRequest): Promise<ApiDoc> {
    try {
      const { data } = await axiosInstance.post<ApiDoc>(API_ENDPOINTS.DOCS, doc);
      return data;
    } catch (error) {
      console.error('Error creating doc:', error);
      throw error;
    }
  }

  /**
   * 更新文档
   * @param id 文档ID (UUID)
   * @param doc 更新的文档信息
   * @returns 更新后的文档
   */
  static async updateDoc(id: string, doc: UpdateDocRequest): Promise<ApiDoc> {
    try {
      const { data } = await axiosInstance.put<ApiDoc>(`${API_ENDPOINTS.DOCS}/${id}`, doc);
      return data;
    } catch (error) {
      console.error(`Error updating doc ${id}:`, error);
      throw error;
    }
  }

  /**
   * 删除文档
   * @param id 文档ID (UUID)
   */
  static async deleteDoc(id: string): Promise<void> {
    try {
      await axiosInstance.delete(`${API_ENDPOINTS.DOCS}/${id}`);
    } catch (error) {
      console.error(`Error deleting doc ${id}:`, error);
      throw error;
    }
  }

  /**
   * 刷新文档元数据
   * @param id 文档ID (UUID)
   * @returns 更新后的文档
   */
  static async refreshMetadata(id: string): Promise<ApiDoc> {
    try {
      const { data } = await axiosInstance.post<ApiDoc>(`${API_ENDPOINTS.DOCS}/${id}/refresh`);
      return data;
    } catch (error) {
      console.error(`Error refreshing metadata for doc ${id}:`, error);
      throw error;
    }
  }

  /**
   * 增加文档访问计数
   * @param id 文档ID (UUID)
   * @returns 更新后的文档
   */
  static async incrementViewCount(id: string): Promise<ApiDoc> {
    try {
      const { data } = await axiosInstance.post<ApiDoc>(`${API_ENDPOINTS.DOCS}/${id}/view`);
      return data;
    } catch (error) {
      console.error(`Error incrementing view count for doc ${id}:`, error);
      throw error;
    }
  }

  /**
   * 验证URL
   * @param url 要验证的URL
   * @returns 验证结果
   */
  static async validateUrl(url: string): Promise<ValidateUrlResponse> {
    try {
      const { data } = await axiosInstance.post<ValidateUrlResponse>(API_ENDPOINTS.VALIDATE_URL, {
        url,
      } as ValidateUrlRequest);
      return data;
    } catch (error) {
      console.error('Error validating URL:', error);
      throw error;
    }
  }
}

/**
 * 健康检查服务类
 */
export class HealthService {
  /**
   * 获取服务健康状态
   * @param signal 请求信号
   * @returns 健康状态信息
   */
  static async checkHealth(signal?: AbortSignal): Promise<HealthCheckResponse> {
    try {
      const { data } = await axiosInstance.get<HealthCheckResponse>(API_ENDPOINTS.HEALTH, {
        signal,
      });
      return data;
    } catch (error) {
      console.error('Error checking health:', error);
      throw error;
    }
  }
}
