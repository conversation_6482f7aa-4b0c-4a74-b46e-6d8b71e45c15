import { defineStore } from 'pinia';
import axios from 'axios';

interface User {
  id: string;
  name: string;
  email?: string;
  [key: string]: any;
}

interface AuthState {
  token: string | null;
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    token: localStorage.getItem('auth_token') || null,
    user: null,
    isAuthenticated: !!localStorage.getItem('auth_token'),
    loading: false
  }),

  getters: {
    getToken: (state) => state.token,
    getUser: (state) => state.user,
    isLoggedIn: (state) => state.isAuthenticated
  },

  actions: {
    setToken(token: string) {
      this.token = token;
      this.isAuthenticated = true;
      localStorage.setItem('auth_token', token);

      // 设置axios默认请求头
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    },

    clearToken() {
      this.token = null;
      this.user = null;
      this.isAuthenticated = false;
      localStorage.removeItem('auth_token');

      // 清除axios默认请求头
      delete axios.defaults.headers.common['Authorization'];
    },

    async fetchUserInfo() {
      if (!this.token) return;

      this.loading = true;
      try {
        // 添加获取用户信息的API调用
        const response = await axios.get('/backend/api/auth/user');
        this.user = response.data;
      } catch (error) {
        console.error('Failed to fetch user info:', error);
        this.clearToken();
      } finally {
        this.loading = false;
      }
    },

    async handleCallback(code: string, accessToken?: string) {
      this.loading = true;
      try {
        // 如果已经有access_token（从URL参数获取），直接使用
        if (accessToken) {
          this.setToken(accessToken);
          await this.fetchUserInfo();
          return true;
        }

        // 否则调用后端API获取token
        const response = await axios.get(`/backend/api/auth/callback?code=${code}`);
        if (response.data && response.data.access_token) {
          this.setToken(response.data.access_token);
          await this.fetchUserInfo();
          return true;
        }
        return false;
      } catch (error) {
        console.error('Auth callback failed:', error);
        return false;
      } finally {
        this.loading = false;
      }
    },

    async logout() {
      this.loading = true;
      try {
        // 可选：调用后端注销接口
        // await axios.post('/backend/api/auth/logout');
        this.clearToken();
        return true;
      } catch (error) {
        console.error('Logout failed:', error);
        return false;
      } finally {
        this.loading = false;
      }
    },

    // 初始化认证状态
    async init() {
      if (this.token) {
        await this.fetchUserInfo();
      }
    }
  }
});
