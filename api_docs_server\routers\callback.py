from fastapi import APIRouter, Request, HTTPException
import httpx

router = APIRouter()

@router.get("/taihu_callback")
async def taihu_callback(request: Request):
    code = request.query_params.get("code")
    state = request.query_params.get("state")

    if not code or not state:
        raise HTTPException(status_code=400, detail="Missing code or state")

    # 验证授权码
    token_url = "https://tai.it.tencent.com/api/auth-center/oauth2/token"
    data = {
        "grant_type": "authorization_code",
        "client_id": "api-docs",
        "client_secret": "WNEK28UBJHHMN0D81GMPBHAWWZ4GEL2U",
        "code": code,
        "redirect_uri": "https://api-docs.woa.com/taihu_callback"
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(token_url, data=data)
        if response.status_code != 200:
            raise HTTPException(status_code=400, detail="Failed to fetch token")

        return response.json()