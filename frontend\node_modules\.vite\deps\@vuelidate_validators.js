import {
  unref
} from "./chunk-U3LI7FBV.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/@vuelidate/validators/dist/index.mjs
function ownKeys(object, enumerableOnly) {
  var keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);
    enumerableOnly && (symbols = symbols.filter(function(sym) {
      return Object.getOwnPropertyDescriptor(object, sym).enumerable;
    })), keys.push.apply(keys, symbols);
  }
  return keys;
}
function _objectSpread2(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = null != arguments[i] ? arguments[i] : {};
    i % 2 ? ownKeys(Object(source), true).forEach(function(key) {
      _defineProperty(target, key, source[key]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {
      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
    });
  }
  return target;
}
function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
function isFunction(val) {
  return typeof val === "function";
}
function isObject(o) {
  return o !== null && typeof o === "object" && !Array.isArray(o);
}
function normalizeValidatorObject(validator) {
  return isFunction(validator.$validator) ? _objectSpread2({}, validator) : {
    $validator: validator
  };
}
function isPromise(object) {
  return isObject(object) && isFunction(object.then);
}
function unwrapValidatorResponse(result) {
  if (typeof result === "object") return result.$valid;
  return result;
}
function unwrapNormalizedValidator(validator) {
  return validator.$validator || validator;
}
function withParams($params, $validator) {
  if (!isObject($params)) throw new Error(`[@vuelidate/validators]: First parameter to "withParams" should be an object, provided ${typeof $params}`);
  if (!isObject($validator) && !isFunction($validator)) throw new Error(`[@vuelidate/validators]: Validator must be a function or object with $validator parameter`);
  const validatorObj = normalizeValidatorObject($validator);
  validatorObj.$params = _objectSpread2(_objectSpread2({}, validatorObj.$params || {}), $params);
  return validatorObj;
}
function withMessage($message, $validator) {
  if (!isFunction($message) && typeof unref($message) !== "string") throw new Error(`[@vuelidate/validators]: First parameter to "withMessage" should be string or a function returning a string, provided ${typeof $message}`);
  if (!isObject($validator) && !isFunction($validator)) throw new Error(`[@vuelidate/validators]: Validator must be a function or object with $validator parameter`);
  const validatorObj = normalizeValidatorObject($validator);
  validatorObj.$message = $message;
  return validatorObj;
}
function withAsync($validator) {
  let $watchTargets = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];
  const validatorObj = normalizeValidatorObject($validator);
  return _objectSpread2(_objectSpread2({}, validatorObj), {}, {
    $async: true,
    $watchTargets
  });
}
function forEach(validators) {
  return {
    $validator(collection) {
      for (var _len = arguments.length, others = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
        others[_key - 1] = arguments[_key];
      }
      return unref(collection).reduce((previous, collectionItem, index) => {
        const collectionEntryResult = Object.entries(collectionItem).reduce((all, _ref) => {
          let [property, $model] = _ref;
          const innerValidators = validators[property] || {};
          const propertyResult = Object.entries(innerValidators).reduce((all2, _ref2) => {
            let [validatorName, currentValidator] = _ref2;
            const validatorFunction = unwrapNormalizedValidator(currentValidator);
            const $response = validatorFunction.call(this, $model, collectionItem, index, ...others);
            const $valid = unwrapValidatorResponse($response);
            all2.$data[validatorName] = $response;
            all2.$data.$invalid = !$valid || !!all2.$data.$invalid;
            all2.$data.$error = all2.$data.$invalid;
            if (!$valid) {
              let $message = currentValidator.$message || "";
              const $params = currentValidator.$params || {};
              if (typeof $message === "function") {
                $message = $message({
                  $pending: false,
                  $invalid: !$valid,
                  $params,
                  $model,
                  $response
                });
              }
              all2.$errors.push({
                $property: property,
                $message,
                $params,
                $response,
                $model,
                $pending: false,
                $validator: validatorName
              });
            }
            return {
              $valid: all2.$valid && $valid,
              $data: all2.$data,
              $errors: all2.$errors
            };
          }, {
            $valid: true,
            $data: {},
            $errors: []
          });
          all.$data[property] = propertyResult.$data;
          all.$errors[property] = propertyResult.$errors;
          return {
            $valid: all.$valid && propertyResult.$valid,
            $data: all.$data,
            $errors: all.$errors
          };
        }, {
          $valid: true,
          $data: {},
          $errors: {}
        });
        return {
          $valid: previous.$valid && collectionEntryResult.$valid,
          $data: previous.$data.concat(collectionEntryResult.$data),
          $errors: previous.$errors.concat(collectionEntryResult.$errors)
        };
      }, {
        $valid: true,
        $data: [],
        $errors: []
      });
    },
    $message: (_ref3) => {
      let {
        $response
      } = _ref3;
      return $response ? $response.$errors.map((context) => {
        return Object.values(context).map((errors) => errors.map((error) => error.$message)).reduce((a, b) => a.concat(b), []);
      }) : [];
    }
  };
}
var req = (value) => {
  value = unref(value);
  if (Array.isArray(value)) return !!value.length;
  if (value === void 0 || value === null) {
    return false;
  }
  if (value === false) {
    return true;
  }
  if (value instanceof Date) {
    return !isNaN(value.getTime());
  }
  if (typeof value === "object") {
    for (let _ in value) return true;
    return false;
  }
  return !!String(value).length;
};
var len = (value) => {
  value = unref(value);
  if (Array.isArray(value)) return value.length;
  if (typeof value === "object") {
    return Object.keys(value).length;
  }
  return String(value).length;
};
function regex() {
  for (var _len = arguments.length, expr = new Array(_len), _key = 0; _key < _len; _key++) {
    expr[_key] = arguments[_key];
  }
  return (value) => {
    value = unref(value);
    return !req(value) || expr.every((reg) => {
      reg.lastIndex = 0;
      return reg.test(value);
    });
  };
}
var common = Object.freeze({
  __proto__: null,
  forEach,
  len,
  normalizeValidatorObject,
  regex,
  req,
  unwrap: unref,
  unwrapNormalizedValidator,
  unwrapValidatorResponse,
  withAsync,
  withMessage,
  withParams
});
var alpha$1 = regex(/^[a-zA-Z]*$/);
var alpha = {
  $validator: alpha$1,
  $message: "The value is not alphabetical",
  $params: {
    type: "alpha"
  }
};
var alphaNum$1 = regex(/^[a-zA-Z0-9]*$/);
var alphaNum = {
  $validator: alphaNum$1,
  $message: "The value must be alpha-numeric",
  $params: {
    type: "alphaNum"
  }
};
var numeric$1 = regex(/^\d*(\.\d+)?$/);
var numeric = {
  $validator: numeric$1,
  $message: "Value must be numeric",
  $params: {
    type: "numeric"
  }
};
function between$1(min, max) {
  return (value) => !req(value) || (!/\s/.test(value) || value instanceof Date) && +unref(min) <= +value && +unref(max) >= +value;
}
function between(min, max) {
  return {
    $validator: between$1(min, max),
    $message: (_ref) => {
      let {
        $params
      } = _ref;
      return `The value must be between ${$params.min} and ${$params.max}`;
    },
    $params: {
      min,
      max,
      type: "between"
    }
  };
}
var emailRegex = /^(?:[A-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[A-z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9]{2,}(?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])$/i;
var email$1 = regex(emailRegex);
var email = {
  $validator: email$1,
  $message: "Value is not a valid email address",
  $params: {
    type: "email"
  }
};
function ipAddress$1(value) {
  if (!req(value)) {
    return true;
  }
  if (typeof value !== "string") {
    return false;
  }
  const nibbles = value.split(".");
  return nibbles.length === 4 && nibbles.every(nibbleValid);
}
var nibbleValid = (nibble) => {
  if (nibble.length > 3 || nibble.length === 0) {
    return false;
  }
  if (nibble[0] === "0" && nibble !== "0") {
    return false;
  }
  if (!nibble.match(/^\d+$/)) {
    return false;
  }
  const numeric2 = +nibble | 0;
  return numeric2 >= 0 && numeric2 <= 255;
};
var ipAddress = {
  $validator: ipAddress$1,
  $message: "The value is not a valid IP address",
  $params: {
    type: "ipAddress"
  }
};
function macAddress$1() {
  let separator = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : ":";
  return (value) => {
    separator = unref(separator);
    if (!req(value)) {
      return true;
    }
    if (typeof value !== "string") {
      return false;
    }
    const parts = typeof separator === "string" && separator !== "" ? value.split(separator) : value.length === 12 || value.length === 16 ? value.match(/.{2}/g) : null;
    return parts !== null && (parts.length === 6 || parts.length === 8) && parts.every(hexValid);
  };
}
var hexValid = (hex) => hex.toLowerCase().match(/^[0-9a-f]{2}$/);
function macAddress(separator) {
  return {
    $validator: macAddress$1(separator),
    $message: "The value is not a valid MAC Address",
    $params: {
      type: "macAddress"
    }
  };
}
function maxLength$1(length) {
  return (value) => !req(value) || len(value) <= unref(length);
}
function maxLength(max) {
  return {
    $validator: maxLength$1(max),
    $message: (_ref) => {
      let {
        $params
      } = _ref;
      return `The maximum length allowed is ${$params.max}`;
    },
    $params: {
      max,
      type: "maxLength"
    }
  };
}
function minLength$1(length) {
  return (value) => !req(value) || len(value) >= unref(length);
}
function minLength(min) {
  return {
    $validator: minLength$1(min),
    $message: (_ref) => {
      let {
        $params
      } = _ref;
      return `This field should be at least ${$params.min} characters long`;
    },
    $params: {
      min,
      type: "minLength"
    }
  };
}
function required$1(value) {
  if (typeof value === "string") {
    value = value.trim();
  }
  return req(value);
}
var required = {
  $validator: required$1,
  $message: "Value is required",
  $params: {
    type: "required"
  }
};
var validate$1 = (prop, val) => prop ? req(typeof val === "string" ? val.trim() : val) : true;
function requiredIf$1(propOrFunction) {
  return function(value, parentVM) {
    if (typeof propOrFunction !== "function") {
      return validate$1(unref(propOrFunction), value);
    }
    const result = propOrFunction.call(this, value, parentVM);
    return validate$1(result, value);
  };
}
function requiredIf(prop) {
  return {
    $validator: requiredIf$1(prop),
    $message: "The value is required",
    $params: {
      type: "requiredIf",
      prop
    }
  };
}
var validate = (prop, val) => !prop ? req(typeof val === "string" ? val.trim() : val) : true;
function requiredUnless$1(propOrFunction) {
  return function(value, parentVM) {
    if (typeof propOrFunction !== "function") {
      return validate(unref(propOrFunction), value);
    }
    const result = propOrFunction.call(this, value, parentVM);
    return validate(result, value);
  };
}
function requiredUnless(prop) {
  return {
    $validator: requiredUnless$1(prop),
    $message: "The value is required",
    $params: {
      type: "requiredUnless",
      prop
    }
  };
}
function sameAs$1(equalTo) {
  return (value) => unref(value) === unref(equalTo);
}
function sameAs(equalTo) {
  let otherName = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "other";
  return {
    $validator: sameAs$1(equalTo),
    $message: (_ref) => {
      return `The value must be equal to the ${otherName} value`;
    },
    $params: {
      equalTo,
      otherName,
      type: "sameAs"
    }
  };
}
var urlRegex = /^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u00a1-\uffff][a-z0-9\u00a1-\uffff_-]{0,62})?[a-z0-9\u00a1-\uffff]\.)+(?:[a-z\u00a1-\uffff]{2,}\.?))(?::\d{2,5})?(?:[/?#]\S*)?$/i;
var url$1 = regex(urlRegex);
var url = {
  $validator: url$1,
  $message: "The value is not a valid URL address",
  $params: {
    type: "url"
  }
};
function syncOr(validators) {
  return function() {
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    return validators.reduce((valid, fn) => {
      if (unwrapValidatorResponse(valid)) return valid;
      return unwrapNormalizedValidator(fn).apply(this, args);
    }, false);
  };
}
function asyncOr(validators) {
  return function() {
    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
      args[_key2] = arguments[_key2];
    }
    return validators.reduce(async (valid, fn) => {
      const r = await valid;
      if (unwrapValidatorResponse(r)) return r;
      return unwrapNormalizedValidator(fn).apply(this, args);
    }, Promise.resolve(false));
  };
}
function or$1() {
  for (var _len3 = arguments.length, validators = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
    validators[_key3] = arguments[_key3];
  }
  const $async = validators.some((v) => v.$async);
  const $watchTargets = validators.reduce((all, v) => {
    if (!v.$watchTargets) return all;
    return all.concat(v.$watchTargets);
  }, []);
  let $validator = () => false;
  if (validators.length) $validator = $async ? asyncOr(validators) : syncOr(validators);
  return {
    $async,
    $validator,
    $watchTargets
  };
}
function or() {
  return withParams({
    type: "or"
  }, withMessage("The value does not match any of the provided validators", or$1(...arguments)));
}
function syncAnd(validators) {
  return function() {
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    return validators.reduce((valid, fn) => {
      if (!unwrapValidatorResponse(valid)) return valid;
      return unwrapNormalizedValidator(fn).apply(this, args);
    }, true);
  };
}
function asyncAnd(validators) {
  return function() {
    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
      args[_key2] = arguments[_key2];
    }
    return validators.reduce(async (valid, fn) => {
      const r = await valid;
      if (!unwrapValidatorResponse(r)) return r;
      return unwrapNormalizedValidator(fn).apply(this, args);
    }, Promise.resolve(true));
  };
}
function and$1() {
  for (var _len3 = arguments.length, validators = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
    validators[_key3] = arguments[_key3];
  }
  const $async = validators.some((v) => v.$async);
  const $watchTargets = validators.reduce((all, v) => {
    if (!v.$watchTargets) return all;
    return all.concat(v.$watchTargets);
  }, []);
  let $validator = () => false;
  if (validators.length) $validator = $async ? asyncAnd(validators) : syncAnd(validators);
  return {
    $async,
    $validator,
    $watchTargets
  };
}
function and() {
  return withParams({
    type: "and"
  }, withMessage("The value does not match all of the provided validators", and$1(...arguments)));
}
function not$1(validator) {
  return function(value, vm) {
    if (!req(value)) return true;
    const response = unwrapNormalizedValidator(validator).call(this, value, vm);
    if (!isPromise(response)) return !unwrapValidatorResponse(response);
    return response.then((r) => !unwrapValidatorResponse(r));
  };
}
function not(validator) {
  return {
    $validator: not$1(validator),
    $message: `The value does not match the provided validator`,
    $params: {
      type: "not"
    }
  };
}
function minValue$1(min) {
  return (value) => !req(value) || (!/\s/.test(value) || value instanceof Date) && +value >= +unref(min);
}
function minValue(min) {
  return {
    $validator: minValue$1(min),
    $message: (_ref) => {
      let {
        $params
      } = _ref;
      return `The minimum value allowed is ${$params.min}`;
    },
    $params: {
      min,
      type: "minValue"
    }
  };
}
function maxValue$1(max) {
  return (value) => !req(value) || (!/\s/.test(value) || value instanceof Date) && +value <= +unref(max);
}
var maxValue = (max) => ({
  $validator: maxValue$1(max),
  $message: (_ref) => {
    let {
      $params
    } = _ref;
    return `The maximum value allowed is ${$params.max}`;
  },
  $params: {
    max,
    type: "maxValue"
  }
});
var integer$1 = regex(/(^[0-9]*$)|(^-[0-9]+$)/);
var integer = {
  $validator: integer$1,
  $message: "Value is not an integer",
  $params: {
    type: "integer"
  }
};
var decimal$1 = regex(/^[-]?\d*(\.\d+)?$/);
var decimal = {
  $validator: decimal$1,
  $message: "Value must be decimal",
  $params: {
    type: "decimal"
  }
};
function createI18nMessage(_ref) {
  let {
    t,
    messagePath = (_ref2) => {
      let {
        $validator
      } = _ref2;
      return `validations.${$validator}`;
    },
    messageParams = (params) => params
  } = _ref;
  return function withI18nMessage(validator) {
    let {
      withArguments = false,
      messagePath: localMessagePath = messagePath,
      messageParams: localMessageParams = messageParams
    } = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    function message(props) {
      return t(localMessagePath(props), localMessageParams(_objectSpread2({
        model: props.$model,
        property: props.$property,
        pending: props.$pending,
        invalid: props.$invalid,
        response: props.$response,
        validator: props.$validator,
        propertyPath: props.$propertyPath
      }, props.$params)));
    }
    if (withArguments && typeof validator === "function") {
      return function() {
        return withMessage(message, validator(...arguments));
      };
    }
    return withMessage(message, validator);
  };
}
export {
  alpha,
  alphaNum,
  and,
  between,
  createI18nMessage,
  decimal,
  email,
  common as helpers,
  integer,
  ipAddress,
  macAddress,
  maxLength,
  maxValue,
  minLength,
  minValue,
  not,
  numeric,
  or,
  required,
  requiredIf,
  requiredUnless,
  sameAs,
  url
};
//# sourceMappingURL=@vuelidate_validators.js.map
