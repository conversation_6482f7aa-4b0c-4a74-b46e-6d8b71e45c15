<template>
  <div
    class="doc-card p-3 surface-card border-round-lg transition-all transition-duration-300"
    :class="{ 'highlight': isHighlighted }"
    @contextmenu.prevent
  >
    <!-- 卡片头部 -->
    <div class="flex align-items-start gap-2 sm:gap-3">
      <!-- 文档图标 -->
      <div class="doc-icon flex-shrink-0 flex align-items-center justify-content-center border-round">
        <img 
          v-if="doc.icon_url" 
          :src="doc.icon_url" 
          :alt="doc.title"
          class="doc-favicon"
          @error="handleImageError"
        />
        <i v-else :class="getDocIcon(doc.url)" class="text-2xl text-primary"></i>
      </div>

      <!-- 标题和URL -->
      <div class="flex-grow-1 min-w-0">
        <h3 
          class="text-sm sm:text-base font-semibold m-0 mb-1 text-900 line-clamp-1 cursor-pointer hover:text-primary transition-colors transition-duration-200"
          @click="handleDocClick"
          v-tooltip="'点击访问文档'"
        >
          {{ doc.title || '未命名文档' }}
        </h3>
        <div 
          class="url-container p-1 border-round bg-primary-50 flex align-items-center gap-2 cursor-pointer hover:bg-primary-100 transition-colors transition-duration-200"
          @click="handleDocClick"
          v-tooltip="'点击访问链接'"
        >
          <i class="pi pi-link text-primary-700 text-xs sm:text-sm"></i>
          <span class="text-xs sm:text-sm text-600 line-clamp-1 flex-grow-1">{{ formatUrl(doc.url) }}</span>
        </div>
      </div>

      <!-- 更多操作按钮 -->
      <Button
        icon="pi pi-ellipsis-v"
        text
        rounded
        aria-label="更多操作"
        class="p-button-text flex-shrink-0 action-button"
        @click="showMenu($event)"
        v-tooltip.left="'更多操作'"
      />
    </div>

    <!-- 卡片内容 -->
    <div class="card-content mt-2 sm:mt-3">
      <!-- 时间信息 -->
      <div class="flex flex-wrap gap-2 sm:gap-3 text-xs sm:text-sm text-500">
        <span class="flex align-items-center gap-1">
          <i class="pi pi-calendar"></i>
          {{ formatDate(doc.created_at) }}
        </span>
        <span class="flex align-items-center gap-1">
          <i class="pi pi-eye"></i>
          <span>{{ doc.view_count || 0 }} 次访问</span>
        </span>
      </div>

      <!-- 文档描述 -->
      <div v-if="doc.description" class="mt-2 sm:mt-3">
        <p class="text-xs sm:text-sm text-700 m-0 line-clamp-2 p-2 surface-ground border-round">
          <i class="pi pi-info-circle mr-2 text-primary"></i>
          {{ doc.description }}
        </p>
      </div>
    </div>
  </div>

  <!-- 右键菜单 -->
  <Menu 
    ref="menu" 
    :model="menuItems" 
    :popup="true"
    class="surface-card shadow-md border-round-lg"
  />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { MenuItem } from 'primevue/menuitem'
import { useToast } from 'primevue/usetoast'
import Menu from 'primevue/menu'

const props = defineProps<{
  doc: {
    id: string
    title: string
    url: string
    description?: string
    icon_url?: string
    created_at: string
    view_count: number
  }
}>()

const emit = defineEmits<{
  (e: 'edit', doc: {
    id: string
    title: string
    url: string
    description?: string
    icon_url?: string
    created_at: string
    view_count: number
  }): void
  (e: 'delete', doc: {
    id: string
    title: string
    url: string
    description?: string
    icon_url?: string
    created_at: string
    view_count: number
  }): void
  (e: 'refresh', doc: {
    id: string
    title: string
    url: string
    description?: string
    icon_url?: string
    created_at: string
    view_count: number
  }): void
  (e: 'view', doc: {
    id: string
    title: string
    url: string
    description?: string
    icon_url?: string
    created_at: string
    view_count: number
  }): void
}>()

const toast = useToast()
const menu = ref<InstanceType<typeof Menu>>()
const isRefreshing = ref(false)
const isHighlighted = ref(false)

// 菜单项配置
const menuItems = computed<MenuItem[]>(() => [
  {
    label: '在新标签页打开',
    icon: 'pi pi-external-link',
    command: handleDocClick
  },
  {
    label: '编辑文档',
    icon: 'pi pi-pencil',
    command: () => emit('edit', props.doc)
  },
  {
    label: '刷新元数据',
    icon: 'pi pi-refresh',
    command: handleRefresh
  },
  {
    separator: true
  },
  {
    label: '删除文档',
    icon: 'pi pi-trash',
    class: 'p-error',
    command: () => emit('delete', props.doc)
  }
])

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

// 处理文档点击
const handleDocClick = () => {
  emit('view', props.doc)  // 先触发访问事件
  window.open(props.doc.url, '_blank')  // 然后打开链接
}

// 处理刷新
const handleRefresh = async () => {
  if (isRefreshing.value) return
  isRefreshing.value = true
  try {
    await emit('refresh', props.doc)
  } finally {
    isRefreshing.value = false
  }
}

// 格式化URL显示
const formatUrl = (url: string) => {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname + urlObj.pathname
  } catch {
    return url
  }
}

// 格式化日期
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// 获取文档图标
const getDocIcon = (url: string) => {
  try {
    const urlObj = new URL(url)
    const hostname = urlObj.hostname.toLowerCase()
    
    // 根据域名返回不同的图标
    if (hostname.includes('github')) return 'pi pi-github'
    if (hostname.includes('gitlab')) return 'pi pi-code'
    if (hostname.includes('docs') || hostname.includes('documentation')) return 'pi pi-book'
    
    return 'pi pi-file'
  } catch {
    return 'pi pi-file'
  }
}

// 显示菜单
const showMenu = (event: MouseEvent) => {
  menu.value?.toggle(event)
}
</script>

<style scoped>
.doc-card {
  border: 1px solid var(--surface-border);
  background: var(--surface-card);
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.doc-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.doc-icon {
  width: 32px;
  height: 32px;
  min-width: 32px;
  background: var(--primary-50);
}

@media screen and (min-width: 640px) {
  .doc-icon {
    width: 40px;
    height: 40px;
    min-width: 40px;
  }
}

.doc-favicon {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

@media screen and (min-width: 640px) {
  .doc-favicon {
    width: 24px;
    height: 24px;
  }
}

.action-button {
  width: 2rem;
  height: 2rem;
  transition: all 0.2s ease;
  color: var(--text-color-secondary);
}

@media screen and (min-width: 640px) {
  .action-button {
    width: 2.5rem;
    height: 2.5rem;
  }
}

.action-button:hover {
  transform: scale(1.1);
  background-color: var(--surface-hover) !important;
  color: var(--primary-color);
}

.action-button:active {
  transform: scale(0.95);
}

/* 卡片高亮效果 */
.highlight {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 1px var(--primary-color);
}

/* URL容器样式 */
.url-container {
  max-width: 100%;
  overflow: hidden;
}

/* 文本截断 */
.line-clamp-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
