"""太湖认证路由 - 按照Node.js代码参数实现"""
from fastapi import APIRouter, HTTPException, Request, Depends
from fastapi.responses import RedirectResponse, HTMLResponse
import httpx
import logging
import json
import base64
import os
from urllib.parse import urlencode
from pydantic import BaseModel
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(tags=["taihu-auth"])

# HTML模板
LOGIN_PAGE_HTML = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录选择</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 100px auto; padding: 20px; }
        .container { background: #f5f5f5; padding: 30px; border-radius: 10px; text-align: center; }
        .button { display: inline-block; padding: 15px 30px; margin: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; font-size: 16px; }
        .button:hover { background: #0056b3; }
        .button.github { background: #333; }
        .button.github:hover { background: #24292e; }
        .button.success { background: #28a745; }
        .button.success:hover { background: #1e7e34; }
        h1 { color: #333; }
        p { color: #666; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 登录选择</h1>
        <p>请选择登录方式：</p>
        <a href="/auth/github" class="button github">🐙 GitHub 登录</a><br>
        <a href="/auth/taihu" class="button">🧪 腾讯太湖测试环境登录</a><br>
        <a href="/auth/taihu-production" class="button success">🏭 腾讯太湖生产环境登录</a>
        <p style="margin-top: 30px; font-size: 14px; color: #888;">
            GitHub登录使用OAuth2.0<br>
            太湖测试环境使用 test-app 配置<br>
            太湖生产环境使用 api-docs 配置
        </p>
    </div>
</body>
</html>
"""

PROFILE_PAGE_HTML = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户资料</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .container { background: #f5f5f5; padding: 30px; border-radius: 10px; }
        .user-info { background: white; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .button { display: inline-block; padding: 10px 20px; margin: 10px 5px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .button:hover { background: #0056b3; }
        .button.danger { background: #dc3545; }
        .button.danger:hover { background: #c82333; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        h1 { color: #333; }
        .welcome { color: #28a745; font-size: 18px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>👤 用户资料</h1>
        <div class="welcome">✅ 登录成功！你好，{username}！</div>
        <div class="user-info">
            <h3>用户信息：</h3>
            <pre>{user_json}</pre>
        </div>
        <a href="/" class="button">🏠 返回首页</a>
        <a href="/auth/logout" class="button danger">🚪 注销</a>
    </div>
</body>
</html>
"""

# 配置类 - 按照Node.js代码参数
class GitHubConfig:
    """GitHub配置类"""
    def __init__(self):
        self.client_id = "Ov23li6YPPKLySKlalx7"
        self.client_secret = "a83bbda7f7b9eabab63f5b6592e674fc1bf9286c"
        self.auth_url = "https://github.com/login/oauth/authorize"
        self.token_url = "https://github.com/login/oauth/access_token"
        self.userinfo_url = "https://api.github.com/user"
        self.redirect_uri = "http://localhost:8001/auth/github/callback"

class TaihuConfig:
    """太湖配置类 - 按照Node.js代码参数"""
    def __init__(self, env="development"):
        self.env = env
        if env == "production":
            # 太湖生产环境配置
            self.client_id = "api-docs"
            self.client_secret = "WNEK28UBJHHMN0D81GMPBHAWWZ4GEL2U"
            self.issuer = "https://tai.it.tencent.com/api/auth-center/oauth2/"
            self.auth_url = "https://tai.it.tencent.com/api/auth-center/oauth2/authorize"
            self.token_url = "https://tai.it.tencent.com/api/auth-center/oauth2/token"
            self.userinfo_url = "https://tai.it.tencent.com/api/auth-center/oauth2/userinfo"
            self.redirect_uri = "https://api-docs.woa.com/auth/tencent/callback"
            self.scope = ["openid", "profile"]
        else:
            # 太湖测试环境配置
            self.client_id = "test-app"
            self.client_secret = "ilX3uqWyJRbK"
            self.issuer = "https://test-odc.it.woa.com/api/auth-center/oauth2/"
            self.auth_url = "https://test-odc.it.woa.com/api/auth-center/oauth2/authorize"
            self.token_url = "https://test-odc.it.woa.com/api/auth-center/oauth2/token"
            self.userinfo_url = "https://test-odc.it.woa.com/api/auth-center/oauth2/userinfo"
            self.redirect_uri = "http://localhost:8001/auth/taihu/callback"  # 修正：应该是taihu而不是github
            self.scope = ["openid", "profile"]

class UserInfo(BaseModel):
    """用户信息模型"""
    sub: str
    name: str = ""
    email: str = ""
    username: str = ""
    roles: list = []
    permissions: list = []

# 路由定义
@router.get("/", response_class=HTMLResponse)
async def root():
    """根路径 - 显示登录页面"""
    return LOGIN_PAGE_HTML

@router.get("/login", response_class=HTMLResponse)
async def login_page():
    """登录页面"""
    return LOGIN_PAGE_HTML

@router.get("/github")
async def github_login(request: Request):
    """GitHub登录"""
    config = GitHubConfig()
    
    # 生成state参数
    state_data = {"redirect": "/", "timestamp": **********, "provider": "github"}
    encoded_state = base64.b64encode(json.dumps(state_data).encode()).decode()
    
    # 构建GitHub授权URL
    params = {
        'client_id': config.client_id,
        'redirect_uri': config.redirect_uri,
        'scope': 'user:email',
        'state': encoded_state
    }
    
    redirect_url = f"{config.auth_url}?{urlencode(params)}"
    logger.info(f"Redirecting to GitHub: {redirect_url}")
    
    return RedirectResponse(url=redirect_url)

@router.get("/taihu")
async def taihu_login(request: Request):
    """腾讯太湖测试环境登录"""
    config = TaihuConfig("development")
    
    # 生成state参数
    state_data = {"redirect": "/", "timestamp": **********, "env": "development"}
    encoded_state = base64.b64encode(json.dumps(state_data).encode()).decode()
    
    # 构建授权URL
    params = {
        'client_id': config.client_id,
        'response_type': 'code',
        'scope': ' '.join(config.scope),
        'state': encoded_state,
        'redirect_uri': config.redirect_uri
    }
    
    redirect_url = f"{config.auth_url}?{urlencode(params)}"
    logger.info(f"Redirecting to Taihu test environment: {redirect_url}")
    
    return RedirectResponse(url=redirect_url)

@router.get("/taihu-production")
async def taihu_production_login(request: Request):
    """腾讯太湖生产环境登录"""
    config = TaihuConfig("production")
    
    # 生成state参数
    state_data = {"redirect": "/", "timestamp": **********, "env": "production"}
    encoded_state = base64.b64encode(json.dumps(state_data).encode()).decode()
    
    # 构建授权URL
    params = {
        'client_id': config.client_id,
        'response_type': 'code',
        'scope': ' '.join(config.scope),
        'state': encoded_state,
        'redirect_uri': config.redirect_uri
    }
    
    redirect_url = f"{config.auth_url}?{urlencode(params)}"
    logger.info(f"Redirecting to Taihu production environment: {redirect_url}")

    return RedirectResponse(url=redirect_url)

# 回调处理
@router.get("/github/callback")
async def github_callback(request: Request):
    """GitHub回调处理"""
    try:
        code = request.query_params.get("code")
        state = request.query_params.get("state")
        error = request.query_params.get("error")

        if error:
            logger.error(f"GitHub OAuth error: {error}")
            return RedirectResponse(url="/auth/login")

        if not code or not state:
            logger.error("Missing code or state parameter")
            return RedirectResponse(url="/auth/login")

        config = GitHubConfig()

        logger.info(f"Processing GitHub callback with code: {code[:10]}...")

        # 使用授权码换取访问令牌
        async with httpx.AsyncClient() as client:
            token_response = await client.post(
                config.token_url,
                data={
                    "client_id": config.client_id,
                    "client_secret": config.client_secret,
                    "code": code
                },
                headers={"Accept": "application/json"}
            )
            token_response.raise_for_status()
            token_data = token_response.json()

            access_token = token_data.get("access_token")
            if not access_token:
                logger.error(f"Missing access_token in GitHub response: {token_data}")
                return RedirectResponse(url="/auth/login")

            # 获取用户信息
            userinfo_response = await client.get(
                config.userinfo_url,
                headers={"Authorization": f"Bearer {access_token}"}
            )
            userinfo_response.raise_for_status()
            user_info = userinfo_response.json()

            logger.info(f"Got GitHub user info: {user_info.get('login', 'unknown')}")

            # 保存用户信息到session
            request.session["user"] = user_info
            request.session["access_token"] = access_token
            request.session["provider"] = "github"

            # 重定向到profile页面
            return RedirectResponse(url="/auth/profile")

    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error during GitHub callback: {e.response.status_code} - {e.response.text}")
        return RedirectResponse(url="/auth/login")
    except Exception as e:
        logger.error(f"Unexpected error during GitHub callback: {str(e)}")
        return RedirectResponse(url="/auth/login")

@router.get("/taihu/callback")
async def taihu_callback(request: Request):
    """腾讯太湖测试环境回调"""
    return await handle_taihu_callback(request, "development")

@router.get("/taihu-production/callback")
async def taihu_production_callback(request: Request):
    """腾讯太湖生产环境回调"""
    return await handle_taihu_callback(request, "production")

@router.get("/tencent/callback")
async def tencent_callback(request: Request):
    """兼容旧的回调地址 - 生产环境"""
    return await handle_taihu_callback(request, "production")

async def handle_taihu_callback(request: Request, env: str):
    """太湖认证回调处理 - 支持测试和生产环境"""
    try:
        code = request.query_params.get("code")
        state = request.query_params.get("state")
        error = request.query_params.get("error")

        if error:
            logger.error(f"Taihu OAuth error: {error}")
            return RedirectResponse(url="/auth/login")

        if not code or not state:
            logger.error("Missing code or state parameter")
            return RedirectResponse(url="/auth/login")

        # 解析state参数确定环境
        try:
            state_data = json.loads(base64.b64decode(state).decode('utf-8'))
            env = state_data.get('env', env)
            redirect_path = state_data.get('redirect', '/')
        except Exception as e:
            logger.warning(f"Failed to parse state parameter: {e}")
            redirect_path = '/'

        # 根据环境选择配置
        config = TaihuConfig(env)

        logger.info(f"Processing Taihu {env} callback with code: {code[:10]}...")

        # 使用授权码换取访问令牌
        async with httpx.AsyncClient() as client:
            token_response = await client.post(
                config.token_url,
                data={
                    "grant_type": "authorization_code",
                    "code": code,
                    "client_id": config.client_id,
                    "client_secret": config.client_secret,
                    "redirect_uri": config.redirect_uri
                },
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            token_response.raise_for_status()
            token_data = token_response.json()

            logger.info(f"Token exchange successful for Taihu {env} environment")

            access_token = token_data.get("access_token")
            if not access_token:
                logger.error(f"Missing access_token in response: {token_data}")
                return RedirectResponse(url="/auth/login")

            # 获取用户信息
            userinfo_response = await client.get(
                config.userinfo_url,
                headers={"Authorization": f"Bearer {access_token}"}
            )
            userinfo_response.raise_for_status()
            user_info = userinfo_response.json()

            logger.info(f"Got Taihu {env} user info: {user_info.get('sub', 'unknown')}")

            # 保存用户信息到session
            request.session["user"] = user_info
            request.session["access_token"] = access_token
            request.session["env"] = env
            request.session["provider"] = "taihu"

            # 重定向到profile页面
            return RedirectResponse(url="/auth/profile")

    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error during Taihu {env} callback: {e.response.status_code} - {e.response.text}")
        return RedirectResponse(url="/auth/login")
    except Exception as e:
        logger.error(f"Unexpected error during Taihu {env} callback: {str(e)}")
        return RedirectResponse(url="/auth/login")

# Profile和注销功能
@router.get("/profile", response_class=HTMLResponse)
async def profile(request: Request):
    """用户资料页面 - 类似Node.js示例中的profile路由"""
    try:
        # 检查session中是否有用户信息 - 类似req.isAuthenticated()
        user = request.session.get("user")
        if not user:
            return RedirectResponse(url="/auth/login")

        # 获取用户名 - 支持GitHub和太湖用户
        provider = request.session.get("provider", "taihu")
        if provider == "github":
            username = user.get("login") or user.get("name") or "GitHub用户"
        else:
            username = user.get("name") or user.get("preferred_username") or user.get("sub", "太湖用户")

        # 格式化用户信息JSON
        user_json = json.dumps(user, indent=2, ensure_ascii=False)

        # 返回profile页面 - 类似Node.js中的res.send()
        return PROFILE_PAGE_HTML.format(username=username, user_json=user_json)

    except Exception as e:
        logger.error(f"Error in profile route: {str(e)}")
        return RedirectResponse(url="/auth/login")

@router.get("/logout")
async def logout(request: Request):
    """注销用户 - 类似Node.js中的req.logout()"""
    try:
        # 清除session - 类似req.logout()
        request.session.clear()

        # 重定向到首页 - 类似res.redirect('/')
        return RedirectResponse(url="/auth/login")

    except Exception as e:
        logger.error(f"Error during logout: {str(e)}")
        return RedirectResponse(url="/auth/login")

# API端点
@router.get("/user")
async def get_current_user(request: Request):
    """获取当前登录用户信息 - API端点"""
    try:
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            # 尝试从session获取
            user = request.session.get("user")
            if not user:
                raise HTTPException(status_code=401, detail="Not authenticated")
            return user

        # 如果有Authorization header，验证token
        access_token = auth_header.split(" ")[1]
        provider = request.session.get("provider", "taihu")
        env = request.session.get("env", "development")

        if provider == "github":
            config = GitHubConfig()
            userinfo_url = config.userinfo_url
        else:
            config = TaihuConfig(env)
            userinfo_url = config.userinfo_url

        async with httpx.AsyncClient() as client:
            response = await client.get(
                userinfo_url,
                headers={"Authorization": f"Bearer {access_token}"}
            )
            response.raise_for_status()
            user_data = response.json()

            return user_data

    except httpx.HTTPStatusError as e:
        logger.error(f"Failed to get user info: {e.response.status_code} - {e.response.text}")
        raise HTTPException(status_code=401, detail="Invalid or expired token")
    except Exception as e:
        logger.error(f"Error getting user info: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve user information")
