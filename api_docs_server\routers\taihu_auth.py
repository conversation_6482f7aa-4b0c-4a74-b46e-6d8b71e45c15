"""太湖认证路由 - 支持测试和生产环境"""
from fastapi import APIRouter, HTTPException, Request, Depends
from fastapi.responses import RedirectResponse
import httpx
import logging
import json
import base64
import os
from urllib.parse import urlencode
from pydantic import BaseModel
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(tags=["taihu-auth"])

class TaihuConfig:
    """太湖配置类"""
    def __init__(self, env="development"):
        self.env = env
        if env == "production":
            self.client_id = os.getenv("TAIHU_CLIENT_ID", "api-docs")
            self.client_secret = os.getenv("TAIHU_CLIENT_SECRET", "WNEK28UBJHHMN0D81GMPBHAWWZ4GEL2U")
            self.auth_url = os.getenv("TAIHU_AUTH_URL", "https://tai.it.tencent.com/api/auth-center/oauth2")
            self.redirect_uri = os.getenv("TAIHU_REDIRECT_URI", "https://api-docs.woa.com/auth/tencent/callback")
            self.userinfo_url = os.getenv("TAIHU_USERINFO_URL", "https://tai.it.tencent.com/api/auth-center/oauth2/userinfo")
            self.token_url = os.getenv("TAIHU_TOKEN_URL", "https://tai.it.tencent.com/api/auth-center/oauth2/token")
            self.logout_url = os.getenv("TAIHU_LOGOUT_URL", "https://tai.it.tencent.com/logout")
            self.frontend_url = os.getenv("FRONTEND_URL", "https://api-docs.woa.com")
        else:
            self.client_id = os.getenv("TAIHU_CLIENT_ID", "test-app")
            self.client_secret = os.getenv("TAIHU_CLIENT_SECRET", "ilX3uqWyJRbK")
            self.auth_url = os.getenv("TAIHU_AUTH_URL", "https://test-odc.it.woa.com/api/auth-center/oauth2")
            self.redirect_uri = os.getenv("TAIHU_REDIRECT_URI", "http://127.0.0.1:8001/auth/tencent/callback")
            self.userinfo_url = os.getenv("TAIHU_USERINFO_URL", "https://test-odc.it.woa.com/api/auth-center/oauth2/userinfo")
            self.token_url = os.getenv("TAIHU_TOKEN_URL", "https://test-odc.it.woa.com/api/auth-center/oauth2/token")
            self.logout_url = os.getenv("TAIHU_LOGOUT_URL", "https://test-odc.it.woa.com/logout")
            self.frontend_url = os.getenv("FRONTEND_URL", "http://localhost:3005")

class UserInfo(BaseModel):
    """用户信息模型"""
    sub: str
    name: str = ""
    email: str = ""
    username: str = ""
    roles: list = []
    permissions: list = []

@router.get("/taihu")
async def taihu_login(request: Request):
    """太湖测试环境登录"""
    config = TaihuConfig("development")
    
    # 生成state参数
    state_data = {"redirect": "/", "timestamp": 1234567890, "env": "development"}
    encoded_state = base64.b64encode(json.dumps(state_data).encode()).decode()
    
    # 构建授权URL
    auth_url = f"{config.auth_url}/authorize"
    params = {
        'client_id': config.client_id,
        'response_type': 'code',
        'scope': 'openid profile',
        'state': encoded_state,
        'redirect_uri': config.redirect_uri
    }
    
    redirect_url = f"{auth_url}?{urlencode(params)}"
    logger.info(f"Redirecting to Taihu test environment: {redirect_url}")
    
    return RedirectResponse(url=redirect_url)

@router.get("/taihu-production")
async def taihu_production_login(request: Request):
    """太湖生产环境登录"""
    config = TaihuConfig("production")
    
    # 生成state参数
    state_data = {"redirect": "/", "timestamp": 1234567890, "env": "production"}
    encoded_state = base64.b64encode(json.dumps(state_data).encode()).decode()
    
    # 构建授权URL
    auth_url = f"{config.auth_url}/authorize"
    params = {
        'client_id': config.client_id,
        'response_type': 'code',
        'scope': 'openid profile',
        'state': encoded_state,
        'redirect_uri': config.redirect_uri
    }
    
    redirect_url = f"{auth_url}?{urlencode(params)}"
    logger.info(f"Redirecting to Taihu production environment: {redirect_url}")
    
    return RedirectResponse(url=redirect_url)

@router.get("/tencent/callback")
async def tencent_callback(request: Request):
    """太湖认证回调处理 - 支持测试和生产环境"""
    try:
        code = request.query_params.get("code")
        state = request.query_params.get("state")
        error = request.query_params.get("error")
        
        if error:
            logger.error(f"OAuth error: {error}")
            return RedirectResponse(url="http://localhost:3005/#/auth/callback?error=" + error)
        
        if not code or not state:
            logger.error("Missing code or state parameter")
            return RedirectResponse(url="http://localhost:3005/#/auth/callback?error=missing_parameters")

        # 解析state参数确定环境
        try:
            state_data = json.loads(base64.b64decode(state).decode('utf-8'))
            env = state_data.get('env', 'development')
            redirect_path = state_data.get('redirect', '/')
        except Exception as e:
            logger.warning(f"Failed to parse state parameter: {e}")
            env = 'development'
            redirect_path = '/'

        # 根据环境选择配置
        config = TaihuConfig(env)
        
        logger.info(f"Processing callback for {env} environment with code: {code[:10]}...")

        # 使用授权码换取访问令牌
        async with httpx.AsyncClient() as client:
            token_response = await client.post(
                config.token_url,
                data={
                    "grant_type": "authorization_code",
                    "code": code,
                    "client_id": config.client_id,
                    "client_secret": config.client_secret,
                    "redirect_uri": config.redirect_uri
                },
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            token_response.raise_for_status()
            token_data = token_response.json()

            logger.info(f"Token exchange successful for {env} environment")

            access_token = token_data.get("access_token")
            if not access_token:
                logger.error(f"Missing access_token in response: {token_data}")
                return RedirectResponse(url=f"{config.frontend_url}/#/auth/callback?error=missing_access_token")

            # 获取用户信息
            userinfo_response = await client.get(
                config.userinfo_url,
                headers={"Authorization": f"Bearer {access_token}"}
            )
            userinfo_response.raise_for_status()
            user_info = userinfo_response.json()

            logger.info(f"Got user info for {env} environment: {user_info.get('sub', 'unknown')}")

            # 重定向到前端回调页面
            params = {
                "access_token": access_token,
                "user_info": json.dumps(user_info),
                "state": redirect_path,
                "env": env
            }
            
            if token_data.get("refresh_token"):
                params["refresh_token"] = token_data["refresh_token"]
            
            callback_url = f"{config.frontend_url}/#/auth/callback?{urlencode(params)}"
            return RedirectResponse(url=callback_url)

    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error during callback: {e.response.status_code} - {e.response.text}")
        return RedirectResponse(url="http://localhost:3005/#/auth/callback?error=http_error")
    except Exception as e:
        logger.error(f"Unexpected error during callback: {str(e)}")
        return RedirectResponse(url="http://localhost:3005/#/auth/callback?error=unexpected_error")

@router.get("/user")
async def get_current_user(request: Request, env: str = "development"):
    """获取当前登录用户信息"""
    try:
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Missing or invalid authorization header")
        
        access_token = auth_header.split(" ")[1]
        config = TaihuConfig(env)
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                config.userinfo_url,
                headers={"Authorization": f"Bearer {access_token}"}
            )
            response.raise_for_status()
            user_data = response.json()
            
            user_info = UserInfo(
                sub=user_data.get("sub", ""),
                name=user_data.get("name", user_data.get("preferred_username", "")),
                email=user_data.get("email", ""),
                username=user_data.get("preferred_username", ""),
                roles=user_data.get("roles", []),
                permissions=user_data.get("permissions", [])
            )
            
            return user_info.dict()
            
    except httpx.HTTPStatusError as e:
        logger.error(f"Failed to get user info: {e.response.status_code} - {e.response.text}")
        raise HTTPException(status_code=401, detail="Invalid or expired token")
    except Exception as e:
        logger.error(f"Error getting user info: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve user information")

@router.get("/profile")
async def profile(request: Request):
    """用户资料页面 - 类似Node.js示例中的profile路由"""
    try:
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            return RedirectResponse(url="/")
        
        # 这里可以返回HTML页面或JSON数据
        # 为了简化，返回重定向到前端
        return RedirectResponse(url="http://localhost:3005/profile")
        
    except Exception as e:
        logger.error(f"Error in profile route: {str(e)}")
        return RedirectResponse(url="/")

@router.post("/logout")
async def logout(request: Request, env: str = "development"):
    """注销用户"""
    try:
        config = TaihuConfig(env)
        logout_callback_url = f"{config.frontend_url}/login"
        logout_redirect_url = f"{config.logout_url}?url={logout_callback_url}"
        
        return {"logout_url": logout_redirect_url}
        
    except Exception as e:
        logger.error(f"Error during logout: {str(e)}")
        raise HTTPException(status_code=500, detail="Logout failed")
