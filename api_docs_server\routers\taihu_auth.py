"""太湖认证路由 - 支持测试和生产环境"""
from fastapi import APIRouter, HTTPException, Request, Depends
from fastapi.responses import RedirectResponse, HTMLResponse
import httpx
import logging
import json
import base64
import os
from urllib.parse import urlencode
from pydantic import BaseModel
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(tags=["taihu-auth"])

# HTML模板
LOGIN_PAGE_HTML = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>太湖登录</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 100px auto; padding: 20px; }
        .container { background: #f5f5f5; padding: 30px; border-radius: 10px; text-align: center; }
        .button { display: inline-block; padding: 15px 30px; margin: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; font-size: 16px; }
        .button:hover { background: #0056b3; }
        .button.success { background: #28a745; }
        .button.success:hover { background: #1e7e34; }
        h1 { color: #333; }
        p { color: #666; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 太湖登录</h1>
        <p>请选择登录环境：</p>
        <a href="/auth/taihu" class="button">🧪 太湖测试环境登录</a><br>
        <a href="/auth/taihu-production" class="button success">🏭 太湖生产环境登录</a>
        <p style="margin-top: 30px; font-size: 14px; color: #888;">
            测试环境使用 test-app 配置<br>
            生产环境使用 api-docs 配置
        </p>
    </div>
</body>
</html>
"""

PROFILE_PAGE_HTML = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户资料</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .container { background: #f5f5f5; padding: 30px; border-radius: 10px; }
        .user-info { background: white; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .button { display: inline-block; padding: 10px 20px; margin: 10px 5px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .button:hover { background: #0056b3; }
        .button.danger { background: #dc3545; }
        .button.danger:hover { background: #c82333; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        h1 { color: #333; }
        .welcome { color: #28a745; font-size: 18px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>👤 用户资料</h1>
        <div class="welcome">✅ 登录成功！欢迎，{username}！</div>
        <div class="user-info">
            <h3>用户信息：</h3>
            <pre>{user_json}</pre>
        </div>
        <a href="/" class="button">🏠 返回首页</a>
        <a href="/auth/logout" class="button danger">🚪 注销</a>
    </div>
</body>
</html>
"""

class TaihuConfig:
    """太湖配置类"""
    def __init__(self, env="development"):
        self.env = env
        if env == "production":
            self.client_id = "api-docs"
            self.client_secret = "WNEK28UBJHHMN0D81GMPBHAWWZ4GEL2U"
            self.auth_url = "https://tai.it.tencent.com/api/auth-center/oauth2/authorize"
            self.redirect_uri = "https://api-docs.woa.com/auth/tencent/callback"
            self.userinfo_url = "https://tai.it.tencent.com/api/auth-center/oauth2/userinfo"
            self.token_url = "https://tai.it.tencent.com/api/auth-center/oauth2/token"
            self.logout_url = "https://tai.it.tencent.com/logout"
            self.frontend_url = "https://api-docs.woa.com"
        else:
            self.client_id = "test-app"
            self.client_secret = "ilX3uqWyJRbK"
            self.auth_url = "https://test-odc.it.woa.com/api/auth-center/oauth2/authorize"
            self.redirect_uri = "http://localhost:8001/auth/taihu/callback"
            self.userinfo_url = "https://test-odc.it.woa.com/api/auth-center/oauth2/userinfo"
            self.token_url = "https://test-odc.it.woa.com/api/auth-center/oauth2/token"
            self.logout_url = "https://test-odc.it.woa.com/logout"
            self.frontend_url = "http://localhost:8001"

class UserInfo(BaseModel):
    """用户信息模型"""
    sub: str
    name: str = ""
    email: str = ""
    username: str = ""
    roles: list = []
    permissions: list = []

@router.get("/", response_class=HTMLResponse)
async def root():
    """根路径 - 显示登录页面"""
    return LOGIN_PAGE_HTML

@router.get("/login", response_class=HTMLResponse)
async def login_page():
    """登录页面"""
    return LOGIN_PAGE_HTML

@router.get("/taihu")
async def taihu_login(request: Request):
    """太湖测试环境登录"""
    config = TaihuConfig("development")
    
    # 生成state参数
    state_data = {"redirect": "/", "timestamp": 1234567890, "env": "development"}
    encoded_state = base64.b64encode(json.dumps(state_data).encode()).decode()
    
    # 构建授权URL
    auth_url = f"{config.auth_url}/authorize"
    params = {
        'client_id': config.client_id,
        'response_type': 'code',
        'scope': 'openid profile',
        'state': encoded_state,
        'redirect_uri': config.redirect_uri
    }
    
    redirect_url = f"{auth_url}?{urlencode(params)}"
    logger.info(f"Redirecting to Taihu test environment: {redirect_url}")
    
    return RedirectResponse(url=redirect_url)

@router.get("/taihu-production")
async def taihu_production_login(request: Request):
    """太湖生产环境登录"""
    config = TaihuConfig("production")
    
    # 生成state参数
    state_data = {"redirect": "/", "timestamp": 1234567890, "env": "production"}
    encoded_state = base64.b64encode(json.dumps(state_data).encode()).decode()
    
    # 构建授权URL
    auth_url = f"{config.auth_url}/authorize"
    params = {
        'client_id': config.client_id,
        'response_type': 'code',
        'scope': 'openid profile',
        'state': encoded_state,
        'redirect_uri': config.redirect_uri
    }
    
    redirect_url = f"{auth_url}?{urlencode(params)}"
    logger.info(f"Redirecting to Taihu production environment: {redirect_url}")
    
    return RedirectResponse(url=redirect_url)

@router.get("/taihu/callback")
async def taihu_callback(request: Request):
    """太湖测试环境回调"""
    return await handle_callback(request, "development")

@router.get("/taihu-production/callback")
async def taihu_production_callback(request: Request):
    """太湖生产环境回调"""
    return await handle_callback(request, "production")

@router.get("/tencent/callback")
async def tencent_callback(request: Request):
    """兼容旧的回调地址"""
    return await handle_callback(request, "production")

async def handle_callback(request: Request, env: str):
    """太湖认证回调处理 - 支持测试和生产环境"""
    try:
        code = request.query_params.get("code")
        state = request.query_params.get("state")
        error = request.query_params.get("error")
        
        if error:
            logger.error(f"OAuth error: {error}")
            return RedirectResponse(url="http://localhost:3005/#/auth/callback?error=" + error)
        
        if not code or not state:
            logger.error("Missing code or state parameter")
            return RedirectResponse(url="http://localhost:3005/#/auth/callback?error=missing_parameters")

        # 解析state参数确定环境
        try:
            state_data = json.loads(base64.b64decode(state).decode('utf-8'))
            env = state_data.get('env', 'development')
            redirect_path = state_data.get('redirect', '/')
        except Exception as e:
            logger.warning(f"Failed to parse state parameter: {e}")
            env = 'development'
            redirect_path = '/'

        # 根据环境选择配置
        config = TaihuConfig(env)
        
        logger.info(f"Processing callback for {env} environment with code: {code[:10]}...")

        # 使用授权码换取访问令牌
        async with httpx.AsyncClient() as client:
            token_response = await client.post(
                config.token_url,
                data={
                    "grant_type": "authorization_code",
                    "code": code,
                    "client_id": config.client_id,
                    "client_secret": config.client_secret,
                    "redirect_uri": config.redirect_uri
                },
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            token_response.raise_for_status()
            token_data = token_response.json()

            logger.info(f"Token exchange successful for {env} environment")

            access_token = token_data.get("access_token")
            if not access_token:
                logger.error(f"Missing access_token in response: {token_data}")
                return RedirectResponse(url=f"{config.frontend_url}/#/auth/callback?error=missing_access_token")

            # 获取用户信息
            userinfo_response = await client.get(
                config.userinfo_url,
                headers={"Authorization": f"Bearer {access_token}"}
            )
            userinfo_response.raise_for_status()
            user_info = userinfo_response.json()

            logger.info(f"Got user info for {env} environment: {user_info.get('sub', 'unknown')}")

            # 保存用户信息到session
            request.session["user"] = user_info
            request.session["access_token"] = access_token
            request.session["env"] = env

            # 重定向到profile页面
            return RedirectResponse(url="/auth/profile")

    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error during callback: {e.response.status_code} - {e.response.text}")
        return RedirectResponse(url="http://localhost:3005/#/auth/callback?error=http_error")
    except Exception as e:
        logger.error(f"Unexpected error during callback: {str(e)}")
        return RedirectResponse(url="http://localhost:3005/#/auth/callback?error=unexpected_error")

@router.get("/user")
async def get_current_user(request: Request, env: str = "development"):
    """获取当前登录用户信息"""
    try:
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Missing or invalid authorization header")
        
        access_token = auth_header.split(" ")[1]
        config = TaihuConfig(env)
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                config.userinfo_url,
                headers={"Authorization": f"Bearer {access_token}"}
            )
            response.raise_for_status()
            user_data = response.json()
            
            user_info = UserInfo(
                sub=user_data.get("sub", ""),
                name=user_data.get("name", user_data.get("preferred_username", "")),
                email=user_data.get("email", ""),
                username=user_data.get("preferred_username", ""),
                roles=user_data.get("roles", []),
                permissions=user_data.get("permissions", [])
            )
            
            return user_info.dict()
            
    except httpx.HTTPStatusError as e:
        logger.error(f"Failed to get user info: {e.response.status_code} - {e.response.text}")
        raise HTTPException(status_code=401, detail="Invalid or expired token")
    except Exception as e:
        logger.error(f"Error getting user info: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve user information")

@router.get("/profile", response_class=HTMLResponse)
async def profile(request: Request):
    """用户资料页面 - 类似Node.js示例中的profile路由"""
    try:
        # 检查session中是否有用户信息
        user = request.session.get("user")
        if not user:
            return RedirectResponse(url="/auth/login")

        # 获取用户名
        username = user.get("name") or user.get("preferred_username") or user.get("sub", "未知用户")

        # 格式化用户信息JSON
        user_json = json.dumps(user, indent=2, ensure_ascii=False)

        # 返回profile页面
        return PROFILE_PAGE_HTML.format(username=username, user_json=user_json)

    except Exception as e:
        logger.error(f"Error in profile route: {str(e)}")
        return RedirectResponse(url="/auth/login")

@router.get("/logout")
async def logout(request: Request):
    """注销用户"""
    try:
        # 清除session
        request.session.clear()

        # 重定向到登录页面
        return RedirectResponse(url="/auth/login")

    except Exception as e:
        logger.error(f"Error during logout: {str(e)}")
        return RedirectResponse(url="/auth/login")
