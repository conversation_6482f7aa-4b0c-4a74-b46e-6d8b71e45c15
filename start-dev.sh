#!/bin/bash

# 创建一个临时文件来存储进程ID
PIDFILE=".dev-servers.pid"

# 清理函数
cleanup() {
    echo "Stopping all development servers..."
    if [ -f "$PIDFILE" ]; then
        kill $(cat "$PIDFILE") 2>/dev/null
        rm "$PIDFILE"
    fi
    exit 0
}

# 捕获中断信号
trap cleanup SIGINT SIGTERM

# 启动后端
./start-dev-backend.sh & echo $! >> "$PIDFILE"

# 启动前端
cd frontend && ./start-dev.sh & echo $! >> "$PIDFILE"

echo "Starting development servers..."
echo "Backend: http://localhost:8000"
echo "Frontend: http://localhost:80"
echo "Press Ctrl+C to stop all servers"

# 等待任意子进程退出
wait
