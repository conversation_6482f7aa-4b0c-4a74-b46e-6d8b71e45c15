# Build backend
FROM --platform=linux/amd64 python:3.10.12-slim
WORKDIR /app

# Install minimal build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    python3-dev \
    git \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install uv package manager
RUN pip install --no-cache-dir uv

# Install runtime dependencies first (less likely to change)
RUN uv pip install --system --no-cache-dir "uvicorn[standard]>=0.24.0,<0.25.0"

# Copy and install requirements (changes when dependencies change)
COPY requirements.txt .
RUN uv pip install --system --no-cache-dir -r requirements.txt

# Copy application code (changes most frequently)
COPY . .

# Install the package in editable mode
RUN uv pip install --system -e .

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV LOG_LEVEL=debug
ENV PYTHON_VERSION=3.10.12

EXPOSE 8000
CMD ["uvicorn", "api_docs_server.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1", "--log-level", "debug", "--reload", "--timeout-keep-alive", "75"]
