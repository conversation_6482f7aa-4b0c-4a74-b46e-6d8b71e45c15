"""API Documentation models."""
from datetime import datetime
from typing import Optional
from urllib.parse import urlparse
from uuid import UUID

from pydantic import BaseModel, Field, HttpUrl, field_validator
from sqlalchemy import Column, String, Boolean, DateTime, Text, func, Integer
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import declarative_base
import uuid

Base = declarative_base()

class ApiDoc(Base):
    """API文档数据库模型."""
    __tablename__ = "api_docs"

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    url = Column(String(255), nullable=False)
    title = Column(String(255))
    description = Column(Text)
    icon_url = Column(String(255))
    icon_data = Column(Text)
    auto_metadata = Column(Boolean, default=True)
    view_count = Column(Integer, default=0)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    metadata_updated_at = Column(DateTime)

    def __repr__(self):
        """返回对象的字符串表示."""
        return f"<ApiDoc(id={self.id}, url='{self.url}', title='{self.title}')>"

class ApiDocBase(BaseModel):
    """API文档基础模型."""
    url: HttpUrl
    title: Optional[str] = Field(None, description="文档标题")
    description: Optional[str] = Field(None, description="文档描述")
    icon_url: Optional[HttpUrl] = Field(None, description="图标URL")
    icon_data: Optional[str] = Field(None, description="Base64编码的图标数据")
    auto_metadata: bool = Field(True, description="是否自动获取元数据")

    @field_validator('url')
    @classmethod
    def validate_url(cls, v: str) -> str:
        """验证URL格式."""
        parsed = urlparse(str(v))
        if not parsed.scheme or not parsed.netloc:
            raise ValueError('Invalid URL format')
        return str(v)

    class Config:
        from_attributes = True

class ApiDocCreate(ApiDocBase):
    """API文档创建模型."""
    pass

class ApiDocUpdate(BaseModel):
    """API文档更新模型."""
    url: Optional[HttpUrl] = Field(None, description="API文档URL")
    title: Optional[str] = Field(None, description="文档标题")
    description: Optional[str] = Field(None, description="文档描述")
    icon_url: Optional[HttpUrl] = Field(None, description="图标URL")
    icon_data: Optional[str] = Field(None, description="Base64编码的图标数据")
    auto_metadata: Optional[bool] = Field(None, description="是否自动获取元数据")

    @field_validator('url')
    @classmethod
    def validate_url(cls, v: Optional[str]) -> Optional[str]:
        """验证URL格式."""
        if v is None:
            return None
        parsed = urlparse(str(v))
        if not parsed.scheme or not parsed.netloc:
            raise ValueError('Invalid URL format')
        return str(v)

    class Config:
        from_attributes = True

class ApiDocInDB(ApiDocBase):
    """数据库中的API文档模型."""
    id: UUID
    metadata_updated_at: Optional[datetime] = None
    view_count: int = 0
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class ApiDocResponse(BaseModel):
    """API文档响应模型."""
    id: UUID
    url: str
    title: Optional[str] = None
    description: Optional[str] = None
    icon_url: Optional[str] = None
    icon_data: Optional[str] = None
    auto_metadata: bool = True
    view_count: int = 0
    created_at: datetime
    updated_at: datetime
    metadata_updated_at: Optional[datetime] = None

    @field_validator('url')
    @classmethod
    def validate_url(cls, v: str) -> str:
        """验证URL格式."""
        parsed = urlparse(str(v))
        if not parsed.scheme or not parsed.netloc:
            raise ValueError('Invalid URL format')
        return str(v)

    class Config:
        from_attributes = True
