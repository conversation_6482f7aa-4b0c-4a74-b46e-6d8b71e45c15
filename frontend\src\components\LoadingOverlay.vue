<template>
  <Transition name="fade">
    <div v-if="show" class="loading-overlay" :class="{ 'loading-overlay--transparent': transparent }">
      <div class="loading-content">
        <i class="pi pi-spin pi-spinner loading-icon"></i>
        <span v-if="message" class="loading-message">{{ message }}</span>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
defineProps<{
  show: boolean;
  message?: string;
  transparent?: boolean;
}>();
</script>

<style scoped>
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--surface-ground);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-overlay--transparent {
  background: rgba(255, 255, 255, 0.7);
}

.loading-content {
  text-align: center;
  padding: 1rem;
}

.loading-icon {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.loading-message {
  display: block;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
  margin-top: 0.5rem;
}

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
