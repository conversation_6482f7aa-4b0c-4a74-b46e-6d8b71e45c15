import { describe, it, expect, vi, beforeEach } from 'vitest'
import { flushPromises, mount } from '@vue/test-utils'
import ApiDocList from '../../views/ApiDocList.vue'
import axiosInstance from '../../api/axios'
import { ApiDocCard, EmptyState, ApiDocHero } from '../../test/mocks/components'
import { nextTick } from 'vue'
import { ApiDocService } from '../../api/apiService'

vi.mock('../../api/axios', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    interceptors: {
      request: { use: vi.fn() },
      response: { use: vi.fn() }
    }
  },
  API_ENDPOINTS: {
    DOCS: '/api/docs',
    VALIDATE_URL: '/api/docs/url/validate',
    HEALTH: '/api/health'
  }
}))

const mockToast = {
  add: vi.fn()
}

vi.mock('primevue/usetoast', () => ({
  useToast: () => mockToast
}))

describe('ApiDocList', () => {
  const mockDocs = [
    { id: '1', title: 'Doc 1', content: 'Content 1', type: 'swagger', url: 'https://api.example.com/swagger' },
    { id: '2', title: 'Doc 2', content: 'Content 2', type: 'openapi', url: 'https://api.example.com/openapi' }
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(axiosInstance.get).mockResolvedValue({ data: mockDocs })
  })

  const mountComponent = async () => {
    const wrapper = mount(ApiDocList, {
      global: {
        stubs: {
          TransitionGroup: {
            template: '<div class="grid"><slot></slot></div>'
          }
        },
        components: {
          ApiDocCard,
          ApiDocHero,
          EmptyState,
          Button: {
            template: '<button><slot></slot></button>',
            props: ['label', 'icon', 'severity']
          },
          Dialog: {
            template: '<div v-if="visible"><slot></slot></div>',
            props: ['visible']
          },
          ContextMenu: {
            template: '<div class="context-menu"><slot></slot></div>',
            props: ['model']
          }
        },
        mocks: {
          $toast: mockToast
        }
      }
    })

    // 等待组件挂载和初始数据加载
    await flushPromises()
    await nextTick()

    return wrapper
  }

  it('renders properly', async () => {
    const wrapper = await mountComponent()
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.surface-ground').exists()).toBe(true)
  })

  it('shows empty state when no documents', async () => {
    vi.mocked(axiosInstance.get).mockResolvedValueOnce({ data: [] })
    const wrapper = await mountComponent()

    const emptyState = wrapper.findComponent(EmptyState)
    expect(emptyState.exists()).toBe(true)
  })

  it('shows search results when documents exist', async () => {
    const wrapper = await mountComponent()

    // 等待文档加载完成
    await nextTick()

    // 验证 ApiDocHero 组件
    const hero = wrapper.findComponent(ApiDocHero)
    expect(hero.exists()).toBe(true)
    expect(hero.props('totalDocs')).toBe(mockDocs.length)

    // 验证文档卡片
    const docCards = wrapper.findAllComponents(ApiDocCard)
    expect(docCards).toHaveLength(mockDocs.length)

    // 验证每个卡片的属性
    docCards.forEach((card, index) => {
      expect(card.props('doc')).toEqual(mockDocs[index])
    })
  })

  it('handles error when loading docs', async () => {
    vi.spyOn(ApiDocService, 'getDocs').mockRejectedValue(new Error('Failed to fetch'))

    await mountComponent()

    expect(mockToast.add).toHaveBeenCalledWith({
      severity: 'error',
      summary: '错误',
      detail: '获取文档列表失败',
      life: 5000
    })
  })
})
