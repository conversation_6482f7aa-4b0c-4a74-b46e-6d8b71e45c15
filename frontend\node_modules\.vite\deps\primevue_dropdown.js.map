{"version": 3, "sources": ["../../@primevue/src/blank/BlankIcon.vue", "../../@primevue/src/blank/BlankIcon.vue?vue&type=template&id=4a4f308f&lang.js", "../../@primevue/src/search/SearchIcon.vue", "../../@primevue/src/search/SearchIcon.vue?vue&type=template&id=67bdad4e&lang.js", "../../src/iconfield/style/IconFieldStyle.js", "../../src/iconfield/BaseIconField.vue", "../../src/iconfield/IconField.vue", "../../src/iconfield/IconField.vue?vue&type=template&id=e18f8da6&lang.js", "../../src/inputicon/style/InputIconStyle.js", "../../src/inputicon/BaseInputIcon.vue", "../../src/inputicon/InputIcon.vue", "../../src/inputicon/InputIcon.vue?vue&type=template&id=17e7e820&lang.js", "../../src/virtualscroller/style/VirtualScrollerStyle.js", "../../src/virtualscroller/BaseVirtualScroller.vue", "../../src/virtualscroller/VirtualScroller.vue", "../../src/virtualscroller/VirtualScroller.vue?vue&type=template&id=7b311004&lang.js", "../../src/select/style/SelectStyle.js", "../../src/select/BaseSelect.vue", "../../src/select/Select.vue", "../../src/select/Select.vue?vue&type=template&id=641b1f9f&lang.js", "../../src/dropdown/Dropdown.vue"], "sourcesContent": ["<template>\n    <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" v-bind=\"pti()\">\n        <rect width=\"1\" height=\"1\" fill=\"currentColor\" fill-opacity=\"0\" />\n    </svg>\n</template>\n<script>\nimport BaseIcon from '@primevue/icons/baseicon';\n\nexport default {\n    name: 'BlankIcon',\n    extends: BaseIcon\n};\n</script>\n", "<template>\n    <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" v-bind=\"pti()\">\n        <rect width=\"1\" height=\"1\" fill=\"currentColor\" fill-opacity=\"0\" />\n    </svg>\n</template>\n<script>\nimport BaseIcon from '@primevue/icons/baseicon';\n\nexport default {\n    name: 'BlankIcon',\n    extends: BaseIcon\n};\n</script>\n", "<template>\n    <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" v-bind=\"pti()\">\n        <path\n            fill-rule=\"evenodd\"\n            clip-rule=\"evenodd\"\n            d=\"M2.67602 11.0265C3.6661 11.688 4.83011 12.0411 6.02086 12.0411C6.81149 12.0411 7.59438 11.8854 8.32483 11.5828C8.87005 11.357 9.37808 11.0526 9.83317 10.6803L12.9769 13.8241C13.0323 13.8801 13.0983 13.9245 13.171 13.9548C13.2438 13.985 13.3219 14.0003 13.4007 14C13.4795 14.0003 13.5575 13.985 13.6303 13.9548C13.7031 13.9245 13.7691 13.8801 13.8244 13.8241C13.9367 13.7116 13.9998 13.5592 13.9998 13.4003C13.9998 13.2414 13.9367 13.089 13.8244 12.9765L10.6807 9.8328C11.053 9.37773 11.3573 8.86972 11.5831 8.32452C11.8857 7.59408 12.0414 6.81119 12.0414 6.02056C12.0414 4.8298 11.6883 3.66579 11.0268 2.67572C10.3652 1.68564 9.42494 0.913972 8.32483 0.45829C7.22472 0.00260857 6.01418 -0.116618 4.84631 0.115686C3.67844 0.34799 2.60568 0.921393 1.76369 1.76338C0.921698 2.60537 0.348296 3.67813 0.115991 4.84601C-0.116313 6.01388 0.00291375 7.22441 0.458595 8.32452C0.914277 9.42464 1.68595 10.3649 2.67602 11.0265ZM3.35565 2.0158C4.14456 1.48867 5.07206 1.20731 6.02086 1.20731C7.29317 1.20731 8.51338 1.71274 9.41304 2.6124C10.3127 3.51206 10.8181 4.73226 10.8181 6.00457C10.8181 6.95337 10.5368 7.88088 10.0096 8.66978C9.48251 9.45868 8.73328 10.0736 7.85669 10.4367C6.98011 10.7997 6.01554 10.8947 5.08496 10.7096C4.15439 10.5245 3.2996 10.0676 2.62869 9.39674C1.95778 8.72583 1.50089 7.87104 1.31579 6.94046C1.13068 6.00989 1.22568 5.04532 1.58878 4.16874C1.95187 3.29215 2.56675 2.54292 3.35565 2.0158Z\"\n            fill=\"currentColor\"\n        />\n    </svg>\n</template>\n\n<script>\nimport BaseIcon from '@primevue/icons/baseicon';\n\nexport default {\n    name: 'SearchIcon',\n    extends: BaseIcon\n};\n</script>\n", "<template>\n    <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" v-bind=\"pti()\">\n        <path\n            fill-rule=\"evenodd\"\n            clip-rule=\"evenodd\"\n            d=\"M2.67602 11.0265C3.6661 11.688 4.83011 12.0411 6.02086 12.0411C6.81149 12.0411 7.59438 11.8854 8.32483 11.5828C8.87005 11.357 9.37808 11.0526 9.83317 10.6803L12.9769 13.8241C13.0323 13.8801 13.0983 13.9245 13.171 13.9548C13.2438 13.985 13.3219 14.0003 13.4007 14C13.4795 14.0003 13.5575 13.985 13.6303 13.9548C13.7031 13.9245 13.7691 13.8801 13.8244 13.8241C13.9367 13.7116 13.9998 13.5592 13.9998 13.4003C13.9998 13.2414 13.9367 13.089 13.8244 12.9765L10.6807 9.8328C11.053 9.37773 11.3573 8.86972 11.5831 8.32452C11.8857 7.59408 12.0414 6.81119 12.0414 6.02056C12.0414 4.8298 11.6883 3.66579 11.0268 2.67572C10.3652 1.68564 9.42494 0.913972 8.32483 0.45829C7.22472 0.00260857 6.01418 -0.116618 4.84631 0.115686C3.67844 0.34799 2.60568 0.921393 1.76369 1.76338C0.921698 2.60537 0.348296 3.67813 0.115991 4.84601C-0.116313 6.01388 0.00291375 7.22441 0.458595 8.32452C0.914277 9.42464 1.68595 10.3649 2.67602 11.0265ZM3.35565 2.0158C4.14456 1.48867 5.07206 1.20731 6.02086 1.20731C7.29317 1.20731 8.51338 1.71274 9.41304 2.6124C10.3127 3.51206 10.8181 4.73226 10.8181 6.00457C10.8181 6.95337 10.5368 7.88088 10.0096 8.66978C9.48251 9.45868 8.73328 10.0736 7.85669 10.4367C6.98011 10.7997 6.01554 10.8947 5.08496 10.7096C4.15439 10.5245 3.2996 10.0676 2.62869 9.39674C1.95778 8.72583 1.50089 7.87104 1.31579 6.94046C1.13068 6.00989 1.22568 5.04532 1.58878 4.16874C1.95187 3.29215 2.56675 2.54292 3.35565 2.0158Z\"\n            fill=\"currentColor\"\n        />\n    </svg>\n</template>\n\n<script>\nimport BaseIcon from '@primevue/icons/baseicon';\n\nexport default {\n    name: 'SearchIcon',\n    extends: BaseIcon\n};\n</script>\n", "import BaseStyle from '@primevue/core/base/style';\n\nconst theme = ({ dt }) => `\n.p-iconfield {\n    position: relative;\n}\n\n.p-inputicon {\n    position: absolute;\n    top: 50%;\n    margin-top: calc(-1 * (${dt('icon.size')} / 2));\n    color: ${dt('iconfield.icon.color')};\n    line-height: 1;\n}\n\n.p-iconfield .p-inputicon:first-child {\n    inset-inline-start: ${dt('form.field.padding.x')};\n}\n\n.p-iconfield .p-inputicon:last-child {\n    inset-inline-end: ${dt('form.field.padding.x')};\n}\n\n.p-iconfield .p-inputtext:not(:first-child) {\n    padding-inline-start: calc((${dt('form.field.padding.x')} * 2) + ${dt('icon.size')});\n}\n\n.p-iconfield .p-inputtext:not(:last-child) {\n    padding-inline-end: calc((${dt('form.field.padding.x')} * 2) + ${dt('icon.size')});\n}\n\n.p-iconfield:has(.p-inputfield-sm) .p-inputicon {\n    font-size: ${dt('form.field.sm.font.size')};\n    width: ${dt('form.field.sm.font.size')};\n    height: ${dt('form.field.sm.font.size')};\n    margin-top: calc(-1 * (${dt('form.field.sm.font.size')} / 2));\n}\n\n.p-iconfield:has(.p-inputfield-lg) .p-inputicon {\n    font-size: ${dt('form.field.lg.font.size')};\n    width: ${dt('form.field.lg.font.size')};\n    height: ${dt('form.field.lg.font.size')};\n    margin-top: calc(-1 * (${dt('form.field.lg.font.size')} / 2));\n}\n`;\n\nconst classes = {\n    root: 'p-iconfield'\n};\n\nexport default BaseStyle.extend({\n    name: 'iconfield',\n    theme,\n    classes\n});\n", "<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport IconFieldStyle from 'primevue/iconfield/style';\n\nexport default {\n    name: 'BaseIconField',\n    extends: BaseComponent,\n    style: IconFieldStyle,\n    provide() {\n        return {\n            $pcIconField: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" v-bind=\"ptmi('root')\">\n        <slot />\n    </div>\n</template>\n\n<script>\nimport BaseIconField from './BaseIconField.vue';\n\nexport default {\n    name: 'IconField',\n    extends: BaseIconField,\n    inheritAttrs: false\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" v-bind=\"ptmi('root')\">\n        <slot />\n    </div>\n</template>\n\n<script>\nimport BaseIconField from './BaseIconField.vue';\n\nexport default {\n    name: 'IconField',\n    extends: BaseIconField,\n    inheritAttrs: false\n};\n</script>\n", "import BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: 'p-inputicon'\n};\n\nexport default BaseStyle.extend({\n    name: 'inputicon',\n    classes\n});\n", "<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport InputIconStyle from 'primevue/inputicon/style';\n\nexport default {\n    name: 'BaseInputIcon',\n    extends: BaseComponent,\n    style: InputIconStyle,\n    props: {\n        class: null\n    },\n    provide() {\n        return {\n            $pcInputIcon: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <span :class=\"containerClass\" v-bind=\"ptmi('root')\">\n        <slot />\n    </span>\n</template>\n\n<script>\nimport BaseInputIcon from './BaseInputIcon.vue';\n\nexport default {\n    name: 'InputIcon',\n    extends: BaseInputIcon,\n    inheritAttrs: false,\n    computed: {\n        containerClass() {\n            return [this.cx('root'), this.class];\n        }\n    }\n};\n</script>\n", "<template>\n    <span :class=\"containerClass\" v-bind=\"ptmi('root')\">\n        <slot />\n    </span>\n</template>\n\n<script>\nimport BaseInputIcon from './BaseInputIcon.vue';\n\nexport default {\n    name: 'InputIcon',\n    extends: BaseInputIcon,\n    inheritAttrs: false,\n    computed: {\n        containerClass() {\n            return [this.cx('root'), this.class];\n        }\n    }\n};\n</script>\n", "import BaseStyle from '@primevue/core/base/style';\n\nconst theme = ({ dt }) => `\n.p-virtualscroller-loader {\n    background: ${dt('virtualscroller.loader.mask.background')};\n    color: ${dt('virtualscroller.loader.mask.color')};\n}\n\n.p-virtualscroller-loading-icon {\n    font-size: ${dt('virtualscroller.loader.icon.size')};\n    width: ${dt('virtualscroller.loader.icon.size')};\n    height: ${dt('virtualscroller.loader.icon.size')};\n}\n`;\n\nconst css = `\n.p-virtualscroller {\n    position: relative;\n    overflow: auto;\n    contain: strict;\n    transform: translateZ(0);\n    will-change: scroll-position;\n    outline: 0 none;\n}\n\n.p-virtualscroller-content {\n    position: absolute;\n    top: 0;\n    left: 0;\n    min-height: 100%;\n    min-width: 100%;\n    will-change: transform;\n}\n\n.p-virtualscroller-spacer {\n    position: absolute;\n    top: 0;\n    left: 0;\n    height: 1px;\n    width: 1px;\n    transform-origin: 0 0;\n    pointer-events: none;\n}\n\n.p-virtualscroller-loader {\n    position: sticky;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n}\n\n.p-virtualscroller-loader-mask {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n\n.p-virtualscroller-horizontal > .p-virtualscroller-content {\n    display: flex;\n}\n\n.p-virtualscroller-inline .p-virtualscroller-content {\n    position: static;\n}\n`;\n\nexport default BaseStyle.extend({\n    name: 'virtualscroller',\n    css,\n    theme\n});\n", "<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport VirtualScrollerStyle from 'primevue/virtualscroller/style';\n\nexport default {\n    name: 'BaseVirtualScroller',\n    extends: BaseComponent,\n    props: {\n        id: {\n            type: String,\n            default: null\n        },\n        style: null,\n        class: null,\n        items: {\n            type: Array,\n            default: null\n        },\n        itemSize: {\n            type: [Number, Array],\n            default: 0\n        },\n        scrollHeight: null,\n        scrollWidth: null,\n        orientation: {\n            type: String,\n            default: 'vertical'\n        },\n        numToleratedItems: {\n            type: Number,\n            default: null\n        },\n        delay: {\n            type: Number,\n            default: 0\n        },\n        resizeDelay: {\n            type: Number,\n            default: 10\n        },\n        lazy: {\n            type: Boolean,\n            default: false\n        },\n        disabled: {\n            type: Boolean,\n            default: false\n        },\n        loaderDisabled: {\n            type: Boolean,\n            default: false\n        },\n        columns: {\n            type: Array,\n            default: null\n        },\n        loading: {\n            type: Boolean,\n            default: false\n        },\n        showSpacer: {\n            type: Boolean,\n            default: true\n        },\n        showLoader: {\n            type: <PERSON><PERSON>an,\n            default: false\n        },\n        tabindex: {\n            type: Number,\n            default: 0\n        },\n        inline: {\n            type: Boolean,\n            default: false\n        },\n        step: {\n            type: Number,\n            default: 0\n        },\n        appendOnly: {\n            type: Boolean,\n            default: false\n        },\n        autoSize: {\n            type: Boolean,\n            default: false\n        }\n    },\n    style: VirtualScrollerStyle,\n    provide() {\n        return {\n            $pcVirtualScroller: this,\n            $parentInstance: this\n        };\n    },\n    beforeMount() {\n        VirtualScrollerStyle.loadCSS({ nonce: this.$primevueConfig?.csp?.nonce });\n    }\n};\n</script>\n", "<template>\n    <template v-if=\"!disabled\">\n        <div :ref=\"elementRef\" :class=\"containerClass\" :tabindex=\"tabindex\" :style=\"style\" @scroll=\"onScroll\" v-bind=\"ptmi('root')\">\n            <slot\n                name=\"content\"\n                :styleClass=\"contentClass\"\n                :items=\"loadedItems\"\n                :getItemOptions=\"getOptions\"\n                :loading=\"d_loading\"\n                :getLoaderOptions=\"getLoaderOptions\"\n                :itemSize=\"itemSize\"\n                :rows=\"loadedRows\"\n                :columns=\"loadedColumns\"\n                :contentRef=\"contentRef\"\n                :spacerStyle=\"spacerStyle\"\n                :contentStyle=\"contentStyle\"\n                :vertical=\"isVertical()\"\n                :horizontal=\"isHorizontal()\"\n                :both=\"isBoth()\"\n            >\n                <div :ref=\"contentRef\" :class=\"contentClass\" :style=\"contentStyle\" v-bind=\"ptm('content')\">\n                    <template v-for=\"(item, index) of loadedItems\" :key=\"index\">\n                        <slot name=\"item\" :item=\"item\" :options=\"getOptions(index)\"></slot>\n                    </template>\n                </div>\n            </slot>\n            <div v-if=\"showSpacer\" class=\"p-virtualscroller-spacer\" :style=\"spacerStyle\" v-bind=\"ptm('spacer')\"></div>\n            <div v-if=\"!loaderDisabled && showLoader && d_loading\" :class=\"loaderClass\" v-bind=\"ptm('loader')\">\n                <template v-if=\"$slots && $slots.loader\">\n                    <template v-for=\"(_, index) of loaderArr\" :key=\"index\">\n                        <slot name=\"loader\" :options=\"getLoaderOptions(index, isBoth() && { numCols: d_numItemsInViewport.cols })\"></slot>\n                    </template>\n                </template>\n                <slot name=\"loadingicon\">\n                    <SpinnerIcon spin class=\"p-virtualscroller-loading-icon\" v-bind=\"ptm('loadingIcon')\" />\n                </slot>\n            </div>\n        </div>\n    </template>\n    <template v-else>\n        <slot></slot>\n        <slot name=\"content\" :items=\"items\" :rows=\"items\" :columns=\"loadedColumns\"></slot>\n    </template>\n</template>\n\n<script>\nimport { findSingle, getHeight, getWidth, isVisible } from '@primeuix/utils/dom';\nimport SpinnerIcon from '@primevue/icons/spinner';\nimport BaseVirtualScroller from './BaseVirtualScroller.vue';\n\nexport default {\n    name: 'VirtualScroller',\n    extends: BaseVirtualScroller,\n    inheritAttrs: false,\n    emits: ['update:numToleratedItems', 'scroll', 'scroll-index-change', 'lazy-load'],\n    data() {\n        const both = this.isBoth();\n\n        return {\n            first: both ? { rows: 0, cols: 0 } : 0,\n            last: both ? { rows: 0, cols: 0 } : 0,\n            page: both ? { rows: 0, cols: 0 } : 0,\n            numItemsInViewport: both ? { rows: 0, cols: 0 } : 0,\n            lastScrollPos: both ? { top: 0, left: 0 } : 0,\n            d_numToleratedItems: this.numToleratedItems,\n            d_loading: this.loading,\n            loaderArr: [],\n            spacerStyle: {},\n            contentStyle: {}\n        };\n    },\n    element: null,\n    content: null,\n    lastScrollPos: null,\n    scrollTimeout: null,\n    resizeTimeout: null,\n    defaultWidth: 0,\n    defaultHeight: 0,\n    defaultContentWidth: 0,\n    defaultContentHeight: 0,\n    isRangeChanged: false,\n    lazyLoadState: {},\n    resizeListener: null,\n    initialized: false,\n    watch: {\n        numToleratedItems(newValue) {\n            this.d_numToleratedItems = newValue;\n        },\n        loading(newValue, oldValue) {\n            if (this.lazy && newValue !== oldValue && newValue !== this.d_loading) {\n                this.d_loading = newValue;\n            }\n        },\n        items(newValue, oldValue) {\n            if (!oldValue || oldValue.length !== (newValue || []).length) {\n                this.init();\n                this.calculateAutoSize();\n            }\n        },\n        itemSize() {\n            this.init();\n            this.calculateAutoSize();\n        },\n        orientation() {\n            this.lastScrollPos = this.isBoth() ? { top: 0, left: 0 } : 0;\n        },\n        scrollHeight() {\n            this.init();\n            this.calculateAutoSize();\n        },\n        scrollWidth() {\n            this.init();\n            this.calculateAutoSize();\n        }\n    },\n    mounted() {\n        this.viewInit();\n\n        this.lastScrollPos = this.isBoth() ? { top: 0, left: 0 } : 0;\n        this.lazyLoadState = this.lazyLoadState || {};\n    },\n    updated() {\n        !this.initialized && this.viewInit();\n    },\n    unmounted() {\n        this.unbindResizeListener();\n\n        this.initialized = false;\n    },\n    methods: {\n        viewInit() {\n            if (isVisible(this.element)) {\n                this.setContentEl(this.content);\n                this.init();\n                this.calculateAutoSize();\n                this.bindResizeListener();\n\n                this.defaultWidth = getWidth(this.element);\n                this.defaultHeight = getHeight(this.element);\n                this.defaultContentWidth = getWidth(this.content);\n                this.defaultContentHeight = getHeight(this.content);\n                this.initialized = true;\n            }\n        },\n        init() {\n            if (!this.disabled) {\n                this.setSize();\n                this.calculateOptions();\n                this.setSpacerSize();\n            }\n        },\n        isVertical() {\n            return this.orientation === 'vertical';\n        },\n        isHorizontal() {\n            return this.orientation === 'horizontal';\n        },\n        isBoth() {\n            return this.orientation === 'both';\n        },\n        scrollTo(options) {\n            //this.lastScrollPos = this.both ? { top: 0, left: 0 } : 0;\n            this.element && this.element.scrollTo(options);\n        },\n        scrollToIndex(index, behavior = 'auto') {\n            const both = this.isBoth();\n            const horizontal = this.isHorizontal();\n            const valid = both ? index.every((i) => i > -1) : index > -1;\n\n            if (valid) {\n                const first = this.first;\n                const { scrollTop = 0, scrollLeft = 0 } = this.element;\n                const { numToleratedItems } = this.calculateNumItems();\n                const contentPos = this.getContentPosition();\n                const itemSize = this.itemSize;\n                const calculateFirst = (_index = 0, _numT) => (_index <= _numT ? 0 : _index);\n                const calculateCoord = (_first, _size, _cpos) => _first * _size + _cpos;\n                const scrollTo = (left = 0, top = 0) => this.scrollTo({ left, top, behavior });\n                let newFirst = both ? { rows: 0, cols: 0 } : 0;\n                let isRangeChanged = false,\n                    isScrollChanged = false;\n\n                if (both) {\n                    newFirst = { rows: calculateFirst(index[0], numToleratedItems[0]), cols: calculateFirst(index[1], numToleratedItems[1]) };\n                    scrollTo(calculateCoord(newFirst.cols, itemSize[1], contentPos.left), calculateCoord(newFirst.rows, itemSize[0], contentPos.top));\n                    isScrollChanged = this.lastScrollPos.top !== scrollTop || this.lastScrollPos.left !== scrollLeft;\n                    isRangeChanged = newFirst.rows !== first.rows || newFirst.cols !== first.cols;\n                } else {\n                    newFirst = calculateFirst(index, numToleratedItems);\n                    horizontal ? scrollTo(calculateCoord(newFirst, itemSize, contentPos.left), scrollTop) : scrollTo(scrollLeft, calculateCoord(newFirst, itemSize, contentPos.top));\n                    isScrollChanged = this.lastScrollPos !== (horizontal ? scrollLeft : scrollTop);\n                    isRangeChanged = newFirst !== first;\n                }\n\n                this.isRangeChanged = isRangeChanged;\n                isScrollChanged && (this.first = newFirst);\n            }\n        },\n        scrollInView(index, to, behavior = 'auto') {\n            if (to) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n                const valid = both ? index.every((i) => i > -1) : index > -1;\n\n                if (valid) {\n                    const { first, viewport } = this.getRenderedRange();\n                    const scrollTo = (left = 0, top = 0) => this.scrollTo({ left, top, behavior });\n                    const isToStart = to === 'to-start';\n                    const isToEnd = to === 'to-end';\n\n                    if (isToStart) {\n                        if (both) {\n                            if (viewport.first.rows - first.rows > index[0]) {\n                                scrollTo(viewport.first.cols * this.itemSize[1], (viewport.first.rows - 1) * this.itemSize[0]);\n                            } else if (viewport.first.cols - first.cols > index[1]) {\n                                scrollTo((viewport.first.cols - 1) * this.itemSize[1], viewport.first.rows * this.itemSize[0]);\n                            }\n                        } else {\n                            if (viewport.first - first > index) {\n                                const pos = (viewport.first - 1) * this.itemSize;\n\n                                horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n                            }\n                        }\n                    } else if (isToEnd) {\n                        if (both) {\n                            if (viewport.last.rows - first.rows <= index[0] + 1) {\n                                scrollTo(viewport.first.cols * this.itemSize[1], (viewport.first.rows + 1) * this.itemSize[0]);\n                            } else if (viewport.last.cols - first.cols <= index[1] + 1) {\n                                scrollTo((viewport.first.cols + 1) * this.itemSize[1], viewport.first.rows * this.itemSize[0]);\n                            }\n                        } else {\n                            if (viewport.last - first <= index + 1) {\n                                const pos = (viewport.first + 1) * this.itemSize;\n\n                                horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n                            }\n                        }\n                    }\n                }\n            } else {\n                this.scrollToIndex(index, behavior);\n            }\n        },\n        getRenderedRange() {\n            const calculateFirstInViewport = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n\n            let firstInViewport = this.first;\n            let lastInViewport = 0;\n\n            if (this.element) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n                const { scrollTop, scrollLeft } = this.element;\n\n                if (both) {\n                    firstInViewport = { rows: calculateFirstInViewport(scrollTop, this.itemSize[0]), cols: calculateFirstInViewport(scrollLeft, this.itemSize[1]) };\n                    lastInViewport = { rows: firstInViewport.rows + this.numItemsInViewport.rows, cols: firstInViewport.cols + this.numItemsInViewport.cols };\n                } else {\n                    const scrollPos = horizontal ? scrollLeft : scrollTop;\n\n                    firstInViewport = calculateFirstInViewport(scrollPos, this.itemSize);\n                    lastInViewport = firstInViewport + this.numItemsInViewport;\n                }\n            }\n\n            return {\n                first: this.first,\n                last: this.last,\n                viewport: {\n                    first: firstInViewport,\n                    last: lastInViewport\n                }\n            };\n        },\n        calculateNumItems() {\n            const both = this.isBoth();\n            const horizontal = this.isHorizontal();\n            const itemSize = this.itemSize;\n            const contentPos = this.getContentPosition();\n            const contentWidth = this.element ? this.element.offsetWidth - contentPos.left : 0;\n            const contentHeight = this.element ? this.element.offsetHeight - contentPos.top : 0;\n            const calculateNumItemsInViewport = (_contentSize, _itemSize) => Math.ceil(_contentSize / (_itemSize || _contentSize));\n            const calculateNumToleratedItems = (_numItems) => Math.ceil(_numItems / 2);\n            const numItemsInViewport = both\n                ? { rows: calculateNumItemsInViewport(contentHeight, itemSize[0]), cols: calculateNumItemsInViewport(contentWidth, itemSize[1]) }\n                : calculateNumItemsInViewport(horizontal ? contentWidth : contentHeight, itemSize);\n\n            const numToleratedItems = this.d_numToleratedItems || (both ? [calculateNumToleratedItems(numItemsInViewport.rows), calculateNumToleratedItems(numItemsInViewport.cols)] : calculateNumToleratedItems(numItemsInViewport));\n\n            return { numItemsInViewport, numToleratedItems };\n        },\n        calculateOptions() {\n            const both = this.isBoth();\n            const first = this.first;\n            const { numItemsInViewport, numToleratedItems } = this.calculateNumItems();\n            const calculateLast = (_first, _num, _numT, _isCols = false) => this.getLast(_first + _num + (_first < _numT ? 2 : 3) * _numT, _isCols);\n            const last = both\n                ? { rows: calculateLast(first.rows, numItemsInViewport.rows, numToleratedItems[0]), cols: calculateLast(first.cols, numItemsInViewport.cols, numToleratedItems[1], true) }\n                : calculateLast(first, numItemsInViewport, numToleratedItems);\n\n            this.last = last;\n            this.numItemsInViewport = numItemsInViewport;\n            this.d_numToleratedItems = numToleratedItems;\n            this.$emit('update:numToleratedItems', this.d_numToleratedItems);\n\n            if (this.showLoader) {\n                this.loaderArr = both ? Array.from({ length: numItemsInViewport.rows }).map(() => Array.from({ length: numItemsInViewport.cols })) : Array.from({ length: numItemsInViewport });\n            }\n\n            if (this.lazy) {\n                Promise.resolve().then(() => {\n                    this.lazyLoadState = {\n                        first: this.step ? (both ? { rows: 0, cols: first.cols } : 0) : first,\n                        last: Math.min(this.step ? this.step : last, this.items?.length || 0)\n                    };\n\n                    this.$emit('lazy-load', this.lazyLoadState);\n                });\n            }\n        },\n        calculateAutoSize() {\n            if (this.autoSize && !this.d_loading) {\n                Promise.resolve().then(() => {\n                    if (this.content) {\n                        const both = this.isBoth();\n                        const horizontal = this.isHorizontal();\n                        const vertical = this.isVertical();\n\n                        this.content.style.minHeight = this.content.style.minWidth = 'auto';\n                        this.content.style.position = 'relative';\n                        this.element.style.contain = 'none';\n\n                        /*const [contentWidth, contentHeight] = [getWidth(this.content), getHeight(this.content)];\n\n                        contentWidth !== this.defaultContentWidth && (this.element.style.width = '');\n                        contentHeight !== this.defaultContentHeight && (this.element.style.height = '');*/\n\n                        const [width, height] = [getWidth(this.element), getHeight(this.element)];\n\n                        (both || horizontal) && (this.element.style.width = width < this.defaultWidth ? width + 'px' : this.scrollWidth || this.defaultWidth + 'px');\n                        (both || vertical) && (this.element.style.height = height < this.defaultHeight ? height + 'px' : this.scrollHeight || this.defaultHeight + 'px');\n\n                        this.content.style.minHeight = this.content.style.minWidth = '';\n                        this.content.style.position = '';\n                        this.element.style.contain = '';\n                    }\n                });\n            }\n        },\n        getLast(last = 0, isCols) {\n            return this.items ? Math.min(isCols ? (this.columns || this.items[0])?.length || 0 : this.items?.length || 0, last) : 0;\n        },\n        getContentPosition() {\n            if (this.content) {\n                const style = getComputedStyle(this.content);\n                const left = parseFloat(style.paddingLeft) + Math.max(parseFloat(style.left) || 0, 0);\n                const right = parseFloat(style.paddingRight) + Math.max(parseFloat(style.right) || 0, 0);\n                const top = parseFloat(style.paddingTop) + Math.max(parseFloat(style.top) || 0, 0);\n                const bottom = parseFloat(style.paddingBottom) + Math.max(parseFloat(style.bottom) || 0, 0);\n\n                return { left, right, top, bottom, x: left + right, y: top + bottom };\n            }\n\n            return { left: 0, right: 0, top: 0, bottom: 0, x: 0, y: 0 };\n        },\n        setSize() {\n            if (this.element) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n                const parentElement = this.element.parentElement;\n                const width = this.scrollWidth || `${this.element.offsetWidth || parentElement.offsetWidth}px`;\n                const height = this.scrollHeight || `${this.element.offsetHeight || parentElement.offsetHeight}px`;\n                const setProp = (_name, _value) => (this.element.style[_name] = _value);\n\n                if (both || horizontal) {\n                    setProp('height', height);\n                    setProp('width', width);\n                } else {\n                    setProp('height', height);\n                }\n            }\n        },\n        setSpacerSize() {\n            const items = this.items;\n\n            if (items) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n                const contentPos = this.getContentPosition();\n                const setProp = (_name, _value, _size, _cpos = 0) => (this.spacerStyle = { ...this.spacerStyle, ...{ [`${_name}`]: (_value || []).length * _size + _cpos + 'px' } });\n\n                if (both) {\n                    setProp('height', items, this.itemSize[0], contentPos.y);\n                    setProp('width', this.columns || items[1], this.itemSize[1], contentPos.x);\n                } else {\n                    horizontal ? setProp('width', this.columns || items, this.itemSize, contentPos.x) : setProp('height', items, this.itemSize, contentPos.y);\n                }\n            }\n        },\n        setContentPosition(pos) {\n            if (this.content && !this.appendOnly) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n                const first = pos ? pos.first : this.first;\n                const calculateTranslateVal = (_first, _size) => _first * _size;\n                const setTransform = (_x = 0, _y = 0) => (this.contentStyle = { ...this.contentStyle, ...{ transform: `translate3d(${_x}px, ${_y}px, 0)` } });\n\n                if (both) {\n                    setTransform(calculateTranslateVal(first.cols, this.itemSize[1]), calculateTranslateVal(first.rows, this.itemSize[0]));\n                } else {\n                    const translateVal = calculateTranslateVal(first, this.itemSize);\n\n                    horizontal ? setTransform(translateVal, 0) : setTransform(0, translateVal);\n                }\n            }\n        },\n        onScrollPositionChange(event) {\n            const target = event.target;\n            const both = this.isBoth();\n            const horizontal = this.isHorizontal();\n            const contentPos = this.getContentPosition();\n            const calculateScrollPos = (_pos, _cpos) => (_pos ? (_pos > _cpos ? _pos - _cpos : _pos) : 0);\n            const calculateCurrentIndex = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n\n            const calculateTriggerIndex = (_currentIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n                return _currentIndex <= _numT ? _numT : _isScrollDownOrRight ? _last - _num - _numT : _first + _numT - 1;\n            };\n\n            const calculateFirst = (_currentIndex, _triggerIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n                if (_currentIndex <= _numT) return 0;\n                else return Math.max(0, _isScrollDownOrRight ? (_currentIndex < _triggerIndex ? _first : _currentIndex - _numT) : _currentIndex > _triggerIndex ? _first : _currentIndex - 2 * _numT);\n            };\n\n            const calculateLast = (_currentIndex, _first, _last, _num, _numT, _isCols) => {\n                let lastValue = _first + _num + 2 * _numT;\n\n                if (_currentIndex >= _numT) {\n                    lastValue += _numT + 1;\n                }\n\n                return this.getLast(lastValue, _isCols);\n            };\n\n            const scrollTop = calculateScrollPos(target.scrollTop, contentPos.top);\n            const scrollLeft = calculateScrollPos(target.scrollLeft, contentPos.left);\n\n            let newFirst = both ? { rows: 0, cols: 0 } : 0;\n            let newLast = this.last;\n            let isRangeChanged = false;\n            let newScrollPos = this.lastScrollPos;\n\n            if (both) {\n                const isScrollDown = this.lastScrollPos.top <= scrollTop;\n                const isScrollRight = this.lastScrollPos.left <= scrollLeft;\n\n                if (!this.appendOnly || (this.appendOnly && (isScrollDown || isScrollRight))) {\n                    const currentIndex = { rows: calculateCurrentIndex(scrollTop, this.itemSize[0]), cols: calculateCurrentIndex(scrollLeft, this.itemSize[1]) };\n                    const triggerIndex = {\n                        rows: calculateTriggerIndex(currentIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n                        cols: calculateTriggerIndex(currentIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n                    };\n\n                    newFirst = {\n                        rows: calculateFirst(currentIndex.rows, triggerIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n                        cols: calculateFirst(currentIndex.cols, triggerIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n                    };\n                    newLast = {\n                        rows: calculateLast(currentIndex.rows, newFirst.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0]),\n                        cols: calculateLast(currentIndex.cols, newFirst.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], true)\n                    };\n\n                    isRangeChanged = newFirst.rows !== this.first.rows || newLast.rows !== this.last.rows || newFirst.cols !== this.first.cols || newLast.cols !== this.last.cols || this.isRangeChanged;\n                    newScrollPos = { top: scrollTop, left: scrollLeft };\n                }\n            } else {\n                const scrollPos = horizontal ? scrollLeft : scrollTop;\n                const isScrollDownOrRight = this.lastScrollPos <= scrollPos;\n\n                if (!this.appendOnly || (this.appendOnly && isScrollDownOrRight)) {\n                    const currentIndex = calculateCurrentIndex(scrollPos, this.itemSize);\n                    const triggerIndex = calculateTriggerIndex(currentIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n\n                    newFirst = calculateFirst(currentIndex, triggerIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n                    newLast = calculateLast(currentIndex, newFirst, this.last, this.numItemsInViewport, this.d_numToleratedItems);\n                    isRangeChanged = newFirst !== this.first || newLast !== this.last || this.isRangeChanged;\n                    newScrollPos = scrollPos;\n                }\n            }\n\n            return {\n                first: newFirst,\n                last: newLast,\n                isRangeChanged,\n                scrollPos: newScrollPos\n            };\n        },\n        onScrollChange(event) {\n            const { first, last, isRangeChanged, scrollPos } = this.onScrollPositionChange(event);\n\n            if (isRangeChanged) {\n                const newState = { first, last };\n\n                this.setContentPosition(newState);\n\n                this.first = first;\n                this.last = last;\n                this.lastScrollPos = scrollPos;\n\n                this.$emit('scroll-index-change', newState);\n\n                if (this.lazy && this.isPageChanged(first)) {\n                    const lazyLoadState = {\n                        first: this.step ? Math.min(this.getPageByFirst(first) * this.step, (this.items?.length || 0) - this.step) : first,\n                        last: Math.min(this.step ? (this.getPageByFirst(first) + 1) * this.step : last, this.items?.length || 0)\n                    };\n                    const isLazyStateChanged = this.lazyLoadState.first !== lazyLoadState.first || this.lazyLoadState.last !== lazyLoadState.last;\n\n                    isLazyStateChanged && this.$emit('lazy-load', lazyLoadState);\n                    this.lazyLoadState = lazyLoadState;\n                }\n            }\n        },\n        onScroll(event) {\n            this.$emit('scroll', event);\n\n            if (this.delay) {\n                if (this.scrollTimeout) {\n                    clearTimeout(this.scrollTimeout);\n                }\n\n                if (this.isPageChanged()) {\n                    if (!this.d_loading && this.showLoader) {\n                        const { isRangeChanged } = this.onScrollPositionChange(event);\n                        const changed = isRangeChanged || (this.step ? this.isPageChanged() : false);\n\n                        changed && (this.d_loading = true);\n                    }\n\n                    this.scrollTimeout = setTimeout(() => {\n                        this.onScrollChange(event);\n\n                        if (this.d_loading && this.showLoader && (!this.lazy || this.loading === undefined)) {\n                            this.d_loading = false;\n                            this.page = this.getPageByFirst();\n                        }\n                    }, this.delay);\n                }\n            } else {\n                this.onScrollChange(event);\n            }\n        },\n        onResize() {\n            if (this.resizeTimeout) {\n                clearTimeout(this.resizeTimeout);\n            }\n\n            this.resizeTimeout = setTimeout(() => {\n                if (isVisible(this.element)) {\n                    const both = this.isBoth();\n                    const vertical = this.isVertical();\n                    const horizontal = this.isHorizontal();\n                    const [width, height] = [getWidth(this.element), getHeight(this.element)];\n                    const [isDiffWidth, isDiffHeight] = [width !== this.defaultWidth, height !== this.defaultHeight];\n                    const reinit = both ? isDiffWidth || isDiffHeight : horizontal ? isDiffWidth : vertical ? isDiffHeight : false;\n\n                    if (reinit) {\n                        this.d_numToleratedItems = this.numToleratedItems;\n                        this.defaultWidth = width;\n                        this.defaultHeight = height;\n                        this.defaultContentWidth = getWidth(this.content);\n                        this.defaultContentHeight = getHeight(this.content);\n\n                        this.init();\n                    }\n                }\n            }, this.resizeDelay);\n        },\n        bindResizeListener() {\n            if (!this.resizeListener) {\n                this.resizeListener = this.onResize.bind(this);\n\n                window.addEventListener('resize', this.resizeListener);\n                window.addEventListener('orientationchange', this.resizeListener);\n            }\n        },\n        unbindResizeListener() {\n            if (this.resizeListener) {\n                window.removeEventListener('resize', this.resizeListener);\n                window.removeEventListener('orientationchange', this.resizeListener);\n                this.resizeListener = null;\n            }\n        },\n        getOptions(renderedIndex) {\n            const count = (this.items || []).length;\n            const index = this.isBoth() ? this.first.rows + renderedIndex : this.first + renderedIndex;\n\n            return {\n                index,\n                count,\n                first: index === 0,\n                last: index === count - 1,\n                even: index % 2 === 0,\n                odd: index % 2 !== 0\n            };\n        },\n        getLoaderOptions(index, extOptions) {\n            let count = this.loaderArr.length;\n\n            return {\n                index,\n                count,\n                first: index === 0,\n                last: index === count - 1,\n                even: index % 2 === 0,\n                odd: index % 2 !== 0,\n                ...extOptions\n            };\n        },\n        getPageByFirst(first) {\n            return Math.floor(((first ?? this.first) + this.d_numToleratedItems * 4) / (this.step || 1));\n        },\n        isPageChanged(first) {\n            return this.step && !this.lazy ? this.page !== this.getPageByFirst(first ?? this.first) : true;\n        },\n        setContentEl(el) {\n            this.content = el || this.content || findSingle(this.element, '[data-pc-section=\"content\"]');\n        },\n        elementRef(el) {\n            this.element = el;\n        },\n        contentRef(el) {\n            this.content = el;\n        }\n    },\n    computed: {\n        containerClass() {\n            return [\n                'p-virtualscroller',\n                this.class,\n                {\n                    'p-virtualscroller-inline': this.inline,\n                    'p-virtualscroller-both p-both-scroll': this.isBoth(),\n                    'p-virtualscroller-horizontal p-horizontal-scroll': this.isHorizontal()\n                }\n            ];\n        },\n        contentClass() {\n            return [\n                'p-virtualscroller-content',\n                {\n                    'p-virtualscroller-loading': this.d_loading\n                }\n            ];\n        },\n        loaderClass() {\n            return [\n                'p-virtualscroller-loader',\n                {\n                    'p-virtualscroller-loader-mask': !this.$slots.loader\n                }\n            ];\n        },\n        loadedItems() {\n            if (this.items && !this.d_loading) {\n                if (this.isBoth()) return this.items.slice(this.appendOnly ? 0 : this.first.rows, this.last.rows).map((item) => (this.columns ? item : item.slice(this.appendOnly ? 0 : this.first.cols, this.last.cols)));\n                else if (this.isHorizontal() && this.columns) return this.items;\n                else return this.items.slice(this.appendOnly ? 0 : this.first, this.last);\n            }\n\n            return [];\n        },\n        loadedRows() {\n            return this.d_loading ? (this.loaderDisabled ? this.loaderArr : []) : this.loadedItems;\n        },\n        loadedColumns() {\n            if (this.columns) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n\n                if (both || horizontal) {\n                    return this.d_loading && this.loaderDisabled ? (both ? this.loaderArr[0] : this.loaderArr) : this.columns.slice(both ? this.first.cols : this.first, both ? this.last.cols : this.last);\n                }\n            }\n\n            return this.columns;\n        }\n    },\n    components: {\n        SpinnerIcon: SpinnerIcon\n    }\n};\n</script>\n", "<template>\n    <template v-if=\"!disabled\">\n        <div :ref=\"elementRef\" :class=\"containerClass\" :tabindex=\"tabindex\" :style=\"style\" @scroll=\"onScroll\" v-bind=\"ptmi('root')\">\n            <slot\n                name=\"content\"\n                :styleClass=\"contentClass\"\n                :items=\"loadedItems\"\n                :getItemOptions=\"getOptions\"\n                :loading=\"d_loading\"\n                :getLoaderOptions=\"getLoaderOptions\"\n                :itemSize=\"itemSize\"\n                :rows=\"loadedRows\"\n                :columns=\"loadedColumns\"\n                :contentRef=\"contentRef\"\n                :spacerStyle=\"spacerStyle\"\n                :contentStyle=\"contentStyle\"\n                :vertical=\"isVertical()\"\n                :horizontal=\"isHorizontal()\"\n                :both=\"isBoth()\"\n            >\n                <div :ref=\"contentRef\" :class=\"contentClass\" :style=\"contentStyle\" v-bind=\"ptm('content')\">\n                    <template v-for=\"(item, index) of loadedItems\" :key=\"index\">\n                        <slot name=\"item\" :item=\"item\" :options=\"getOptions(index)\"></slot>\n                    </template>\n                </div>\n            </slot>\n            <div v-if=\"showSpacer\" class=\"p-virtualscroller-spacer\" :style=\"spacerStyle\" v-bind=\"ptm('spacer')\"></div>\n            <div v-if=\"!loaderDisabled && showLoader && d_loading\" :class=\"loaderClass\" v-bind=\"ptm('loader')\">\n                <template v-if=\"$slots && $slots.loader\">\n                    <template v-for=\"(_, index) of loaderArr\" :key=\"index\">\n                        <slot name=\"loader\" :options=\"getLoaderOptions(index, isBoth() && { numCols: d_numItemsInViewport.cols })\"></slot>\n                    </template>\n                </template>\n                <slot name=\"loadingicon\">\n                    <SpinnerIcon spin class=\"p-virtualscroller-loading-icon\" v-bind=\"ptm('loadingIcon')\" />\n                </slot>\n            </div>\n        </div>\n    </template>\n    <template v-else>\n        <slot></slot>\n        <slot name=\"content\" :items=\"items\" :rows=\"items\" :columns=\"loadedColumns\"></slot>\n    </template>\n</template>\n\n<script>\nimport { findSingle, getHeight, getWidth, isVisible } from '@primeuix/utils/dom';\nimport SpinnerIcon from '@primevue/icons/spinner';\nimport BaseVirtualScroller from './BaseVirtualScroller.vue';\n\nexport default {\n    name: 'VirtualScroller',\n    extends: BaseVirtualScroller,\n    inheritAttrs: false,\n    emits: ['update:numToleratedItems', 'scroll', 'scroll-index-change', 'lazy-load'],\n    data() {\n        const both = this.isBoth();\n\n        return {\n            first: both ? { rows: 0, cols: 0 } : 0,\n            last: both ? { rows: 0, cols: 0 } : 0,\n            page: both ? { rows: 0, cols: 0 } : 0,\n            numItemsInViewport: both ? { rows: 0, cols: 0 } : 0,\n            lastScrollPos: both ? { top: 0, left: 0 } : 0,\n            d_numToleratedItems: this.numToleratedItems,\n            d_loading: this.loading,\n            loaderArr: [],\n            spacerStyle: {},\n            contentStyle: {}\n        };\n    },\n    element: null,\n    content: null,\n    lastScrollPos: null,\n    scrollTimeout: null,\n    resizeTimeout: null,\n    defaultWidth: 0,\n    defaultHeight: 0,\n    defaultContentWidth: 0,\n    defaultContentHeight: 0,\n    isRangeChanged: false,\n    lazyLoadState: {},\n    resizeListener: null,\n    initialized: false,\n    watch: {\n        numToleratedItems(newValue) {\n            this.d_numToleratedItems = newValue;\n        },\n        loading(newValue, oldValue) {\n            if (this.lazy && newValue !== oldValue && newValue !== this.d_loading) {\n                this.d_loading = newValue;\n            }\n        },\n        items(newValue, oldValue) {\n            if (!oldValue || oldValue.length !== (newValue || []).length) {\n                this.init();\n                this.calculateAutoSize();\n            }\n        },\n        itemSize() {\n            this.init();\n            this.calculateAutoSize();\n        },\n        orientation() {\n            this.lastScrollPos = this.isBoth() ? { top: 0, left: 0 } : 0;\n        },\n        scrollHeight() {\n            this.init();\n            this.calculateAutoSize();\n        },\n        scrollWidth() {\n            this.init();\n            this.calculateAutoSize();\n        }\n    },\n    mounted() {\n        this.viewInit();\n\n        this.lastScrollPos = this.isBoth() ? { top: 0, left: 0 } : 0;\n        this.lazyLoadState = this.lazyLoadState || {};\n    },\n    updated() {\n        !this.initialized && this.viewInit();\n    },\n    unmounted() {\n        this.unbindResizeListener();\n\n        this.initialized = false;\n    },\n    methods: {\n        viewInit() {\n            if (isVisible(this.element)) {\n                this.setContentEl(this.content);\n                this.init();\n                this.calculateAutoSize();\n                this.bindResizeListener();\n\n                this.defaultWidth = getWidth(this.element);\n                this.defaultHeight = getHeight(this.element);\n                this.defaultContentWidth = getWidth(this.content);\n                this.defaultContentHeight = getHeight(this.content);\n                this.initialized = true;\n            }\n        },\n        init() {\n            if (!this.disabled) {\n                this.setSize();\n                this.calculateOptions();\n                this.setSpacerSize();\n            }\n        },\n        isVertical() {\n            return this.orientation === 'vertical';\n        },\n        isHorizontal() {\n            return this.orientation === 'horizontal';\n        },\n        isBoth() {\n            return this.orientation === 'both';\n        },\n        scrollTo(options) {\n            //this.lastScrollPos = this.both ? { top: 0, left: 0 } : 0;\n            this.element && this.element.scrollTo(options);\n        },\n        scrollToIndex(index, behavior = 'auto') {\n            const both = this.isBoth();\n            const horizontal = this.isHorizontal();\n            const valid = both ? index.every((i) => i > -1) : index > -1;\n\n            if (valid) {\n                const first = this.first;\n                const { scrollTop = 0, scrollLeft = 0 } = this.element;\n                const { numToleratedItems } = this.calculateNumItems();\n                const contentPos = this.getContentPosition();\n                const itemSize = this.itemSize;\n                const calculateFirst = (_index = 0, _numT) => (_index <= _numT ? 0 : _index);\n                const calculateCoord = (_first, _size, _cpos) => _first * _size + _cpos;\n                const scrollTo = (left = 0, top = 0) => this.scrollTo({ left, top, behavior });\n                let newFirst = both ? { rows: 0, cols: 0 } : 0;\n                let isRangeChanged = false,\n                    isScrollChanged = false;\n\n                if (both) {\n                    newFirst = { rows: calculateFirst(index[0], numToleratedItems[0]), cols: calculateFirst(index[1], numToleratedItems[1]) };\n                    scrollTo(calculateCoord(newFirst.cols, itemSize[1], contentPos.left), calculateCoord(newFirst.rows, itemSize[0], contentPos.top));\n                    isScrollChanged = this.lastScrollPos.top !== scrollTop || this.lastScrollPos.left !== scrollLeft;\n                    isRangeChanged = newFirst.rows !== first.rows || newFirst.cols !== first.cols;\n                } else {\n                    newFirst = calculateFirst(index, numToleratedItems);\n                    horizontal ? scrollTo(calculateCoord(newFirst, itemSize, contentPos.left), scrollTop) : scrollTo(scrollLeft, calculateCoord(newFirst, itemSize, contentPos.top));\n                    isScrollChanged = this.lastScrollPos !== (horizontal ? scrollLeft : scrollTop);\n                    isRangeChanged = newFirst !== first;\n                }\n\n                this.isRangeChanged = isRangeChanged;\n                isScrollChanged && (this.first = newFirst);\n            }\n        },\n        scrollInView(index, to, behavior = 'auto') {\n            if (to) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n                const valid = both ? index.every((i) => i > -1) : index > -1;\n\n                if (valid) {\n                    const { first, viewport } = this.getRenderedRange();\n                    const scrollTo = (left = 0, top = 0) => this.scrollTo({ left, top, behavior });\n                    const isToStart = to === 'to-start';\n                    const isToEnd = to === 'to-end';\n\n                    if (isToStart) {\n                        if (both) {\n                            if (viewport.first.rows - first.rows > index[0]) {\n                                scrollTo(viewport.first.cols * this.itemSize[1], (viewport.first.rows - 1) * this.itemSize[0]);\n                            } else if (viewport.first.cols - first.cols > index[1]) {\n                                scrollTo((viewport.first.cols - 1) * this.itemSize[1], viewport.first.rows * this.itemSize[0]);\n                            }\n                        } else {\n                            if (viewport.first - first > index) {\n                                const pos = (viewport.first - 1) * this.itemSize;\n\n                                horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n                            }\n                        }\n                    } else if (isToEnd) {\n                        if (both) {\n                            if (viewport.last.rows - first.rows <= index[0] + 1) {\n                                scrollTo(viewport.first.cols * this.itemSize[1], (viewport.first.rows + 1) * this.itemSize[0]);\n                            } else if (viewport.last.cols - first.cols <= index[1] + 1) {\n                                scrollTo((viewport.first.cols + 1) * this.itemSize[1], viewport.first.rows * this.itemSize[0]);\n                            }\n                        } else {\n                            if (viewport.last - first <= index + 1) {\n                                const pos = (viewport.first + 1) * this.itemSize;\n\n                                horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n                            }\n                        }\n                    }\n                }\n            } else {\n                this.scrollToIndex(index, behavior);\n            }\n        },\n        getRenderedRange() {\n            const calculateFirstInViewport = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n\n            let firstInViewport = this.first;\n            let lastInViewport = 0;\n\n            if (this.element) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n                const { scrollTop, scrollLeft } = this.element;\n\n                if (both) {\n                    firstInViewport = { rows: calculateFirstInViewport(scrollTop, this.itemSize[0]), cols: calculateFirstInViewport(scrollLeft, this.itemSize[1]) };\n                    lastInViewport = { rows: firstInViewport.rows + this.numItemsInViewport.rows, cols: firstInViewport.cols + this.numItemsInViewport.cols };\n                } else {\n                    const scrollPos = horizontal ? scrollLeft : scrollTop;\n\n                    firstInViewport = calculateFirstInViewport(scrollPos, this.itemSize);\n                    lastInViewport = firstInViewport + this.numItemsInViewport;\n                }\n            }\n\n            return {\n                first: this.first,\n                last: this.last,\n                viewport: {\n                    first: firstInViewport,\n                    last: lastInViewport\n                }\n            };\n        },\n        calculateNumItems() {\n            const both = this.isBoth();\n            const horizontal = this.isHorizontal();\n            const itemSize = this.itemSize;\n            const contentPos = this.getContentPosition();\n            const contentWidth = this.element ? this.element.offsetWidth - contentPos.left : 0;\n            const contentHeight = this.element ? this.element.offsetHeight - contentPos.top : 0;\n            const calculateNumItemsInViewport = (_contentSize, _itemSize) => Math.ceil(_contentSize / (_itemSize || _contentSize));\n            const calculateNumToleratedItems = (_numItems) => Math.ceil(_numItems / 2);\n            const numItemsInViewport = both\n                ? { rows: calculateNumItemsInViewport(contentHeight, itemSize[0]), cols: calculateNumItemsInViewport(contentWidth, itemSize[1]) }\n                : calculateNumItemsInViewport(horizontal ? contentWidth : contentHeight, itemSize);\n\n            const numToleratedItems = this.d_numToleratedItems || (both ? [calculateNumToleratedItems(numItemsInViewport.rows), calculateNumToleratedItems(numItemsInViewport.cols)] : calculateNumToleratedItems(numItemsInViewport));\n\n            return { numItemsInViewport, numToleratedItems };\n        },\n        calculateOptions() {\n            const both = this.isBoth();\n            const first = this.first;\n            const { numItemsInViewport, numToleratedItems } = this.calculateNumItems();\n            const calculateLast = (_first, _num, _numT, _isCols = false) => this.getLast(_first + _num + (_first < _numT ? 2 : 3) * _numT, _isCols);\n            const last = both\n                ? { rows: calculateLast(first.rows, numItemsInViewport.rows, numToleratedItems[0]), cols: calculateLast(first.cols, numItemsInViewport.cols, numToleratedItems[1], true) }\n                : calculateLast(first, numItemsInViewport, numToleratedItems);\n\n            this.last = last;\n            this.numItemsInViewport = numItemsInViewport;\n            this.d_numToleratedItems = numToleratedItems;\n            this.$emit('update:numToleratedItems', this.d_numToleratedItems);\n\n            if (this.showLoader) {\n                this.loaderArr = both ? Array.from({ length: numItemsInViewport.rows }).map(() => Array.from({ length: numItemsInViewport.cols })) : Array.from({ length: numItemsInViewport });\n            }\n\n            if (this.lazy) {\n                Promise.resolve().then(() => {\n                    this.lazyLoadState = {\n                        first: this.step ? (both ? { rows: 0, cols: first.cols } : 0) : first,\n                        last: Math.min(this.step ? this.step : last, this.items?.length || 0)\n                    };\n\n                    this.$emit('lazy-load', this.lazyLoadState);\n                });\n            }\n        },\n        calculateAutoSize() {\n            if (this.autoSize && !this.d_loading) {\n                Promise.resolve().then(() => {\n                    if (this.content) {\n                        const both = this.isBoth();\n                        const horizontal = this.isHorizontal();\n                        const vertical = this.isVertical();\n\n                        this.content.style.minHeight = this.content.style.minWidth = 'auto';\n                        this.content.style.position = 'relative';\n                        this.element.style.contain = 'none';\n\n                        /*const [contentWidth, contentHeight] = [getWidth(this.content), getHeight(this.content)];\n\n                        contentWidth !== this.defaultContentWidth && (this.element.style.width = '');\n                        contentHeight !== this.defaultContentHeight && (this.element.style.height = '');*/\n\n                        const [width, height] = [getWidth(this.element), getHeight(this.element)];\n\n                        (both || horizontal) && (this.element.style.width = width < this.defaultWidth ? width + 'px' : this.scrollWidth || this.defaultWidth + 'px');\n                        (both || vertical) && (this.element.style.height = height < this.defaultHeight ? height + 'px' : this.scrollHeight || this.defaultHeight + 'px');\n\n                        this.content.style.minHeight = this.content.style.minWidth = '';\n                        this.content.style.position = '';\n                        this.element.style.contain = '';\n                    }\n                });\n            }\n        },\n        getLast(last = 0, isCols) {\n            return this.items ? Math.min(isCols ? (this.columns || this.items[0])?.length || 0 : this.items?.length || 0, last) : 0;\n        },\n        getContentPosition() {\n            if (this.content) {\n                const style = getComputedStyle(this.content);\n                const left = parseFloat(style.paddingLeft) + Math.max(parseFloat(style.left) || 0, 0);\n                const right = parseFloat(style.paddingRight) + Math.max(parseFloat(style.right) || 0, 0);\n                const top = parseFloat(style.paddingTop) + Math.max(parseFloat(style.top) || 0, 0);\n                const bottom = parseFloat(style.paddingBottom) + Math.max(parseFloat(style.bottom) || 0, 0);\n\n                return { left, right, top, bottom, x: left + right, y: top + bottom };\n            }\n\n            return { left: 0, right: 0, top: 0, bottom: 0, x: 0, y: 0 };\n        },\n        setSize() {\n            if (this.element) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n                const parentElement = this.element.parentElement;\n                const width = this.scrollWidth || `${this.element.offsetWidth || parentElement.offsetWidth}px`;\n                const height = this.scrollHeight || `${this.element.offsetHeight || parentElement.offsetHeight}px`;\n                const setProp = (_name, _value) => (this.element.style[_name] = _value);\n\n                if (both || horizontal) {\n                    setProp('height', height);\n                    setProp('width', width);\n                } else {\n                    setProp('height', height);\n                }\n            }\n        },\n        setSpacerSize() {\n            const items = this.items;\n\n            if (items) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n                const contentPos = this.getContentPosition();\n                const setProp = (_name, _value, _size, _cpos = 0) => (this.spacerStyle = { ...this.spacerStyle, ...{ [`${_name}`]: (_value || []).length * _size + _cpos + 'px' } });\n\n                if (both) {\n                    setProp('height', items, this.itemSize[0], contentPos.y);\n                    setProp('width', this.columns || items[1], this.itemSize[1], contentPos.x);\n                } else {\n                    horizontal ? setProp('width', this.columns || items, this.itemSize, contentPos.x) : setProp('height', items, this.itemSize, contentPos.y);\n                }\n            }\n        },\n        setContentPosition(pos) {\n            if (this.content && !this.appendOnly) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n                const first = pos ? pos.first : this.first;\n                const calculateTranslateVal = (_first, _size) => _first * _size;\n                const setTransform = (_x = 0, _y = 0) => (this.contentStyle = { ...this.contentStyle, ...{ transform: `translate3d(${_x}px, ${_y}px, 0)` } });\n\n                if (both) {\n                    setTransform(calculateTranslateVal(first.cols, this.itemSize[1]), calculateTranslateVal(first.rows, this.itemSize[0]));\n                } else {\n                    const translateVal = calculateTranslateVal(first, this.itemSize);\n\n                    horizontal ? setTransform(translateVal, 0) : setTransform(0, translateVal);\n                }\n            }\n        },\n        onScrollPositionChange(event) {\n            const target = event.target;\n            const both = this.isBoth();\n            const horizontal = this.isHorizontal();\n            const contentPos = this.getContentPosition();\n            const calculateScrollPos = (_pos, _cpos) => (_pos ? (_pos > _cpos ? _pos - _cpos : _pos) : 0);\n            const calculateCurrentIndex = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n\n            const calculateTriggerIndex = (_currentIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n                return _currentIndex <= _numT ? _numT : _isScrollDownOrRight ? _last - _num - _numT : _first + _numT - 1;\n            };\n\n            const calculateFirst = (_currentIndex, _triggerIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n                if (_currentIndex <= _numT) return 0;\n                else return Math.max(0, _isScrollDownOrRight ? (_currentIndex < _triggerIndex ? _first : _currentIndex - _numT) : _currentIndex > _triggerIndex ? _first : _currentIndex - 2 * _numT);\n            };\n\n            const calculateLast = (_currentIndex, _first, _last, _num, _numT, _isCols) => {\n                let lastValue = _first + _num + 2 * _numT;\n\n                if (_currentIndex >= _numT) {\n                    lastValue += _numT + 1;\n                }\n\n                return this.getLast(lastValue, _isCols);\n            };\n\n            const scrollTop = calculateScrollPos(target.scrollTop, contentPos.top);\n            const scrollLeft = calculateScrollPos(target.scrollLeft, contentPos.left);\n\n            let newFirst = both ? { rows: 0, cols: 0 } : 0;\n            let newLast = this.last;\n            let isRangeChanged = false;\n            let newScrollPos = this.lastScrollPos;\n\n            if (both) {\n                const isScrollDown = this.lastScrollPos.top <= scrollTop;\n                const isScrollRight = this.lastScrollPos.left <= scrollLeft;\n\n                if (!this.appendOnly || (this.appendOnly && (isScrollDown || isScrollRight))) {\n                    const currentIndex = { rows: calculateCurrentIndex(scrollTop, this.itemSize[0]), cols: calculateCurrentIndex(scrollLeft, this.itemSize[1]) };\n                    const triggerIndex = {\n                        rows: calculateTriggerIndex(currentIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n                        cols: calculateTriggerIndex(currentIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n                    };\n\n                    newFirst = {\n                        rows: calculateFirst(currentIndex.rows, triggerIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n                        cols: calculateFirst(currentIndex.cols, triggerIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n                    };\n                    newLast = {\n                        rows: calculateLast(currentIndex.rows, newFirst.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0]),\n                        cols: calculateLast(currentIndex.cols, newFirst.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], true)\n                    };\n\n                    isRangeChanged = newFirst.rows !== this.first.rows || newLast.rows !== this.last.rows || newFirst.cols !== this.first.cols || newLast.cols !== this.last.cols || this.isRangeChanged;\n                    newScrollPos = { top: scrollTop, left: scrollLeft };\n                }\n            } else {\n                const scrollPos = horizontal ? scrollLeft : scrollTop;\n                const isScrollDownOrRight = this.lastScrollPos <= scrollPos;\n\n                if (!this.appendOnly || (this.appendOnly && isScrollDownOrRight)) {\n                    const currentIndex = calculateCurrentIndex(scrollPos, this.itemSize);\n                    const triggerIndex = calculateTriggerIndex(currentIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n\n                    newFirst = calculateFirst(currentIndex, triggerIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n                    newLast = calculateLast(currentIndex, newFirst, this.last, this.numItemsInViewport, this.d_numToleratedItems);\n                    isRangeChanged = newFirst !== this.first || newLast !== this.last || this.isRangeChanged;\n                    newScrollPos = scrollPos;\n                }\n            }\n\n            return {\n                first: newFirst,\n                last: newLast,\n                isRangeChanged,\n                scrollPos: newScrollPos\n            };\n        },\n        onScrollChange(event) {\n            const { first, last, isRangeChanged, scrollPos } = this.onScrollPositionChange(event);\n\n            if (isRangeChanged) {\n                const newState = { first, last };\n\n                this.setContentPosition(newState);\n\n                this.first = first;\n                this.last = last;\n                this.lastScrollPos = scrollPos;\n\n                this.$emit('scroll-index-change', newState);\n\n                if (this.lazy && this.isPageChanged(first)) {\n                    const lazyLoadState = {\n                        first: this.step ? Math.min(this.getPageByFirst(first) * this.step, (this.items?.length || 0) - this.step) : first,\n                        last: Math.min(this.step ? (this.getPageByFirst(first) + 1) * this.step : last, this.items?.length || 0)\n                    };\n                    const isLazyStateChanged = this.lazyLoadState.first !== lazyLoadState.first || this.lazyLoadState.last !== lazyLoadState.last;\n\n                    isLazyStateChanged && this.$emit('lazy-load', lazyLoadState);\n                    this.lazyLoadState = lazyLoadState;\n                }\n            }\n        },\n        onScroll(event) {\n            this.$emit('scroll', event);\n\n            if (this.delay) {\n                if (this.scrollTimeout) {\n                    clearTimeout(this.scrollTimeout);\n                }\n\n                if (this.isPageChanged()) {\n                    if (!this.d_loading && this.showLoader) {\n                        const { isRangeChanged } = this.onScrollPositionChange(event);\n                        const changed = isRangeChanged || (this.step ? this.isPageChanged() : false);\n\n                        changed && (this.d_loading = true);\n                    }\n\n                    this.scrollTimeout = setTimeout(() => {\n                        this.onScrollChange(event);\n\n                        if (this.d_loading && this.showLoader && (!this.lazy || this.loading === undefined)) {\n                            this.d_loading = false;\n                            this.page = this.getPageByFirst();\n                        }\n                    }, this.delay);\n                }\n            } else {\n                this.onScrollChange(event);\n            }\n        },\n        onResize() {\n            if (this.resizeTimeout) {\n                clearTimeout(this.resizeTimeout);\n            }\n\n            this.resizeTimeout = setTimeout(() => {\n                if (isVisible(this.element)) {\n                    const both = this.isBoth();\n                    const vertical = this.isVertical();\n                    const horizontal = this.isHorizontal();\n                    const [width, height] = [getWidth(this.element), getHeight(this.element)];\n                    const [isDiffWidth, isDiffHeight] = [width !== this.defaultWidth, height !== this.defaultHeight];\n                    const reinit = both ? isDiffWidth || isDiffHeight : horizontal ? isDiffWidth : vertical ? isDiffHeight : false;\n\n                    if (reinit) {\n                        this.d_numToleratedItems = this.numToleratedItems;\n                        this.defaultWidth = width;\n                        this.defaultHeight = height;\n                        this.defaultContentWidth = getWidth(this.content);\n                        this.defaultContentHeight = getHeight(this.content);\n\n                        this.init();\n                    }\n                }\n            }, this.resizeDelay);\n        },\n        bindResizeListener() {\n            if (!this.resizeListener) {\n                this.resizeListener = this.onResize.bind(this);\n\n                window.addEventListener('resize', this.resizeListener);\n                window.addEventListener('orientationchange', this.resizeListener);\n            }\n        },\n        unbindResizeListener() {\n            if (this.resizeListener) {\n                window.removeEventListener('resize', this.resizeListener);\n                window.removeEventListener('orientationchange', this.resizeListener);\n                this.resizeListener = null;\n            }\n        },\n        getOptions(renderedIndex) {\n            const count = (this.items || []).length;\n            const index = this.isBoth() ? this.first.rows + renderedIndex : this.first + renderedIndex;\n\n            return {\n                index,\n                count,\n                first: index === 0,\n                last: index === count - 1,\n                even: index % 2 === 0,\n                odd: index % 2 !== 0\n            };\n        },\n        getLoaderOptions(index, extOptions) {\n            let count = this.loaderArr.length;\n\n            return {\n                index,\n                count,\n                first: index === 0,\n                last: index === count - 1,\n                even: index % 2 === 0,\n                odd: index % 2 !== 0,\n                ...extOptions\n            };\n        },\n        getPageByFirst(first) {\n            return Math.floor(((first ?? this.first) + this.d_numToleratedItems * 4) / (this.step || 1));\n        },\n        isPageChanged(first) {\n            return this.step && !this.lazy ? this.page !== this.getPageByFirst(first ?? this.first) : true;\n        },\n        setContentEl(el) {\n            this.content = el || this.content || findSingle(this.element, '[data-pc-section=\"content\"]');\n        },\n        elementRef(el) {\n            this.element = el;\n        },\n        contentRef(el) {\n            this.content = el;\n        }\n    },\n    computed: {\n        containerClass() {\n            return [\n                'p-virtualscroller',\n                this.class,\n                {\n                    'p-virtualscroller-inline': this.inline,\n                    'p-virtualscroller-both p-both-scroll': this.isBoth(),\n                    'p-virtualscroller-horizontal p-horizontal-scroll': this.isHorizontal()\n                }\n            ];\n        },\n        contentClass() {\n            return [\n                'p-virtualscroller-content',\n                {\n                    'p-virtualscroller-loading': this.d_loading\n                }\n            ];\n        },\n        loaderClass() {\n            return [\n                'p-virtualscroller-loader',\n                {\n                    'p-virtualscroller-loader-mask': !this.$slots.loader\n                }\n            ];\n        },\n        loadedItems() {\n            if (this.items && !this.d_loading) {\n                if (this.isBoth()) return this.items.slice(this.appendOnly ? 0 : this.first.rows, this.last.rows).map((item) => (this.columns ? item : item.slice(this.appendOnly ? 0 : this.first.cols, this.last.cols)));\n                else if (this.isHorizontal() && this.columns) return this.items;\n                else return this.items.slice(this.appendOnly ? 0 : this.first, this.last);\n            }\n\n            return [];\n        },\n        loadedRows() {\n            return this.d_loading ? (this.loaderDisabled ? this.loaderArr : []) : this.loadedItems;\n        },\n        loadedColumns() {\n            if (this.columns) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n\n                if (both || horizontal) {\n                    return this.d_loading && this.loaderDisabled ? (both ? this.loaderArr[0] : this.loaderArr) : this.columns.slice(both ? this.first.cols : this.first, both ? this.last.cols : this.last);\n                }\n            }\n\n            return this.columns;\n        }\n    },\n    components: {\n        SpinnerIcon: SpinnerIcon\n    }\n};\n</script>\n", "import BaseStyle from '@primevue/core/base/style';\n\nconst theme = ({ dt }) => `\n.p-select {\n    display: inline-flex;\n    cursor: pointer;\n    position: relative;\n    user-select: none;\n    background: ${dt('select.background')};\n    border: 1px solid ${dt('select.border.color')};\n    transition: background ${dt('select.transition.duration')}, color ${dt('select.transition.duration')}, border-color ${dt('select.transition.duration')},\n        outline-color ${dt('select.transition.duration')}, box-shadow ${dt('select.transition.duration')};\n    border-radius: ${dt('select.border.radius')};\n    outline-color: transparent;\n    box-shadow: ${dt('select.shadow')};\n}\n\n.p-select:not(.p-disabled):hover {\n    border-color: ${dt('select.hover.border.color')};\n}\n\n.p-select:not(.p-disabled).p-focus {\n    border-color: ${dt('select.focus.border.color')};\n    box-shadow: ${dt('select.focus.ring.shadow')};\n    outline: ${dt('select.focus.ring.width')} ${dt('select.focus.ring.style')} ${dt('select.focus.ring.color')};\n    outline-offset: ${dt('select.focus.ring.offset')};\n}\n\n.p-select.p-variant-filled {\n    background: ${dt('select.filled.background')};\n}\n\n.p-select.p-variant-filled:not(.p-disabled):hover {\n    background: ${dt('select.filled.hover.background')};\n}\n\n.p-select.p-variant-filled:not(.p-disabled).p-focus {\n    background: ${dt('select.filled.focus.background')};\n}\n\n.p-select.p-invalid {\n    border-color: ${dt('select.invalid.border.color')};\n}\n\n.p-select.p-disabled {\n    opacity: 1;\n    background: ${dt('select.disabled.background')};\n}\n\n.p-select-clear-icon {\n    position: absolute;\n    top: 50%;\n    margin-top: -0.5rem;\n    color: ${dt('select.clear.icon.color')};\n    inset-inline-end: ${dt('select.dropdown.width')};\n}\n\n.p-select-dropdown {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    flex-shrink: 0;\n    background: transparent;\n    color: ${dt('select.dropdown.color')};\n    width: ${dt('select.dropdown.width')};\n    border-start-end-radius: ${dt('select.border.radius')};\n    border-end-end-radius: ${dt('select.border.radius')};\n}\n\n.p-select-label {\n    display: block;\n    white-space: nowrap;\n    overflow: hidden;\n    flex: 1 1 auto;\n    width: 1%;\n    padding: ${dt('select.padding.y')} ${dt('select.padding.x')};\n    text-overflow: ellipsis;\n    cursor: pointer;\n    color: ${dt('select.color')};\n    background: transparent;\n    border: 0 none;\n    outline: 0 none;\n}\n\n.p-select-label.p-placeholder {\n    color: ${dt('select.placeholder.color')};\n}\n\n.p-select.p-invalid .p-select-label.p-placeholder {\n    color: ${dt('select.invalid.placeholder.color')};\n}\n\n.p-select:has(.p-select-clear-icon) .p-select-label {\n    padding-inline-end: calc(1rem + ${dt('select.padding.x')});\n}\n\n.p-select.p-disabled .p-select-label {\n    color: ${dt('select.disabled.color')};\n}\n\n.p-select-label-empty {\n    overflow: hidden;\n    opacity: 0;\n}\n\ninput.p-select-label {\n    cursor: default;\n}\n\n.p-select .p-select-overlay {\n    min-width: 100%;\n}\n\n.p-select-overlay {\n    position: absolute;\n    top: 0;\n    left: 0;\n    background: ${dt('select.overlay.background')};\n    color: ${dt('select.overlay.color')};\n    border: 1px solid ${dt('select.overlay.border.color')};\n    border-radius: ${dt('select.overlay.border.radius')};\n    box-shadow: ${dt('select.overlay.shadow')};\n}\n\n.p-select-header {\n    padding: ${dt('select.list.header.padding')};\n}\n\n.p-select-filter {\n    width: 100%;\n}\n\n.p-select-list-container {\n    overflow: auto;\n}\n\n.p-select-option-group {\n    cursor: auto;\n    margin: 0;\n    padding: ${dt('select.option.group.padding')};\n    background: ${dt('select.option.group.background')};\n    color: ${dt('select.option.group.color')};\n    font-weight: ${dt('select.option.group.font.weight')};\n}\n\n.p-select-list {\n    margin: 0;\n    padding: 0;\n    list-style-type: none;\n    padding: ${dt('select.list.padding')};\n    gap: ${dt('select.list.gap')};\n    display: flex;\n    flex-direction: column;\n}\n\n.p-select-option {\n    cursor: pointer;\n    font-weight: normal;\n    white-space: nowrap;\n    position: relative;\n    overflow: hidden;\n    display: flex;\n    align-items: center;\n    padding: ${dt('select.option.padding')};\n    border: 0 none;\n    color: ${dt('select.option.color')};\n    background: transparent;\n    transition: background ${dt('select.transition.duration')}, color ${dt('select.transition.duration')}, border-color ${dt('select.transition.duration')},\n            box-shadow ${dt('select.transition.duration')}, outline-color ${dt('select.transition.duration')};\n    border-radius: ${dt('select.option.border.radius')};\n}\n\n.p-select-option:not(.p-select-option-selected):not(.p-disabled).p-focus {\n    background: ${dt('select.option.focus.background')};\n    color: ${dt('select.option.focus.color')};\n}\n\n.p-select-option.p-select-option-selected {\n    background: ${dt('select.option.selected.background')};\n    color: ${dt('select.option.selected.color')};\n}\n\n.p-select-option.p-select-option-selected.p-focus {\n    background: ${dt('select.option.selected.focus.background')};\n    color: ${dt('select.option.selected.focus.color')};\n}\n\n.p-select-option-check-icon {\n    position: relative;\n    margin-inline-start: ${dt('select.checkmark.gutter.start')};\n    margin-inline-end: ${dt('select.checkmark.gutter.end')};\n    color: ${dt('select.checkmark.color')};\n}\n\n.p-select-empty-message {\n    padding: ${dt('select.empty.message.padding')};\n}\n\n.p-select-fluid {\n    display: flex;\n    width: 100%;\n}\n\n.p-select-sm .p-select-label {\n    font-size: ${dt('select.sm.font.size')};\n    padding-block: ${dt('select.sm.padding.y')};\n    padding-inline: ${dt('select.sm.padding.x')};\n}\n\n.p-select-sm .p-select-dropdown .p-icon {\n    font-size: ${dt('select.sm.font.size')};\n    width: ${dt('select.sm.font.size')};\n    height: ${dt('select.sm.font.size')};\n}\n\n.p-select-lg .p-select-label {\n    font-size: ${dt('select.lg.font.size')};\n    padding-block: ${dt('select.lg.padding.y')};\n    padding-inline: ${dt('select.lg.padding.x')};\n}\n\n.p-select-lg .p-select-dropdown .p-icon {\n    font-size: ${dt('select.lg.font.size')};\n    width: ${dt('select.lg.font.size')};\n    height: ${dt('select.lg.font.size')};\n}\n`;\n\nconst classes = {\n    root: ({ instance, props, state }) => [\n        'p-select p-component p-inputwrapper',\n        {\n            'p-disabled': props.disabled,\n            'p-invalid': instance.$invalid,\n            'p-variant-filled': instance.$variant === 'filled',\n            'p-focus': state.focused,\n            'p-inputwrapper-filled': instance.$filled,\n            'p-inputwrapper-focus': state.focused || state.overlayVisible,\n            'p-select-open': state.overlayVisible,\n            'p-select-fluid': instance.$fluid,\n            'p-select-sm p-inputfield-sm': props.size === 'small',\n            'p-select-lg p-inputfield-lg': props.size === 'large'\n        }\n    ],\n    label: ({ instance, props }) => [\n        'p-select-label',\n        {\n            'p-placeholder': !props.editable && instance.label === props.placeholder,\n            'p-select-label-empty': !props.editable && !instance.$slots['value'] && (instance.label === 'p-emptylabel' || instance.label.length === 0)\n        }\n    ],\n    clearIcon: 'p-select-clear-icon',\n    dropdown: 'p-select-dropdown',\n    loadingicon: 'p-select-loading-icon',\n    dropdownIcon: 'p-select-dropdown-icon',\n    overlay: 'p-select-overlay p-component',\n    header: 'p-select-header',\n    pcFilter: 'p-select-filter',\n    listContainer: 'p-select-list-container',\n    list: 'p-select-list',\n    optionGroup: 'p-select-option-group',\n    optionGroupLabel: 'p-select-option-group-label',\n    option: ({ instance, props, state, option, focusedOption }) => [\n        'p-select-option',\n        {\n            'p-select-option-selected': instance.isSelected(option) && props.highlightOnSelect,\n            'p-focus': state.focusedOptionIndex === focusedOption,\n            'p-disabled': instance.isOptionDisabled(option)\n        }\n    ],\n    optionLabel: 'p-select-option-label',\n    optionCheckIcon: 'p-select-option-check-icon',\n    optionBlankIcon: 'p-select-option-blank-icon',\n    emptyMessage: 'p-select-empty-message'\n};\n\nexport default BaseStyle.extend({\n    name: 'select',\n    theme,\n    classes\n});\n", "<script>\nimport BaseInput from '@primevue/core/baseinput';\nimport SelectStyle from 'primevue/select/style';\n\nexport default {\n    name: 'BaseSelect',\n    extends: BaseInput,\n    props: {\n        options: Array,\n        optionLabel: [String, Function],\n        optionValue: [String, Function],\n        optionDisabled: [String, Function],\n        optionGroupLabel: [String, Function],\n        optionGroupChildren: [String, Function],\n        scrollHeight: {\n            type: String,\n            default: '14rem'\n        },\n        filter: Boolean,\n        filterPlaceholder: String,\n        filterLocale: String,\n        filterMatchMode: {\n            type: String,\n            default: 'contains'\n        },\n        filterFields: {\n            type: Array,\n            default: null\n        },\n        editable: Boolean,\n        placeholder: {\n            type: String,\n            default: null\n        },\n        dataKey: null,\n        showClear: {\n            type: Boolean,\n            default: false\n        },\n        inputId: {\n            type: String,\n            default: null\n        },\n        inputClass: {\n            type: [String, Object],\n            default: null\n        },\n        inputStyle: {\n            type: Object,\n            default: null\n        },\n        labelId: {\n            type: String,\n            default: null\n        },\n        labelClass: {\n            type: [String, Object],\n            default: null\n        },\n        labelStyle: {\n            type: Object,\n            default: null\n        },\n        panelClass: {\n            type: [String, Object],\n            default: null\n        },\n        overlayStyle: {\n            type: Object,\n            default: null\n        },\n        overlayClass: {\n            type: [String, Object],\n            default: null\n        },\n        panelStyle: {\n            type: Object,\n            default: null\n        },\n        appendTo: {\n            type: [String, Object],\n            default: 'body'\n        },\n        loading: {\n            type: Boolean,\n            default: false\n        },\n        clearIcon: {\n            type: String,\n            default: undefined\n        },\n        dropdownIcon: {\n            type: String,\n            default: undefined\n        },\n        filterIcon: {\n            type: String,\n            default: undefined\n        },\n        loadingIcon: {\n            type: String,\n            default: undefined\n        },\n        resetFilterOnHide: {\n            type: Boolean,\n            default: false\n        },\n        resetFilterOnClear: {\n            type: Boolean,\n            default: false\n        },\n        virtualScrollerOptions: {\n            type: Object,\n            default: null\n        },\n        autoOptionFocus: {\n            type: Boolean,\n            default: false\n        },\n        autoFilterFocus: {\n            type: Boolean,\n            default: false\n        },\n        selectOnFocus: {\n            type: Boolean,\n            default: false\n        },\n        focusOnHover: {\n            type: Boolean,\n            default: true\n        },\n        highlightOnSelect: {\n            type: Boolean,\n            default: true\n        },\n        checkmark: {\n            type: Boolean,\n            default: false\n        },\n        filterMessage: {\n            type: String,\n            default: null\n        },\n        selectionMessage: {\n            type: String,\n            default: null\n        },\n        emptySelectionMessage: {\n            type: String,\n            default: null\n        },\n        emptyFilterMessage: {\n            type: String,\n            default: null\n        },\n        emptyMessage: {\n            type: String,\n            default: null\n        },\n        tabindex: {\n            type: Number,\n            default: 0\n        },\n        ariaLabel: {\n            type: String,\n            default: null\n        },\n        ariaLabelledby: {\n            type: String,\n            default: null\n        }\n    },\n    style: SelectStyle,\n    provide() {\n        return {\n            $pcSelect: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <div ref=\"container\" :id=\"id\" :class=\"cx('root')\" @click=\"onContainerClick\" v-bind=\"ptmi('root')\">\n        <input\n            v-if=\"editable\"\n            ref=\"focusInput\"\n            :id=\"labelId || inputId\"\n            type=\"text\"\n            :class=\"[cx('label'), inputClass, labelClass]\"\n            :style=\"[inputStyle, labelStyle]\"\n            :value=\"editableInputValue\"\n            :placeholder=\"placeholder\"\n            :tabindex=\"!disabled ? tabindex : -1\"\n            :disabled=\"disabled\"\n            autocomplete=\"off\"\n            role=\"combobox\"\n            :aria-label=\"ariaLabel\"\n            :aria-labelledby=\"ariaLabelledby\"\n            aria-haspopup=\"listbox\"\n            :aria-expanded=\"overlayVisible\"\n            :aria-controls=\"id + '_list'\"\n            :aria-activedescendant=\"focused ? focusedOptionId : undefined\"\n            :aria-invalid=\"invalid || undefined\"\n            @focus=\"onFocus\"\n            @blur=\"onBlur\"\n            @keydown=\"onKeyDown\"\n            @input=\"onEditableInput\"\n            v-bind=\"ptm('label')\"\n        />\n        <span\n            v-else\n            ref=\"focusInput\"\n            :id=\"labelId || inputId\"\n            :class=\"[cx('label'), inputClass, labelClass]\"\n            :style=\"[inputStyle, labelStyle]\"\n            :tabindex=\"!disabled ? tabindex : -1\"\n            role=\"combobox\"\n            :aria-label=\"ariaLabel || (label === 'p-emptylabel' ? undefined : label)\"\n            :aria-labelledby=\"ariaLabelledby\"\n            aria-haspopup=\"listbox\"\n            :aria-expanded=\"overlayVisible\"\n            :aria-controls=\"id + '_list'\"\n            :aria-activedescendant=\"focused ? focusedOptionId : undefined\"\n            :aria-disabled=\"disabled\"\n            @focus=\"onFocus\"\n            @blur=\"onBlur\"\n            @keydown=\"onKeyDown\"\n            v-bind=\"ptm('label')\"\n        >\n            <slot name=\"value\" :value=\"d_value\" :placeholder=\"placeholder\">{{ label === 'p-emptylabel' ? '&nbsp;' : label ?? 'empty' }}</slot>\n        </span>\n        <slot v-if=\"isClearIconVisible\" name=\"clearicon\" :class=\"cx('clearIcon')\" :clearCallback=\"onClearClick\">\n            <component :is=\"clearIcon ? 'i' : 'TimesIcon'\" ref=\"clearIcon\" :class=\"[cx('clearIcon'), clearIcon]\" @click=\"onClearClick\" v-bind=\"ptm('clearIcon')\" data-pc-section=\"clearicon\" />\n        </slot>\n        <div :class=\"cx('dropdown')\" v-bind=\"ptm('dropdown')\">\n            <slot v-if=\"loading\" name=\"loadingicon\" :class=\"cx('loadingIcon')\">\n                <span v-if=\"loadingIcon\" :class=\"[cx('loadingIcon'), 'pi-spin', loadingIcon]\" aria-hidden=\"true\" v-bind=\"ptm('loadingIcon')\" />\n                <SpinnerIcon v-else :class=\"cx('loadingIcon')\" spin aria-hidden=\"true\" v-bind=\"ptm('loadingIcon')\" />\n            </slot>\n            <slot v-else name=\"dropdownicon\" :class=\"cx('dropdownIcon')\">\n                <component :is=\"dropdownIcon ? 'span' : 'ChevronDownIcon'\" :class=\"[cx('dropdownIcon'), dropdownIcon]\" aria-hidden=\"true\" v-bind=\"ptm('dropdownIcon')\" />\n            </slot>\n        </div>\n        <Portal :appendTo=\"appendTo\">\n            <transition name=\"p-connected-overlay\" @enter=\"onOverlayEnter\" @after-enter=\"onOverlayAfterEnter\" @leave=\"onOverlayLeave\" @after-leave=\"onOverlayAfterLeave\" v-bind=\"ptm('transition')\">\n                <div v-if=\"overlayVisible\" :ref=\"overlayRef\" :class=\"[cx('overlay'), panelClass, overlayClass]\" :style=\"[panelStyle, overlayStyle]\" @click=\"onOverlayClick\" @keydown=\"onOverlayKeyDown\" v-bind=\"ptm('overlay')\">\n                    <span\n                        ref=\"firstHiddenFocusableElementOnOverlay\"\n                        role=\"presentation\"\n                        aria-hidden=\"true\"\n                        class=\"p-hidden-accessible p-hidden-focusable\"\n                        :tabindex=\"0\"\n                        @focus=\"onFirstHiddenFocus\"\n                        v-bind=\"ptm('hiddenFirstFocusableEl')\"\n                        :data-p-hidden-accessible=\"true\"\n                        :data-p-hidden-focusable=\"true\"\n                    ></span>\n                    <slot name=\"header\" :value=\"d_value\" :options=\"visibleOptions\"></slot>\n                    <div v-if=\"filter\" :class=\"cx('header')\" v-bind=\"ptm('header')\">\n                        <IconField :unstyled=\"unstyled\" :pt=\"ptm('pcFilterContainer')\">\n                            <InputText\n                                ref=\"filterInput\"\n                                type=\"text\"\n                                :value=\"filterValue\"\n                                @vue:mounted=\"onFilterUpdated\"\n                                @vue:updated=\"onFilterUpdated\"\n                                :class=\"cx('pcFilter')\"\n                                :placeholder=\"filterPlaceholder\"\n                                :variant=\"variant\"\n                                :unstyled=\"unstyled\"\n                                role=\"searchbox\"\n                                autocomplete=\"off\"\n                                :aria-owns=\"id + '_list'\"\n                                :aria-activedescendant=\"focusedOptionId\"\n                                @keydown=\"onFilterKeyDown\"\n                                @blur=\"onFilterBlur\"\n                                @input=\"onFilterChange\"\n                                :pt=\"ptm('pcFilter')\"\n                            />\n                            <InputIcon :unstyled=\"unstyled\" :pt=\"ptm('pcFilterIconContainer')\">\n                                <slot name=\"filtericon\">\n                                    <span v-if=\"filterIcon\" :class=\"filterIcon\" v-bind=\"ptm('filterIcon')\" />\n                                    <SearchIcon v-else v-bind=\"ptm('filterIcon')\" />\n                                </slot>\n                            </InputIcon>\n                        </IconField>\n                        <span role=\"status\" aria-live=\"polite\" class=\"p-hidden-accessible\" v-bind=\"ptm('hiddenFilterResult')\" :data-p-hidden-accessible=\"true\">\n                            {{ filterResultMessageText }}\n                        </span>\n                    </div>\n                    <div :class=\"cx('listContainer')\" :style=\"{ 'max-height': virtualScrollerDisabled ? scrollHeight : '' }\" v-bind=\"ptm('listContainer')\">\n                        <VirtualScroller :ref=\"virtualScrollerRef\" v-bind=\"virtualScrollerOptions\" :items=\"visibleOptions\" :style=\"{ height: scrollHeight }\" :tabindex=\"-1\" :disabled=\"virtualScrollerDisabled\" :pt=\"ptm('virtualScroller')\">\n                            <template v-slot:content=\"{ styleClass, contentRef, items, getItemOptions, contentStyle, itemSize }\">\n                                <ul :ref=\"(el) => listRef(el, contentRef)\" :id=\"id + '_list'\" :class=\"[cx('list'), styleClass]\" :style=\"contentStyle\" role=\"listbox\" v-bind=\"ptm('list')\">\n                                    <template v-for=\"(option, i) of items\" :key=\"getOptionRenderKey(option, getOptionIndex(i, getItemOptions))\">\n                                        <li\n                                            v-if=\"isOptionGroup(option)\"\n                                            :id=\"id + '_' + getOptionIndex(i, getItemOptions)\"\n                                            :style=\"{ height: itemSize ? itemSize + 'px' : undefined }\"\n                                            :class=\"cx('optionGroup')\"\n                                            role=\"option\"\n                                            v-bind=\"ptm('optionGroup')\"\n                                        >\n                                            <slot name=\"optiongroup\" :option=\"option.optionGroup\" :index=\"getOptionIndex(i, getItemOptions)\">\n                                                <span :class=\"cx('optionGroupLabel')\" v-bind=\"ptm('optionGroupLabel')\">{{ getOptionGroupLabel(option.optionGroup) }}</span>\n                                            </slot>\n                                        </li>\n                                        <li\n                                            v-else\n                                            :id=\"id + '_' + getOptionIndex(i, getItemOptions)\"\n                                            v-ripple\n                                            :class=\"cx('option', { option, focusedOption: getOptionIndex(i, getItemOptions) })\"\n                                            :style=\"{ height: itemSize ? itemSize + 'px' : undefined }\"\n                                            role=\"option\"\n                                            :aria-label=\"getOptionLabel(option)\"\n                                            :aria-selected=\"isSelected(option)\"\n                                            :aria-disabled=\"isOptionDisabled(option)\"\n                                            :aria-setsize=\"ariaSetSize\"\n                                            :aria-posinset=\"getAriaPosInset(getOptionIndex(i, getItemOptions))\"\n                                            @click=\"onOptionSelect($event, option)\"\n                                            @mousemove=\"onOptionMouseMove($event, getOptionIndex(i, getItemOptions))\"\n                                            :data-p-selected=\"isSelected(option)\"\n                                            :data-p-focused=\"focusedOptionIndex === getOptionIndex(i, getItemOptions)\"\n                                            :data-p-disabled=\"isOptionDisabled(option)\"\n                                            v-bind=\"getPTItemOptions(option, getItemOptions, i, 'option')\"\n                                        >\n                                            <template v-if=\"checkmark\">\n                                                <CheckIcon v-if=\"isSelected(option)\" :class=\"cx('optionCheckIcon')\" v-bind=\"ptm('optionCheckIcon')\" />\n                                                <BlankIcon v-else :class=\"cx('optionBlankIcon')\" v-bind=\"ptm('optionBlankIcon')\" />\n                                            </template>\n                                            <slot name=\"option\" :option=\"option\" :selected=\"isSelected(option)\" :index=\"getOptionIndex(i, getItemOptions)\">\n                                                <span :class=\"cx('optionLabel')\" v-bind=\"ptm('optionLabel')\">{{ getOptionLabel(option) }}</span>\n                                            </slot>\n                                        </li>\n                                    </template>\n                                    <li v-if=\"filterValue && (!items || (items && items.length === 0))\" :class=\"cx('emptyMessage')\" role=\"option\" v-bind=\"ptm('emptyMessage')\" :data-p-hidden-accessible=\"true\">\n                                        <slot name=\"emptyfilter\">{{ emptyFilterMessageText }}</slot>\n                                    </li>\n                                    <li v-else-if=\"!options || (options && options.length === 0)\" :class=\"cx('emptyMessage')\" role=\"option\" v-bind=\"ptm('emptyMessage')\" :data-p-hidden-accessible=\"true\">\n                                        <slot name=\"empty\">{{ emptyMessageText }}</slot>\n                                    </li>\n                                </ul>\n                            </template>\n                            <template v-if=\"$slots.loader\" v-slot:loader=\"{ options }\">\n                                <slot name=\"loader\" :options=\"options\"></slot>\n                            </template>\n                        </VirtualScroller>\n                    </div>\n                    <slot name=\"footer\" :value=\"d_value\" :options=\"visibleOptions\"></slot>\n                    <span v-if=\"!options || (options && options.length === 0)\" role=\"status\" aria-live=\"polite\" class=\"p-hidden-accessible\" v-bind=\"ptm('hiddenEmptyMessage')\" :data-p-hidden-accessible=\"true\">\n                        {{ emptyMessageText }}\n                    </span>\n                    <span role=\"status\" aria-live=\"polite\" class=\"p-hidden-accessible\" v-bind=\"ptm('hiddenSelectedMessage')\" :data-p-hidden-accessible=\"true\">\n                        {{ selectedMessageText }}\n                    </span>\n                    <span\n                        ref=\"lastHiddenFocusableElementOnOverlay\"\n                        role=\"presentation\"\n                        aria-hidden=\"true\"\n                        class=\"p-hidden-accessible p-hidden-focusable\"\n                        :tabindex=\"0\"\n                        @focus=\"onLastHiddenFocus\"\n                        v-bind=\"ptm('hiddenLastFocusableEl')\"\n                        :data-p-hidden-accessible=\"true\"\n                        :data-p-hidden-focusable=\"true\"\n                    ></span>\n                </div>\n            </transition>\n        </Portal>\n    </div>\n</template>\n\n<script>\nimport { absolutePosition, addStyle, findSingle, focus, getFirstFocusableElement, getFocusableElements, getLastFocusableElement, getOuterWidth, isAndroid, isTouchDevice, isVisible, relativePosition } from '@primeuix/utils/dom';\nimport { equals, findLastIndex, isNotEmpty, isPrintableCharacter, resolveFieldData } from '@primeuix/utils/object';\nimport { ZIndex } from '@primeuix/utils/zindex';\nimport { FilterService } from '@primevue/core/api';\nimport { ConnectedOverlayScrollHandler, UniqueComponentId } from '@primevue/core/utils';\nimport BlankIcon from '@primevue/icons/blank';\nimport CheckIcon from '@primevue/icons/check';\nimport ChevronDownIcon from '@primevue/icons/chevrondown';\nimport SearchIcon from '@primevue/icons/search';\nimport SpinnerIcon from '@primevue/icons/spinner';\nimport TimesIcon from '@primevue/icons/times';\nimport IconField from 'primevue/iconfield';\nimport InputIcon from 'primevue/inputicon';\nimport InputText from 'primevue/inputtext';\nimport OverlayEventBus from 'primevue/overlayeventbus';\nimport Portal from 'primevue/portal';\nimport Ripple from 'primevue/ripple';\nimport VirtualScroller from 'primevue/virtualscroller';\nimport BaseSelect from './BaseSelect.vue';\n\nexport default {\n    name: 'Select',\n    extends: BaseSelect,\n    inheritAttrs: false,\n    emits: ['change', 'focus', 'blur', 'before-show', 'before-hide', 'show', 'hide', 'filter'],\n    outsideClickListener: null,\n    scrollHandler: null,\n    resizeListener: null,\n    labelClickListener: null,\n    overlay: null,\n    list: null,\n    virtualScroller: null,\n    searchTimeout: null,\n    searchValue: null,\n    isModelValueChanged: false,\n    data() {\n        return {\n            id: this.$attrs.id,\n            clicked: false,\n            focused: false,\n            focusedOptionIndex: -1,\n            filterValue: null,\n            overlayVisible: false\n        };\n    },\n    watch: {\n        '$attrs.id': function (newValue) {\n            this.id = newValue || UniqueComponentId();\n        },\n        modelValue() {\n            this.isModelValueChanged = true;\n        },\n        options() {\n            this.autoUpdateModel();\n        }\n    },\n    mounted() {\n        this.id = this.id || UniqueComponentId();\n        this.autoUpdateModel();\n        this.bindLabelClickListener();\n    },\n    updated() {\n        if (this.overlayVisible && this.isModelValueChanged) {\n            this.scrollInView(this.findSelectedOptionIndex());\n        }\n\n        this.isModelValueChanged = false;\n    },\n    beforeUnmount() {\n        this.unbindOutsideClickListener();\n        this.unbindResizeListener();\n        this.unbindLabelClickListener();\n\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n\n        if (this.overlay) {\n            ZIndex.clear(this.overlay);\n            this.overlay = null;\n        }\n    },\n    methods: {\n        getOptionIndex(index, fn) {\n            return this.virtualScrollerDisabled ? index : fn && fn(index)['index'];\n        },\n        getOptionLabel(option) {\n            return this.optionLabel ? resolveFieldData(option, this.optionLabel) : option;\n        },\n        getOptionValue(option) {\n            return this.optionValue ? resolveFieldData(option, this.optionValue) : option;\n        },\n        getOptionRenderKey(option, index) {\n            return (this.dataKey ? resolveFieldData(option, this.dataKey) : this.getOptionLabel(option)) + '_' + index;\n        },\n        getPTItemOptions(option, itemOptions, index, key) {\n            return this.ptm(key, {\n                context: {\n                    option,\n                    index,\n                    selected: this.isSelected(option),\n                    focused: this.focusedOptionIndex === this.getOptionIndex(index, itemOptions),\n                    disabled: this.isOptionDisabled(option)\n                }\n            });\n        },\n        isOptionDisabled(option) {\n            return this.optionDisabled ? resolveFieldData(option, this.optionDisabled) : false;\n        },\n        isOptionGroup(option) {\n            return this.optionGroupLabel && option.optionGroup && option.group;\n        },\n        getOptionGroupLabel(optionGroup) {\n            return resolveFieldData(optionGroup, this.optionGroupLabel);\n        },\n        getOptionGroupChildren(optionGroup) {\n            return resolveFieldData(optionGroup, this.optionGroupChildren);\n        },\n        getAriaPosInset(index) {\n            return (this.optionGroupLabel ? index - this.visibleOptions.slice(0, index).filter((option) => this.isOptionGroup(option)).length : index) + 1;\n        },\n        show(isFocus) {\n            this.$emit('before-show');\n            this.overlayVisible = true;\n            this.focusedOptionIndex = this.focusedOptionIndex !== -1 ? this.focusedOptionIndex : this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : this.editable ? -1 : this.findSelectedOptionIndex();\n\n            isFocus && focus(this.$refs.focusInput);\n        },\n        hide(isFocus) {\n            const _hide = () => {\n                this.$emit('before-hide');\n                this.overlayVisible = false;\n                this.clicked = false;\n                this.focusedOptionIndex = -1;\n                this.searchValue = '';\n\n                this.resetFilterOnHide && (this.filterValue = null);\n                isFocus && focus(this.$refs.focusInput);\n            };\n\n            setTimeout(() => {\n                _hide();\n            }, 0); // For ScreenReaders\n        },\n        onFocus(event) {\n            if (this.disabled) {\n                // For ScreenReaders\n                return;\n            }\n\n            this.focused = true;\n\n            if (this.overlayVisible) {\n                this.focusedOptionIndex = this.focusedOptionIndex !== -1 ? this.focusedOptionIndex : this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : this.editable ? -1 : this.findSelectedOptionIndex();\n                this.scrollInView(this.focusedOptionIndex);\n            }\n\n            this.$emit('focus', event);\n        },\n        onBlur(event) {\n            this.focused = false;\n            this.focusedOptionIndex = -1;\n            this.searchValue = '';\n            this.$emit('blur', event);\n            this.formField.onBlur?.(event);\n        },\n        onKeyDown(event) {\n            if (this.disabled || isAndroid()) {\n                event.preventDefault();\n\n                return;\n            }\n\n            const metaKey = event.metaKey || event.ctrlKey;\n\n            switch (event.code) {\n                case 'ArrowDown':\n                    this.onArrowDownKey(event);\n                    break;\n\n                case 'ArrowUp':\n                    this.onArrowUpKey(event, this.editable);\n                    break;\n\n                case 'ArrowLeft':\n                case 'ArrowRight':\n                    this.onArrowLeftKey(event, this.editable);\n                    break;\n\n                case 'Home':\n                    this.onHomeKey(event, this.editable);\n                    break;\n\n                case 'End':\n                    this.onEndKey(event, this.editable);\n                    break;\n\n                case 'PageDown':\n                    this.onPageDownKey(event);\n                    break;\n\n                case 'PageUp':\n                    this.onPageUpKey(event);\n                    break;\n\n                case 'Space':\n                    this.onSpaceKey(event, this.editable);\n                    break;\n\n                case 'Enter':\n                case 'NumpadEnter':\n                    this.onEnterKey(event);\n                    break;\n\n                case 'Escape':\n                    this.onEscapeKey(event);\n                    break;\n\n                case 'Tab':\n                    this.onTabKey(event);\n                    break;\n\n                case 'Backspace':\n                    this.onBackspaceKey(event, this.editable);\n                    break;\n\n                case 'ShiftLeft':\n                case 'ShiftRight':\n                    //NOOP\n                    break;\n\n                default:\n                    if (!metaKey && isPrintableCharacter(event.key)) {\n                        !this.overlayVisible && this.show();\n                        !this.editable && this.searchOptions(event, event.key);\n                    }\n\n                    break;\n            }\n\n            this.clicked = false;\n        },\n        onEditableInput(event) {\n            const value = event.target.value;\n\n            this.searchValue = '';\n            const matched = this.searchOptions(event, value);\n\n            !matched && (this.focusedOptionIndex = -1);\n\n            this.updateModel(event, value);\n\n            !this.overlayVisible && isNotEmpty(value) && this.show();\n        },\n        onContainerClick(event) {\n            if (this.disabled || this.loading) {\n                return;\n            }\n\n            if (event.target.tagName === 'INPUT' || event.target.getAttribute('data-pc-section') === 'clearicon' || event.target.closest('[data-pc-section=\"clearicon\"]')) {\n                return;\n            } else if (!this.overlay || !this.overlay.contains(event.target)) {\n                this.overlayVisible ? this.hide(true) : this.show(true);\n            }\n\n            this.clicked = true;\n        },\n        onClearClick(event) {\n            this.updateModel(event, null);\n            this.resetFilterOnClear && (this.filterValue = null);\n        },\n        onFirstHiddenFocus(event) {\n            const focusableEl = event.relatedTarget === this.$refs.focusInput ? getFirstFocusableElement(this.overlay, ':not([data-p-hidden-focusable=\"true\"])') : this.$refs.focusInput;\n\n            focus(focusableEl);\n        },\n        onLastHiddenFocus(event) {\n            const focusableEl = event.relatedTarget === this.$refs.focusInput ? getLastFocusableElement(this.overlay, ':not([data-p-hidden-focusable=\"true\"])') : this.$refs.focusInput;\n\n            focus(focusableEl);\n        },\n        onOptionSelect(event, option, isHide = true) {\n            const value = this.getOptionValue(option);\n\n            this.updateModel(event, value);\n            isHide && this.hide(true);\n        },\n        onOptionMouseMove(event, index) {\n            if (this.focusOnHover) {\n                this.changeFocusedOptionIndex(event, index);\n            }\n        },\n        onFilterChange(event) {\n            const value = event.target.value;\n\n            this.filterValue = value;\n            this.focusedOptionIndex = -1;\n            this.$emit('filter', { originalEvent: event, value });\n\n            !this.virtualScrollerDisabled && this.virtualScroller.scrollToIndex(0);\n        },\n        onFilterKeyDown(event) {\n            // Check if the event is part of a text composition process (e.g., for Asian languages).\n            // If event.isComposing is true, it means the user is still composing text and the input is not finalized.\n            if (event.isComposing) return;\n\n            switch (event.code) {\n                case 'ArrowDown':\n                    this.onArrowDownKey(event);\n                    break;\n\n                case 'ArrowUp':\n                    this.onArrowUpKey(event, true);\n                    break;\n\n                case 'ArrowLeft':\n                case 'ArrowRight':\n                    this.onArrowLeftKey(event, true);\n                    break;\n\n                case 'Home':\n                    this.onHomeKey(event, true);\n                    break;\n\n                case 'End':\n                    this.onEndKey(event, true);\n                    break;\n\n                case 'Enter':\n                case 'NumpadEnter':\n                    this.onEnterKey(event);\n                    break;\n\n                case 'Escape':\n                    this.onEscapeKey(event);\n                    break;\n\n                case 'Tab':\n                    this.onTabKey(event, true);\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onFilterBlur() {\n            this.focusedOptionIndex = -1;\n        },\n        onFilterUpdated() {\n            if (this.overlayVisible) {\n                this.alignOverlay();\n            }\n        },\n        onOverlayClick(event) {\n            OverlayEventBus.emit('overlay-click', {\n                originalEvent: event,\n                target: this.$el\n            });\n        },\n        onOverlayKeyDown(event) {\n            switch (event.code) {\n                case 'Escape':\n                    this.onEscapeKey(event);\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onArrowDownKey(event) {\n            if (!this.overlayVisible) {\n                this.show();\n                this.editable && this.changeFocusedOptionIndex(event, this.findSelectedOptionIndex());\n            } else {\n                const optionIndex = this.focusedOptionIndex !== -1 ? this.findNextOptionIndex(this.focusedOptionIndex) : this.clicked ? this.findFirstOptionIndex() : this.findFirstFocusedOptionIndex();\n\n                this.changeFocusedOptionIndex(event, optionIndex);\n            }\n\n            event.preventDefault();\n        },\n        onArrowUpKey(event, pressedInInputText = false) {\n            if (event.altKey && !pressedInInputText) {\n                if (this.focusedOptionIndex !== -1) {\n                    this.onOptionSelect(event, this.visibleOptions[this.focusedOptionIndex]);\n                }\n\n                this.overlayVisible && this.hide();\n                event.preventDefault();\n            } else {\n                const optionIndex = this.focusedOptionIndex !== -1 ? this.findPrevOptionIndex(this.focusedOptionIndex) : this.clicked ? this.findLastOptionIndex() : this.findLastFocusedOptionIndex();\n\n                this.changeFocusedOptionIndex(event, optionIndex);\n\n                !this.overlayVisible && this.show();\n                event.preventDefault();\n            }\n        },\n        onArrowLeftKey(event, pressedInInputText = false) {\n            pressedInInputText && (this.focusedOptionIndex = -1);\n        },\n        onHomeKey(event, pressedInInputText = false) {\n            if (pressedInInputText) {\n                const target = event.currentTarget;\n\n                if (event.shiftKey) {\n                    target.setSelectionRange(0, event.target.selectionStart);\n                } else {\n                    target.setSelectionRange(0, 0);\n                    this.focusedOptionIndex = -1;\n                }\n            } else {\n                this.changeFocusedOptionIndex(event, this.findFirstOptionIndex());\n\n                !this.overlayVisible && this.show();\n            }\n\n            event.preventDefault();\n        },\n        onEndKey(event, pressedInInputText = false) {\n            if (pressedInInputText) {\n                const target = event.currentTarget;\n\n                if (event.shiftKey) {\n                    target.setSelectionRange(event.target.selectionStart, target.value.length);\n                } else {\n                    const len = target.value.length;\n\n                    target.setSelectionRange(len, len);\n                    this.focusedOptionIndex = -1;\n                }\n            } else {\n                this.changeFocusedOptionIndex(event, this.findLastOptionIndex());\n\n                !this.overlayVisible && this.show();\n            }\n\n            event.preventDefault();\n        },\n        onPageUpKey(event) {\n            this.scrollInView(0);\n            event.preventDefault();\n        },\n        onPageDownKey(event) {\n            this.scrollInView(this.visibleOptions.length - 1);\n            event.preventDefault();\n        },\n        onEnterKey(event) {\n            if (!this.overlayVisible) {\n                this.focusedOptionIndex = -1; // reset\n                this.onArrowDownKey(event);\n            } else {\n                if (this.focusedOptionIndex !== -1) {\n                    this.onOptionSelect(event, this.visibleOptions[this.focusedOptionIndex]);\n                }\n\n                this.hide();\n            }\n\n            event.preventDefault();\n        },\n        onSpaceKey(event, pressedInInputText = false) {\n            !pressedInInputText && this.onEnterKey(event);\n        },\n        onEscapeKey(event) {\n            this.overlayVisible && this.hide(true);\n            event.preventDefault();\n            event.stopPropagation(); //@todo will be changed next versionss\n        },\n        onTabKey(event, pressedInInputText = false) {\n            if (!pressedInInputText) {\n                if (this.overlayVisible && this.hasFocusableElements()) {\n                    focus(this.$refs.firstHiddenFocusableElementOnOverlay);\n\n                    event.preventDefault();\n                } else {\n                    if (this.focusedOptionIndex !== -1) {\n                        this.onOptionSelect(event, this.visibleOptions[this.focusedOptionIndex]);\n                    }\n\n                    this.overlayVisible && this.hide(this.filter);\n                }\n            }\n        },\n        onBackspaceKey(event, pressedInInputText = false) {\n            if (pressedInInputText) {\n                !this.overlayVisible && this.show();\n            }\n        },\n        onOverlayEnter(el) {\n            ZIndex.set('overlay', el, this.$primevue.config.zIndex.overlay);\n\n            addStyle(el, { position: 'absolute', top: '0', left: '0' });\n            this.alignOverlay();\n            this.scrollInView();\n\n            setTimeout(() => {\n                this.autoFilterFocus && this.filter && focus(this.$refs.filterInput.$el);\n            }, 1);\n        },\n        onOverlayAfterEnter() {\n            this.bindOutsideClickListener();\n            this.bindScrollListener();\n            this.bindResizeListener();\n\n            this.$emit('show');\n        },\n        onOverlayLeave() {\n            this.unbindOutsideClickListener();\n            this.unbindScrollListener();\n            this.unbindResizeListener();\n\n            if (this.autoFilterFocus && this.filter && !this.editable) {\n                this.$nextTick(() => {\n                    focus(this.$refs.filterInput.$el);\n                });\n            }\n\n            this.$emit('hide');\n            this.overlay = null;\n        },\n        onOverlayAfterLeave(el) {\n            ZIndex.clear(el);\n        },\n        alignOverlay() {\n            if (this.appendTo === 'self') {\n                relativePosition(this.overlay, this.$el);\n            } else {\n                this.overlay.style.minWidth = getOuterWidth(this.$el) + 'px';\n                absolutePosition(this.overlay, this.$el);\n            }\n        },\n        bindOutsideClickListener() {\n            if (!this.outsideClickListener) {\n                this.outsideClickListener = (event) => {\n                    if (this.overlayVisible && this.overlay && !this.$el.contains(event.target) && !this.overlay.contains(event.target)) {\n                        this.hide();\n                    }\n                };\n\n                document.addEventListener('click', this.outsideClickListener);\n            }\n        },\n        unbindOutsideClickListener() {\n            if (this.outsideClickListener) {\n                document.removeEventListener('click', this.outsideClickListener);\n                this.outsideClickListener = null;\n            }\n        },\n        bindScrollListener() {\n            if (!this.scrollHandler) {\n                this.scrollHandler = new ConnectedOverlayScrollHandler(this.$refs.container, () => {\n                    if (this.overlayVisible) {\n                        this.hide();\n                    }\n                });\n            }\n\n            this.scrollHandler.bindScrollListener();\n        },\n        unbindScrollListener() {\n            if (this.scrollHandler) {\n                this.scrollHandler.unbindScrollListener();\n            }\n        },\n        bindResizeListener() {\n            if (!this.resizeListener) {\n                this.resizeListener = () => {\n                    if (this.overlayVisible && !isTouchDevice()) {\n                        this.hide();\n                    }\n                };\n\n                window.addEventListener('resize', this.resizeListener);\n            }\n        },\n        unbindResizeListener() {\n            if (this.resizeListener) {\n                window.removeEventListener('resize', this.resizeListener);\n                this.resizeListener = null;\n            }\n        },\n        bindLabelClickListener() {\n            if (!this.editable && !this.labelClickListener) {\n                const label = document.querySelector(`label[for=\"${this.labelId}\"]`);\n\n                if (label && isVisible(label)) {\n                    this.labelClickListener = () => {\n                        focus(this.$refs.focusInput);\n                    };\n\n                    label.addEventListener('click', this.labelClickListener);\n                }\n            }\n        },\n        unbindLabelClickListener() {\n            if (this.labelClickListener) {\n                const label = document.querySelector(`label[for=\"${this.labelId}\"]`);\n\n                if (label && isVisible(label)) {\n                    label.removeEventListener('click', this.labelClickListener);\n                }\n            }\n        },\n        hasFocusableElements() {\n            return getFocusableElements(this.overlay, ':not([data-p-hidden-focusable=\"true\"])').length > 0;\n        },\n        isOptionMatched(option) {\n            return this.isValidOption(option) && typeof this.getOptionLabel(option) === 'string' && this.getOptionLabel(option)?.toLocaleLowerCase(this.filterLocale).startsWith(this.searchValue.toLocaleLowerCase(this.filterLocale));\n        },\n        isValidOption(option) {\n            return isNotEmpty(option) && !(this.isOptionDisabled(option) || this.isOptionGroup(option));\n        },\n        isValidSelectedOption(option) {\n            return this.isValidOption(option) && this.isSelected(option);\n        },\n        isSelected(option) {\n            return equals(this.d_value, this.getOptionValue(option), this.equalityKey);\n        },\n        findFirstOptionIndex() {\n            return this.visibleOptions.findIndex((option) => this.isValidOption(option));\n        },\n        findLastOptionIndex() {\n            return findLastIndex(this.visibleOptions, (option) => this.isValidOption(option));\n        },\n        findNextOptionIndex(index) {\n            const matchedOptionIndex = index < this.visibleOptions.length - 1 ? this.visibleOptions.slice(index + 1).findIndex((option) => this.isValidOption(option)) : -1;\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex + index + 1 : index;\n        },\n        findPrevOptionIndex(index) {\n            const matchedOptionIndex = index > 0 ? findLastIndex(this.visibleOptions.slice(0, index), (option) => this.isValidOption(option)) : -1;\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex : index;\n        },\n        findSelectedOptionIndex() {\n            return this.$filled ? this.visibleOptions.findIndex((option) => this.isValidSelectedOption(option)) : -1;\n        },\n        findFirstFocusedOptionIndex() {\n            const selectedIndex = this.findSelectedOptionIndex();\n\n            return selectedIndex < 0 ? this.findFirstOptionIndex() : selectedIndex;\n        },\n        findLastFocusedOptionIndex() {\n            const selectedIndex = this.findSelectedOptionIndex();\n\n            return selectedIndex < 0 ? this.findLastOptionIndex() : selectedIndex;\n        },\n        searchOptions(event, char) {\n            this.searchValue = (this.searchValue || '') + char;\n\n            let optionIndex = -1;\n            let matched = false;\n\n            if (isNotEmpty(this.searchValue)) {\n                if (this.focusedOptionIndex !== -1) {\n                    optionIndex = this.visibleOptions.slice(this.focusedOptionIndex).findIndex((option) => this.isOptionMatched(option));\n                    optionIndex = optionIndex === -1 ? this.visibleOptions.slice(0, this.focusedOptionIndex).findIndex((option) => this.isOptionMatched(option)) : optionIndex + this.focusedOptionIndex;\n                } else {\n                    optionIndex = this.visibleOptions.findIndex((option) => this.isOptionMatched(option));\n                }\n\n                if (optionIndex !== -1) {\n                    matched = true;\n                }\n\n                if (optionIndex === -1 && this.focusedOptionIndex === -1) {\n                    optionIndex = this.findFirstFocusedOptionIndex();\n                }\n\n                if (optionIndex !== -1) {\n                    this.changeFocusedOptionIndex(event, optionIndex);\n                }\n            }\n\n            if (this.searchTimeout) {\n                clearTimeout(this.searchTimeout);\n            }\n\n            this.searchTimeout = setTimeout(() => {\n                this.searchValue = '';\n                this.searchTimeout = null;\n            }, 500);\n\n            return matched;\n        },\n        changeFocusedOptionIndex(event, index) {\n            if (this.focusedOptionIndex !== index) {\n                this.focusedOptionIndex = index;\n                this.scrollInView();\n\n                if (this.selectOnFocus) {\n                    this.onOptionSelect(event, this.visibleOptions[index], false);\n                }\n            }\n        },\n        scrollInView(index = -1) {\n            this.$nextTick(() => {\n                const id = index !== -1 ? `${this.id}_${index}` : this.focusedOptionId;\n                const element = findSingle(this.list, `li[id=\"${id}\"]`);\n\n                if (element) {\n                    element.scrollIntoView && element.scrollIntoView({ block: 'nearest', inline: 'start' });\n                } else if (!this.virtualScrollerDisabled) {\n                    this.virtualScroller && this.virtualScroller.scrollToIndex(index !== -1 ? index : this.focusedOptionIndex);\n                }\n            });\n        },\n        autoUpdateModel() {\n            if (this.selectOnFocus && this.autoOptionFocus && !this.$filled) {\n                this.focusedOptionIndex = this.findFirstFocusedOptionIndex();\n                this.onOptionSelect(null, this.visibleOptions[this.focusedOptionIndex], false);\n            }\n        },\n        updateModel(event, value) {\n            this.writeValue(value, event);\n            this.$emit('change', { originalEvent: event, value });\n        },\n        flatOptions(options) {\n            return (options || []).reduce((result, option, index) => {\n                result.push({ optionGroup: option, group: true, index });\n\n                const optionGroupChildren = this.getOptionGroupChildren(option);\n\n                optionGroupChildren && optionGroupChildren.forEach((o) => result.push(o));\n\n                return result;\n            }, []);\n        },\n        overlayRef(el) {\n            this.overlay = el;\n        },\n        listRef(el, contentRef) {\n            this.list = el;\n            contentRef && contentRef(el); // For VirtualScroller\n        },\n        virtualScrollerRef(el) {\n            this.virtualScroller = el;\n        }\n    },\n    computed: {\n        visibleOptions() {\n            const options = this.optionGroupLabel ? this.flatOptions(this.options) : this.options || [];\n\n            if (this.filterValue) {\n                const filteredOptions = FilterService.filter(options, this.searchFields, this.filterValue, this.filterMatchMode, this.filterLocale);\n\n                if (this.optionGroupLabel) {\n                    const optionGroups = this.options || [];\n                    const filtered = [];\n\n                    optionGroups.forEach((group) => {\n                        const groupChildren = this.getOptionGroupChildren(group);\n                        const filteredItems = groupChildren.filter((item) => filteredOptions.includes(item));\n\n                        if (filteredItems.length > 0) filtered.push({ ...group, [typeof this.optionGroupChildren === 'string' ? this.optionGroupChildren : 'items']: [...filteredItems] });\n                    });\n\n                    return this.flatOptions(filtered);\n                }\n\n                return filteredOptions;\n            }\n\n            return options;\n        },\n        // @deprecated use $filled instead\n        hasSelectedOption() {\n            return this.$filled;\n        },\n        label() {\n            const selectedOptionIndex = this.findSelectedOptionIndex();\n\n            return selectedOptionIndex !== -1 ? this.getOptionLabel(this.visibleOptions[selectedOptionIndex]) : this.placeholder || 'p-emptylabel';\n        },\n        editableInputValue() {\n            const selectedOptionIndex = this.findSelectedOptionIndex();\n\n            return selectedOptionIndex !== -1 ? this.getOptionLabel(this.visibleOptions[selectedOptionIndex]) : this.d_value || '';\n        },\n        equalityKey() {\n            return this.optionValue ? null : this.dataKey;\n        },\n        searchFields() {\n            return this.filterFields || [this.optionLabel];\n        },\n        filterResultMessageText() {\n            return isNotEmpty(this.visibleOptions) ? this.filterMessageText.replaceAll('{0}', this.visibleOptions.length) : this.emptyFilterMessageText;\n        },\n        filterMessageText() {\n            return this.filterMessage || this.$primevue.config.locale.searchMessage || '';\n        },\n        emptyFilterMessageText() {\n            return this.emptyFilterMessage || this.$primevue.config.locale.emptySearchMessage || this.$primevue.config.locale.emptyFilterMessage || '';\n        },\n        emptyMessageText() {\n            return this.emptyMessage || this.$primevue.config.locale.emptyMessage || '';\n        },\n        selectionMessageText() {\n            return this.selectionMessage || this.$primevue.config.locale.selectionMessage || '';\n        },\n        emptySelectionMessageText() {\n            return this.emptySelectionMessage || this.$primevue.config.locale.emptySelectionMessage || '';\n        },\n        selectedMessageText() {\n            return this.$filled ? this.selectionMessageText.replaceAll('{0}', '1') : this.emptySelectionMessageText;\n        },\n        focusedOptionId() {\n            return this.focusedOptionIndex !== -1 ? `${this.id}_${this.focusedOptionIndex}` : null;\n        },\n        ariaSetSize() {\n            return this.visibleOptions.filter((option) => !this.isOptionGroup(option)).length;\n        },\n        isClearIconVisible() {\n            return this.showClear && this.d_value != null && isNotEmpty(this.options);\n        },\n        virtualScrollerDisabled() {\n            return !this.virtualScrollerOptions;\n        }\n    },\n    directives: {\n        ripple: Ripple\n    },\n    components: {\n        InputText,\n        VirtualScroller,\n        Portal,\n        InputIcon,\n        IconField,\n        TimesIcon,\n        ChevronDownIcon,\n        SpinnerIcon,\n        SearchIcon,\n        CheckIcon,\n        BlankIcon\n    }\n};\n</script>\n", "<template>\n    <div ref=\"container\" :id=\"id\" :class=\"cx('root')\" @click=\"onContainerClick\" v-bind=\"ptmi('root')\">\n        <input\n            v-if=\"editable\"\n            ref=\"focusInput\"\n            :id=\"labelId || inputId\"\n            type=\"text\"\n            :class=\"[cx('label'), inputClass, labelClass]\"\n            :style=\"[inputStyle, labelStyle]\"\n            :value=\"editableInputValue\"\n            :placeholder=\"placeholder\"\n            :tabindex=\"!disabled ? tabindex : -1\"\n            :disabled=\"disabled\"\n            autocomplete=\"off\"\n            role=\"combobox\"\n            :aria-label=\"ariaLabel\"\n            :aria-labelledby=\"ariaLabelledby\"\n            aria-haspopup=\"listbox\"\n            :aria-expanded=\"overlayVisible\"\n            :aria-controls=\"id + '_list'\"\n            :aria-activedescendant=\"focused ? focusedOptionId : undefined\"\n            :aria-invalid=\"invalid || undefined\"\n            @focus=\"onFocus\"\n            @blur=\"onBlur\"\n            @keydown=\"onKeyDown\"\n            @input=\"onEditableInput\"\n            v-bind=\"ptm('label')\"\n        />\n        <span\n            v-else\n            ref=\"focusInput\"\n            :id=\"labelId || inputId\"\n            :class=\"[cx('label'), inputClass, labelClass]\"\n            :style=\"[inputStyle, labelStyle]\"\n            :tabindex=\"!disabled ? tabindex : -1\"\n            role=\"combobox\"\n            :aria-label=\"ariaLabel || (label === 'p-emptylabel' ? undefined : label)\"\n            :aria-labelledby=\"ariaLabelledby\"\n            aria-haspopup=\"listbox\"\n            :aria-expanded=\"overlayVisible\"\n            :aria-controls=\"id + '_list'\"\n            :aria-activedescendant=\"focused ? focusedOptionId : undefined\"\n            :aria-disabled=\"disabled\"\n            @focus=\"onFocus\"\n            @blur=\"onBlur\"\n            @keydown=\"onKeyDown\"\n            v-bind=\"ptm('label')\"\n        >\n            <slot name=\"value\" :value=\"d_value\" :placeholder=\"placeholder\">{{ label === 'p-emptylabel' ? '&nbsp;' : label ?? 'empty' }}</slot>\n        </span>\n        <slot v-if=\"isClearIconVisible\" name=\"clearicon\" :class=\"cx('clearIcon')\" :clearCallback=\"onClearClick\">\n            <component :is=\"clearIcon ? 'i' : 'TimesIcon'\" ref=\"clearIcon\" :class=\"[cx('clearIcon'), clearIcon]\" @click=\"onClearClick\" v-bind=\"ptm('clearIcon')\" data-pc-section=\"clearicon\" />\n        </slot>\n        <div :class=\"cx('dropdown')\" v-bind=\"ptm('dropdown')\">\n            <slot v-if=\"loading\" name=\"loadingicon\" :class=\"cx('loadingIcon')\">\n                <span v-if=\"loadingIcon\" :class=\"[cx('loadingIcon'), 'pi-spin', loadingIcon]\" aria-hidden=\"true\" v-bind=\"ptm('loadingIcon')\" />\n                <SpinnerIcon v-else :class=\"cx('loadingIcon')\" spin aria-hidden=\"true\" v-bind=\"ptm('loadingIcon')\" />\n            </slot>\n            <slot v-else name=\"dropdownicon\" :class=\"cx('dropdownIcon')\">\n                <component :is=\"dropdownIcon ? 'span' : 'ChevronDownIcon'\" :class=\"[cx('dropdownIcon'), dropdownIcon]\" aria-hidden=\"true\" v-bind=\"ptm('dropdownIcon')\" />\n            </slot>\n        </div>\n        <Portal :appendTo=\"appendTo\">\n            <transition name=\"p-connected-overlay\" @enter=\"onOverlayEnter\" @after-enter=\"onOverlayAfterEnter\" @leave=\"onOverlayLeave\" @after-leave=\"onOverlayAfterLeave\" v-bind=\"ptm('transition')\">\n                <div v-if=\"overlayVisible\" :ref=\"overlayRef\" :class=\"[cx('overlay'), panelClass, overlayClass]\" :style=\"[panelStyle, overlayStyle]\" @click=\"onOverlayClick\" @keydown=\"onOverlayKeyDown\" v-bind=\"ptm('overlay')\">\n                    <span\n                        ref=\"firstHiddenFocusableElementOnOverlay\"\n                        role=\"presentation\"\n                        aria-hidden=\"true\"\n                        class=\"p-hidden-accessible p-hidden-focusable\"\n                        :tabindex=\"0\"\n                        @focus=\"onFirstHiddenFocus\"\n                        v-bind=\"ptm('hiddenFirstFocusableEl')\"\n                        :data-p-hidden-accessible=\"true\"\n                        :data-p-hidden-focusable=\"true\"\n                    ></span>\n                    <slot name=\"header\" :value=\"d_value\" :options=\"visibleOptions\"></slot>\n                    <div v-if=\"filter\" :class=\"cx('header')\" v-bind=\"ptm('header')\">\n                        <IconField :unstyled=\"unstyled\" :pt=\"ptm('pcFilterContainer')\">\n                            <InputText\n                                ref=\"filterInput\"\n                                type=\"text\"\n                                :value=\"filterValue\"\n                                @vue:mounted=\"onFilterUpdated\"\n                                @vue:updated=\"onFilterUpdated\"\n                                :class=\"cx('pcFilter')\"\n                                :placeholder=\"filterPlaceholder\"\n                                :variant=\"variant\"\n                                :unstyled=\"unstyled\"\n                                role=\"searchbox\"\n                                autocomplete=\"off\"\n                                :aria-owns=\"id + '_list'\"\n                                :aria-activedescendant=\"focusedOptionId\"\n                                @keydown=\"onFilterKeyDown\"\n                                @blur=\"onFilterBlur\"\n                                @input=\"onFilterChange\"\n                                :pt=\"ptm('pcFilter')\"\n                            />\n                            <InputIcon :unstyled=\"unstyled\" :pt=\"ptm('pcFilterIconContainer')\">\n                                <slot name=\"filtericon\">\n                                    <span v-if=\"filterIcon\" :class=\"filterIcon\" v-bind=\"ptm('filterIcon')\" />\n                                    <SearchIcon v-else v-bind=\"ptm('filterIcon')\" />\n                                </slot>\n                            </InputIcon>\n                        </IconField>\n                        <span role=\"status\" aria-live=\"polite\" class=\"p-hidden-accessible\" v-bind=\"ptm('hiddenFilterResult')\" :data-p-hidden-accessible=\"true\">\n                            {{ filterResultMessageText }}\n                        </span>\n                    </div>\n                    <div :class=\"cx('listContainer')\" :style=\"{ 'max-height': virtualScrollerDisabled ? scrollHeight : '' }\" v-bind=\"ptm('listContainer')\">\n                        <VirtualScroller :ref=\"virtualScrollerRef\" v-bind=\"virtualScrollerOptions\" :items=\"visibleOptions\" :style=\"{ height: scrollHeight }\" :tabindex=\"-1\" :disabled=\"virtualScrollerDisabled\" :pt=\"ptm('virtualScroller')\">\n                            <template v-slot:content=\"{ styleClass, contentRef, items, getItemOptions, contentStyle, itemSize }\">\n                                <ul :ref=\"(el) => listRef(el, contentRef)\" :id=\"id + '_list'\" :class=\"[cx('list'), styleClass]\" :style=\"contentStyle\" role=\"listbox\" v-bind=\"ptm('list')\">\n                                    <template v-for=\"(option, i) of items\" :key=\"getOptionRenderKey(option, getOptionIndex(i, getItemOptions))\">\n                                        <li\n                                            v-if=\"isOptionGroup(option)\"\n                                            :id=\"id + '_' + getOptionIndex(i, getItemOptions)\"\n                                            :style=\"{ height: itemSize ? itemSize + 'px' : undefined }\"\n                                            :class=\"cx('optionGroup')\"\n                                            role=\"option\"\n                                            v-bind=\"ptm('optionGroup')\"\n                                        >\n                                            <slot name=\"optiongroup\" :option=\"option.optionGroup\" :index=\"getOptionIndex(i, getItemOptions)\">\n                                                <span :class=\"cx('optionGroupLabel')\" v-bind=\"ptm('optionGroupLabel')\">{{ getOptionGroupLabel(option.optionGroup) }}</span>\n                                            </slot>\n                                        </li>\n                                        <li\n                                            v-else\n                                            :id=\"id + '_' + getOptionIndex(i, getItemOptions)\"\n                                            v-ripple\n                                            :class=\"cx('option', { option, focusedOption: getOptionIndex(i, getItemOptions) })\"\n                                            :style=\"{ height: itemSize ? itemSize + 'px' : undefined }\"\n                                            role=\"option\"\n                                            :aria-label=\"getOptionLabel(option)\"\n                                            :aria-selected=\"isSelected(option)\"\n                                            :aria-disabled=\"isOptionDisabled(option)\"\n                                            :aria-setsize=\"ariaSetSize\"\n                                            :aria-posinset=\"getAriaPosInset(getOptionIndex(i, getItemOptions))\"\n                                            @click=\"onOptionSelect($event, option)\"\n                                            @mousemove=\"onOptionMouseMove($event, getOptionIndex(i, getItemOptions))\"\n                                            :data-p-selected=\"isSelected(option)\"\n                                            :data-p-focused=\"focusedOptionIndex === getOptionIndex(i, getItemOptions)\"\n                                            :data-p-disabled=\"isOptionDisabled(option)\"\n                                            v-bind=\"getPTItemOptions(option, getItemOptions, i, 'option')\"\n                                        >\n                                            <template v-if=\"checkmark\">\n                                                <CheckIcon v-if=\"isSelected(option)\" :class=\"cx('optionCheckIcon')\" v-bind=\"ptm('optionCheckIcon')\" />\n                                                <BlankIcon v-else :class=\"cx('optionBlankIcon')\" v-bind=\"ptm('optionBlankIcon')\" />\n                                            </template>\n                                            <slot name=\"option\" :option=\"option\" :selected=\"isSelected(option)\" :index=\"getOptionIndex(i, getItemOptions)\">\n                                                <span :class=\"cx('optionLabel')\" v-bind=\"ptm('optionLabel')\">{{ getOptionLabel(option) }}</span>\n                                            </slot>\n                                        </li>\n                                    </template>\n                                    <li v-if=\"filterValue && (!items || (items && items.length === 0))\" :class=\"cx('emptyMessage')\" role=\"option\" v-bind=\"ptm('emptyMessage')\" :data-p-hidden-accessible=\"true\">\n                                        <slot name=\"emptyfilter\">{{ emptyFilterMessageText }}</slot>\n                                    </li>\n                                    <li v-else-if=\"!options || (options && options.length === 0)\" :class=\"cx('emptyMessage')\" role=\"option\" v-bind=\"ptm('emptyMessage')\" :data-p-hidden-accessible=\"true\">\n                                        <slot name=\"empty\">{{ emptyMessageText }}</slot>\n                                    </li>\n                                </ul>\n                            </template>\n                            <template v-if=\"$slots.loader\" v-slot:loader=\"{ options }\">\n                                <slot name=\"loader\" :options=\"options\"></slot>\n                            </template>\n                        </VirtualScroller>\n                    </div>\n                    <slot name=\"footer\" :value=\"d_value\" :options=\"visibleOptions\"></slot>\n                    <span v-if=\"!options || (options && options.length === 0)\" role=\"status\" aria-live=\"polite\" class=\"p-hidden-accessible\" v-bind=\"ptm('hiddenEmptyMessage')\" :data-p-hidden-accessible=\"true\">\n                        {{ emptyMessageText }}\n                    </span>\n                    <span role=\"status\" aria-live=\"polite\" class=\"p-hidden-accessible\" v-bind=\"ptm('hiddenSelectedMessage')\" :data-p-hidden-accessible=\"true\">\n                        {{ selectedMessageText }}\n                    </span>\n                    <span\n                        ref=\"lastHiddenFocusableElementOnOverlay\"\n                        role=\"presentation\"\n                        aria-hidden=\"true\"\n                        class=\"p-hidden-accessible p-hidden-focusable\"\n                        :tabindex=\"0\"\n                        @focus=\"onLastHiddenFocus\"\n                        v-bind=\"ptm('hiddenLastFocusableEl')\"\n                        :data-p-hidden-accessible=\"true\"\n                        :data-p-hidden-focusable=\"true\"\n                    ></span>\n                </div>\n            </transition>\n        </Portal>\n    </div>\n</template>\n\n<script>\nimport { absolutePosition, addStyle, findSingle, focus, getFirstFocusableElement, getFocusableElements, getLastFocusableElement, getOuterWidth, isAndroid, isTouchDevice, isVisible, relativePosition } from '@primeuix/utils/dom';\nimport { equals, findLastIndex, isNotEmpty, isPrintableCharacter, resolveFieldData } from '@primeuix/utils/object';\nimport { ZIndex } from '@primeuix/utils/zindex';\nimport { FilterService } from '@primevue/core/api';\nimport { ConnectedOverlayScrollHandler, UniqueComponentId } from '@primevue/core/utils';\nimport BlankIcon from '@primevue/icons/blank';\nimport CheckIcon from '@primevue/icons/check';\nimport ChevronDownIcon from '@primevue/icons/chevrondown';\nimport SearchIcon from '@primevue/icons/search';\nimport SpinnerIcon from '@primevue/icons/spinner';\nimport TimesIcon from '@primevue/icons/times';\nimport IconField from 'primevue/iconfield';\nimport InputIcon from 'primevue/inputicon';\nimport InputText from 'primevue/inputtext';\nimport OverlayEventBus from 'primevue/overlayeventbus';\nimport Portal from 'primevue/portal';\nimport Ripple from 'primevue/ripple';\nimport VirtualScroller from 'primevue/virtualscroller';\nimport BaseSelect from './BaseSelect.vue';\n\nexport default {\n    name: 'Select',\n    extends: BaseSelect,\n    inheritAttrs: false,\n    emits: ['change', 'focus', 'blur', 'before-show', 'before-hide', 'show', 'hide', 'filter'],\n    outsideClickListener: null,\n    scrollHandler: null,\n    resizeListener: null,\n    labelClickListener: null,\n    overlay: null,\n    list: null,\n    virtualScroller: null,\n    searchTimeout: null,\n    searchValue: null,\n    isModelValueChanged: false,\n    data() {\n        return {\n            id: this.$attrs.id,\n            clicked: false,\n            focused: false,\n            focusedOptionIndex: -1,\n            filterValue: null,\n            overlayVisible: false\n        };\n    },\n    watch: {\n        '$attrs.id': function (newValue) {\n            this.id = newValue || UniqueComponentId();\n        },\n        modelValue() {\n            this.isModelValueChanged = true;\n        },\n        options() {\n            this.autoUpdateModel();\n        }\n    },\n    mounted() {\n        this.id = this.id || UniqueComponentId();\n        this.autoUpdateModel();\n        this.bindLabelClickListener();\n    },\n    updated() {\n        if (this.overlayVisible && this.isModelValueChanged) {\n            this.scrollInView(this.findSelectedOptionIndex());\n        }\n\n        this.isModelValueChanged = false;\n    },\n    beforeUnmount() {\n        this.unbindOutsideClickListener();\n        this.unbindResizeListener();\n        this.unbindLabelClickListener();\n\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n\n        if (this.overlay) {\n            ZIndex.clear(this.overlay);\n            this.overlay = null;\n        }\n    },\n    methods: {\n        getOptionIndex(index, fn) {\n            return this.virtualScrollerDisabled ? index : fn && fn(index)['index'];\n        },\n        getOptionLabel(option) {\n            return this.optionLabel ? resolveFieldData(option, this.optionLabel) : option;\n        },\n        getOptionValue(option) {\n            return this.optionValue ? resolveFieldData(option, this.optionValue) : option;\n        },\n        getOptionRenderKey(option, index) {\n            return (this.dataKey ? resolveFieldData(option, this.dataKey) : this.getOptionLabel(option)) + '_' + index;\n        },\n        getPTItemOptions(option, itemOptions, index, key) {\n            return this.ptm(key, {\n                context: {\n                    option,\n                    index,\n                    selected: this.isSelected(option),\n                    focused: this.focusedOptionIndex === this.getOptionIndex(index, itemOptions),\n                    disabled: this.isOptionDisabled(option)\n                }\n            });\n        },\n        isOptionDisabled(option) {\n            return this.optionDisabled ? resolveFieldData(option, this.optionDisabled) : false;\n        },\n        isOptionGroup(option) {\n            return this.optionGroupLabel && option.optionGroup && option.group;\n        },\n        getOptionGroupLabel(optionGroup) {\n            return resolveFieldData(optionGroup, this.optionGroupLabel);\n        },\n        getOptionGroupChildren(optionGroup) {\n            return resolveFieldData(optionGroup, this.optionGroupChildren);\n        },\n        getAriaPosInset(index) {\n            return (this.optionGroupLabel ? index - this.visibleOptions.slice(0, index).filter((option) => this.isOptionGroup(option)).length : index) + 1;\n        },\n        show(isFocus) {\n            this.$emit('before-show');\n            this.overlayVisible = true;\n            this.focusedOptionIndex = this.focusedOptionIndex !== -1 ? this.focusedOptionIndex : this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : this.editable ? -1 : this.findSelectedOptionIndex();\n\n            isFocus && focus(this.$refs.focusInput);\n        },\n        hide(isFocus) {\n            const _hide = () => {\n                this.$emit('before-hide');\n                this.overlayVisible = false;\n                this.clicked = false;\n                this.focusedOptionIndex = -1;\n                this.searchValue = '';\n\n                this.resetFilterOnHide && (this.filterValue = null);\n                isFocus && focus(this.$refs.focusInput);\n            };\n\n            setTimeout(() => {\n                _hide();\n            }, 0); // For ScreenReaders\n        },\n        onFocus(event) {\n            if (this.disabled) {\n                // For ScreenReaders\n                return;\n            }\n\n            this.focused = true;\n\n            if (this.overlayVisible) {\n                this.focusedOptionIndex = this.focusedOptionIndex !== -1 ? this.focusedOptionIndex : this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : this.editable ? -1 : this.findSelectedOptionIndex();\n                this.scrollInView(this.focusedOptionIndex);\n            }\n\n            this.$emit('focus', event);\n        },\n        onBlur(event) {\n            this.focused = false;\n            this.focusedOptionIndex = -1;\n            this.searchValue = '';\n            this.$emit('blur', event);\n            this.formField.onBlur?.(event);\n        },\n        onKeyDown(event) {\n            if (this.disabled || isAndroid()) {\n                event.preventDefault();\n\n                return;\n            }\n\n            const metaKey = event.metaKey || event.ctrlKey;\n\n            switch (event.code) {\n                case 'ArrowDown':\n                    this.onArrowDownKey(event);\n                    break;\n\n                case 'ArrowUp':\n                    this.onArrowUpKey(event, this.editable);\n                    break;\n\n                case 'ArrowLeft':\n                case 'ArrowRight':\n                    this.onArrowLeftKey(event, this.editable);\n                    break;\n\n                case 'Home':\n                    this.onHomeKey(event, this.editable);\n                    break;\n\n                case 'End':\n                    this.onEndKey(event, this.editable);\n                    break;\n\n                case 'PageDown':\n                    this.onPageDownKey(event);\n                    break;\n\n                case 'PageUp':\n                    this.onPageUpKey(event);\n                    break;\n\n                case 'Space':\n                    this.onSpaceKey(event, this.editable);\n                    break;\n\n                case 'Enter':\n                case 'NumpadEnter':\n                    this.onEnterKey(event);\n                    break;\n\n                case 'Escape':\n                    this.onEscapeKey(event);\n                    break;\n\n                case 'Tab':\n                    this.onTabKey(event);\n                    break;\n\n                case 'Backspace':\n                    this.onBackspaceKey(event, this.editable);\n                    break;\n\n                case 'ShiftLeft':\n                case 'ShiftRight':\n                    //NOOP\n                    break;\n\n                default:\n                    if (!metaKey && isPrintableCharacter(event.key)) {\n                        !this.overlayVisible && this.show();\n                        !this.editable && this.searchOptions(event, event.key);\n                    }\n\n                    break;\n            }\n\n            this.clicked = false;\n        },\n        onEditableInput(event) {\n            const value = event.target.value;\n\n            this.searchValue = '';\n            const matched = this.searchOptions(event, value);\n\n            !matched && (this.focusedOptionIndex = -1);\n\n            this.updateModel(event, value);\n\n            !this.overlayVisible && isNotEmpty(value) && this.show();\n        },\n        onContainerClick(event) {\n            if (this.disabled || this.loading) {\n                return;\n            }\n\n            if (event.target.tagName === 'INPUT' || event.target.getAttribute('data-pc-section') === 'clearicon' || event.target.closest('[data-pc-section=\"clearicon\"]')) {\n                return;\n            } else if (!this.overlay || !this.overlay.contains(event.target)) {\n                this.overlayVisible ? this.hide(true) : this.show(true);\n            }\n\n            this.clicked = true;\n        },\n        onClearClick(event) {\n            this.updateModel(event, null);\n            this.resetFilterOnClear && (this.filterValue = null);\n        },\n        onFirstHiddenFocus(event) {\n            const focusableEl = event.relatedTarget === this.$refs.focusInput ? getFirstFocusableElement(this.overlay, ':not([data-p-hidden-focusable=\"true\"])') : this.$refs.focusInput;\n\n            focus(focusableEl);\n        },\n        onLastHiddenFocus(event) {\n            const focusableEl = event.relatedTarget === this.$refs.focusInput ? getLastFocusableElement(this.overlay, ':not([data-p-hidden-focusable=\"true\"])') : this.$refs.focusInput;\n\n            focus(focusableEl);\n        },\n        onOptionSelect(event, option, isHide = true) {\n            const value = this.getOptionValue(option);\n\n            this.updateModel(event, value);\n            isHide && this.hide(true);\n        },\n        onOptionMouseMove(event, index) {\n            if (this.focusOnHover) {\n                this.changeFocusedOptionIndex(event, index);\n            }\n        },\n        onFilterChange(event) {\n            const value = event.target.value;\n\n            this.filterValue = value;\n            this.focusedOptionIndex = -1;\n            this.$emit('filter', { originalEvent: event, value });\n\n            !this.virtualScrollerDisabled && this.virtualScroller.scrollToIndex(0);\n        },\n        onFilterKeyDown(event) {\n            // Check if the event is part of a text composition process (e.g., for Asian languages).\n            // If event.isComposing is true, it means the user is still composing text and the input is not finalized.\n            if (event.isComposing) return;\n\n            switch (event.code) {\n                case 'ArrowDown':\n                    this.onArrowDownKey(event);\n                    break;\n\n                case 'ArrowUp':\n                    this.onArrowUpKey(event, true);\n                    break;\n\n                case 'ArrowLeft':\n                case 'ArrowRight':\n                    this.onArrowLeftKey(event, true);\n                    break;\n\n                case 'Home':\n                    this.onHomeKey(event, true);\n                    break;\n\n                case 'End':\n                    this.onEndKey(event, true);\n                    break;\n\n                case 'Enter':\n                case 'NumpadEnter':\n                    this.onEnterKey(event);\n                    break;\n\n                case 'Escape':\n                    this.onEscapeKey(event);\n                    break;\n\n                case 'Tab':\n                    this.onTabKey(event, true);\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onFilterBlur() {\n            this.focusedOptionIndex = -1;\n        },\n        onFilterUpdated() {\n            if (this.overlayVisible) {\n                this.alignOverlay();\n            }\n        },\n        onOverlayClick(event) {\n            OverlayEventBus.emit('overlay-click', {\n                originalEvent: event,\n                target: this.$el\n            });\n        },\n        onOverlayKeyDown(event) {\n            switch (event.code) {\n                case 'Escape':\n                    this.onEscapeKey(event);\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onArrowDownKey(event) {\n            if (!this.overlayVisible) {\n                this.show();\n                this.editable && this.changeFocusedOptionIndex(event, this.findSelectedOptionIndex());\n            } else {\n                const optionIndex = this.focusedOptionIndex !== -1 ? this.findNextOptionIndex(this.focusedOptionIndex) : this.clicked ? this.findFirstOptionIndex() : this.findFirstFocusedOptionIndex();\n\n                this.changeFocusedOptionIndex(event, optionIndex);\n            }\n\n            event.preventDefault();\n        },\n        onArrowUpKey(event, pressedInInputText = false) {\n            if (event.altKey && !pressedInInputText) {\n                if (this.focusedOptionIndex !== -1) {\n                    this.onOptionSelect(event, this.visibleOptions[this.focusedOptionIndex]);\n                }\n\n                this.overlayVisible && this.hide();\n                event.preventDefault();\n            } else {\n                const optionIndex = this.focusedOptionIndex !== -1 ? this.findPrevOptionIndex(this.focusedOptionIndex) : this.clicked ? this.findLastOptionIndex() : this.findLastFocusedOptionIndex();\n\n                this.changeFocusedOptionIndex(event, optionIndex);\n\n                !this.overlayVisible && this.show();\n                event.preventDefault();\n            }\n        },\n        onArrowLeftKey(event, pressedInInputText = false) {\n            pressedInInputText && (this.focusedOptionIndex = -1);\n        },\n        onHomeKey(event, pressedInInputText = false) {\n            if (pressedInInputText) {\n                const target = event.currentTarget;\n\n                if (event.shiftKey) {\n                    target.setSelectionRange(0, event.target.selectionStart);\n                } else {\n                    target.setSelectionRange(0, 0);\n                    this.focusedOptionIndex = -1;\n                }\n            } else {\n                this.changeFocusedOptionIndex(event, this.findFirstOptionIndex());\n\n                !this.overlayVisible && this.show();\n            }\n\n            event.preventDefault();\n        },\n        onEndKey(event, pressedInInputText = false) {\n            if (pressedInInputText) {\n                const target = event.currentTarget;\n\n                if (event.shiftKey) {\n                    target.setSelectionRange(event.target.selectionStart, target.value.length);\n                } else {\n                    const len = target.value.length;\n\n                    target.setSelectionRange(len, len);\n                    this.focusedOptionIndex = -1;\n                }\n            } else {\n                this.changeFocusedOptionIndex(event, this.findLastOptionIndex());\n\n                !this.overlayVisible && this.show();\n            }\n\n            event.preventDefault();\n        },\n        onPageUpKey(event) {\n            this.scrollInView(0);\n            event.preventDefault();\n        },\n        onPageDownKey(event) {\n            this.scrollInView(this.visibleOptions.length - 1);\n            event.preventDefault();\n        },\n        onEnterKey(event) {\n            if (!this.overlayVisible) {\n                this.focusedOptionIndex = -1; // reset\n                this.onArrowDownKey(event);\n            } else {\n                if (this.focusedOptionIndex !== -1) {\n                    this.onOptionSelect(event, this.visibleOptions[this.focusedOptionIndex]);\n                }\n\n                this.hide();\n            }\n\n            event.preventDefault();\n        },\n        onSpaceKey(event, pressedInInputText = false) {\n            !pressedInInputText && this.onEnterKey(event);\n        },\n        onEscapeKey(event) {\n            this.overlayVisible && this.hide(true);\n            event.preventDefault();\n            event.stopPropagation(); //@todo will be changed next versionss\n        },\n        onTabKey(event, pressedInInputText = false) {\n            if (!pressedInInputText) {\n                if (this.overlayVisible && this.hasFocusableElements()) {\n                    focus(this.$refs.firstHiddenFocusableElementOnOverlay);\n\n                    event.preventDefault();\n                } else {\n                    if (this.focusedOptionIndex !== -1) {\n                        this.onOptionSelect(event, this.visibleOptions[this.focusedOptionIndex]);\n                    }\n\n                    this.overlayVisible && this.hide(this.filter);\n                }\n            }\n        },\n        onBackspaceKey(event, pressedInInputText = false) {\n            if (pressedInInputText) {\n                !this.overlayVisible && this.show();\n            }\n        },\n        onOverlayEnter(el) {\n            ZIndex.set('overlay', el, this.$primevue.config.zIndex.overlay);\n\n            addStyle(el, { position: 'absolute', top: '0', left: '0' });\n            this.alignOverlay();\n            this.scrollInView();\n\n            setTimeout(() => {\n                this.autoFilterFocus && this.filter && focus(this.$refs.filterInput.$el);\n            }, 1);\n        },\n        onOverlayAfterEnter() {\n            this.bindOutsideClickListener();\n            this.bindScrollListener();\n            this.bindResizeListener();\n\n            this.$emit('show');\n        },\n        onOverlayLeave() {\n            this.unbindOutsideClickListener();\n            this.unbindScrollListener();\n            this.unbindResizeListener();\n\n            if (this.autoFilterFocus && this.filter && !this.editable) {\n                this.$nextTick(() => {\n                    focus(this.$refs.filterInput.$el);\n                });\n            }\n\n            this.$emit('hide');\n            this.overlay = null;\n        },\n        onOverlayAfterLeave(el) {\n            ZIndex.clear(el);\n        },\n        alignOverlay() {\n            if (this.appendTo === 'self') {\n                relativePosition(this.overlay, this.$el);\n            } else {\n                this.overlay.style.minWidth = getOuterWidth(this.$el) + 'px';\n                absolutePosition(this.overlay, this.$el);\n            }\n        },\n        bindOutsideClickListener() {\n            if (!this.outsideClickListener) {\n                this.outsideClickListener = (event) => {\n                    if (this.overlayVisible && this.overlay && !this.$el.contains(event.target) && !this.overlay.contains(event.target)) {\n                        this.hide();\n                    }\n                };\n\n                document.addEventListener('click', this.outsideClickListener);\n            }\n        },\n        unbindOutsideClickListener() {\n            if (this.outsideClickListener) {\n                document.removeEventListener('click', this.outsideClickListener);\n                this.outsideClickListener = null;\n            }\n        },\n        bindScrollListener() {\n            if (!this.scrollHandler) {\n                this.scrollHandler = new ConnectedOverlayScrollHandler(this.$refs.container, () => {\n                    if (this.overlayVisible) {\n                        this.hide();\n                    }\n                });\n            }\n\n            this.scrollHandler.bindScrollListener();\n        },\n        unbindScrollListener() {\n            if (this.scrollHandler) {\n                this.scrollHandler.unbindScrollListener();\n            }\n        },\n        bindResizeListener() {\n            if (!this.resizeListener) {\n                this.resizeListener = () => {\n                    if (this.overlayVisible && !isTouchDevice()) {\n                        this.hide();\n                    }\n                };\n\n                window.addEventListener('resize', this.resizeListener);\n            }\n        },\n        unbindResizeListener() {\n            if (this.resizeListener) {\n                window.removeEventListener('resize', this.resizeListener);\n                this.resizeListener = null;\n            }\n        },\n        bindLabelClickListener() {\n            if (!this.editable && !this.labelClickListener) {\n                const label = document.querySelector(`label[for=\"${this.labelId}\"]`);\n\n                if (label && isVisible(label)) {\n                    this.labelClickListener = () => {\n                        focus(this.$refs.focusInput);\n                    };\n\n                    label.addEventListener('click', this.labelClickListener);\n                }\n            }\n        },\n        unbindLabelClickListener() {\n            if (this.labelClickListener) {\n                const label = document.querySelector(`label[for=\"${this.labelId}\"]`);\n\n                if (label && isVisible(label)) {\n                    label.removeEventListener('click', this.labelClickListener);\n                }\n            }\n        },\n        hasFocusableElements() {\n            return getFocusableElements(this.overlay, ':not([data-p-hidden-focusable=\"true\"])').length > 0;\n        },\n        isOptionMatched(option) {\n            return this.isValidOption(option) && typeof this.getOptionLabel(option) === 'string' && this.getOptionLabel(option)?.toLocaleLowerCase(this.filterLocale).startsWith(this.searchValue.toLocaleLowerCase(this.filterLocale));\n        },\n        isValidOption(option) {\n            return isNotEmpty(option) && !(this.isOptionDisabled(option) || this.isOptionGroup(option));\n        },\n        isValidSelectedOption(option) {\n            return this.isValidOption(option) && this.isSelected(option);\n        },\n        isSelected(option) {\n            return equals(this.d_value, this.getOptionValue(option), this.equalityKey);\n        },\n        findFirstOptionIndex() {\n            return this.visibleOptions.findIndex((option) => this.isValidOption(option));\n        },\n        findLastOptionIndex() {\n            return findLastIndex(this.visibleOptions, (option) => this.isValidOption(option));\n        },\n        findNextOptionIndex(index) {\n            const matchedOptionIndex = index < this.visibleOptions.length - 1 ? this.visibleOptions.slice(index + 1).findIndex((option) => this.isValidOption(option)) : -1;\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex + index + 1 : index;\n        },\n        findPrevOptionIndex(index) {\n            const matchedOptionIndex = index > 0 ? findLastIndex(this.visibleOptions.slice(0, index), (option) => this.isValidOption(option)) : -1;\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex : index;\n        },\n        findSelectedOptionIndex() {\n            return this.$filled ? this.visibleOptions.findIndex((option) => this.isValidSelectedOption(option)) : -1;\n        },\n        findFirstFocusedOptionIndex() {\n            const selectedIndex = this.findSelectedOptionIndex();\n\n            return selectedIndex < 0 ? this.findFirstOptionIndex() : selectedIndex;\n        },\n        findLastFocusedOptionIndex() {\n            const selectedIndex = this.findSelectedOptionIndex();\n\n            return selectedIndex < 0 ? this.findLastOptionIndex() : selectedIndex;\n        },\n        searchOptions(event, char) {\n            this.searchValue = (this.searchValue || '') + char;\n\n            let optionIndex = -1;\n            let matched = false;\n\n            if (isNotEmpty(this.searchValue)) {\n                if (this.focusedOptionIndex !== -1) {\n                    optionIndex = this.visibleOptions.slice(this.focusedOptionIndex).findIndex((option) => this.isOptionMatched(option));\n                    optionIndex = optionIndex === -1 ? this.visibleOptions.slice(0, this.focusedOptionIndex).findIndex((option) => this.isOptionMatched(option)) : optionIndex + this.focusedOptionIndex;\n                } else {\n                    optionIndex = this.visibleOptions.findIndex((option) => this.isOptionMatched(option));\n                }\n\n                if (optionIndex !== -1) {\n                    matched = true;\n                }\n\n                if (optionIndex === -1 && this.focusedOptionIndex === -1) {\n                    optionIndex = this.findFirstFocusedOptionIndex();\n                }\n\n                if (optionIndex !== -1) {\n                    this.changeFocusedOptionIndex(event, optionIndex);\n                }\n            }\n\n            if (this.searchTimeout) {\n                clearTimeout(this.searchTimeout);\n            }\n\n            this.searchTimeout = setTimeout(() => {\n                this.searchValue = '';\n                this.searchTimeout = null;\n            }, 500);\n\n            return matched;\n        },\n        changeFocusedOptionIndex(event, index) {\n            if (this.focusedOptionIndex !== index) {\n                this.focusedOptionIndex = index;\n                this.scrollInView();\n\n                if (this.selectOnFocus) {\n                    this.onOptionSelect(event, this.visibleOptions[index], false);\n                }\n            }\n        },\n        scrollInView(index = -1) {\n            this.$nextTick(() => {\n                const id = index !== -1 ? `${this.id}_${index}` : this.focusedOptionId;\n                const element = findSingle(this.list, `li[id=\"${id}\"]`);\n\n                if (element) {\n                    element.scrollIntoView && element.scrollIntoView({ block: 'nearest', inline: 'start' });\n                } else if (!this.virtualScrollerDisabled) {\n                    this.virtualScroller && this.virtualScroller.scrollToIndex(index !== -1 ? index : this.focusedOptionIndex);\n                }\n            });\n        },\n        autoUpdateModel() {\n            if (this.selectOnFocus && this.autoOptionFocus && !this.$filled) {\n                this.focusedOptionIndex = this.findFirstFocusedOptionIndex();\n                this.onOptionSelect(null, this.visibleOptions[this.focusedOptionIndex], false);\n            }\n        },\n        updateModel(event, value) {\n            this.writeValue(value, event);\n            this.$emit('change', { originalEvent: event, value });\n        },\n        flatOptions(options) {\n            return (options || []).reduce((result, option, index) => {\n                result.push({ optionGroup: option, group: true, index });\n\n                const optionGroupChildren = this.getOptionGroupChildren(option);\n\n                optionGroupChildren && optionGroupChildren.forEach((o) => result.push(o));\n\n                return result;\n            }, []);\n        },\n        overlayRef(el) {\n            this.overlay = el;\n        },\n        listRef(el, contentRef) {\n            this.list = el;\n            contentRef && contentRef(el); // For VirtualScroller\n        },\n        virtualScrollerRef(el) {\n            this.virtualScroller = el;\n        }\n    },\n    computed: {\n        visibleOptions() {\n            const options = this.optionGroupLabel ? this.flatOptions(this.options) : this.options || [];\n\n            if (this.filterValue) {\n                const filteredOptions = FilterService.filter(options, this.searchFields, this.filterValue, this.filterMatchMode, this.filterLocale);\n\n                if (this.optionGroupLabel) {\n                    const optionGroups = this.options || [];\n                    const filtered = [];\n\n                    optionGroups.forEach((group) => {\n                        const groupChildren = this.getOptionGroupChildren(group);\n                        const filteredItems = groupChildren.filter((item) => filteredOptions.includes(item));\n\n                        if (filteredItems.length > 0) filtered.push({ ...group, [typeof this.optionGroupChildren === 'string' ? this.optionGroupChildren : 'items']: [...filteredItems] });\n                    });\n\n                    return this.flatOptions(filtered);\n                }\n\n                return filteredOptions;\n            }\n\n            return options;\n        },\n        // @deprecated use $filled instead\n        hasSelectedOption() {\n            return this.$filled;\n        },\n        label() {\n            const selectedOptionIndex = this.findSelectedOptionIndex();\n\n            return selectedOptionIndex !== -1 ? this.getOptionLabel(this.visibleOptions[selectedOptionIndex]) : this.placeholder || 'p-emptylabel';\n        },\n        editableInputValue() {\n            const selectedOptionIndex = this.findSelectedOptionIndex();\n\n            return selectedOptionIndex !== -1 ? this.getOptionLabel(this.visibleOptions[selectedOptionIndex]) : this.d_value || '';\n        },\n        equalityKey() {\n            return this.optionValue ? null : this.dataKey;\n        },\n        searchFields() {\n            return this.filterFields || [this.optionLabel];\n        },\n        filterResultMessageText() {\n            return isNotEmpty(this.visibleOptions) ? this.filterMessageText.replaceAll('{0}', this.visibleOptions.length) : this.emptyFilterMessageText;\n        },\n        filterMessageText() {\n            return this.filterMessage || this.$primevue.config.locale.searchMessage || '';\n        },\n        emptyFilterMessageText() {\n            return this.emptyFilterMessage || this.$primevue.config.locale.emptySearchMessage || this.$primevue.config.locale.emptyFilterMessage || '';\n        },\n        emptyMessageText() {\n            return this.emptyMessage || this.$primevue.config.locale.emptyMessage || '';\n        },\n        selectionMessageText() {\n            return this.selectionMessage || this.$primevue.config.locale.selectionMessage || '';\n        },\n        emptySelectionMessageText() {\n            return this.emptySelectionMessage || this.$primevue.config.locale.emptySelectionMessage || '';\n        },\n        selectedMessageText() {\n            return this.$filled ? this.selectionMessageText.replaceAll('{0}', '1') : this.emptySelectionMessageText;\n        },\n        focusedOptionId() {\n            return this.focusedOptionIndex !== -1 ? `${this.id}_${this.focusedOptionIndex}` : null;\n        },\n        ariaSetSize() {\n            return this.visibleOptions.filter((option) => !this.isOptionGroup(option)).length;\n        },\n        isClearIconVisible() {\n            return this.showClear && this.d_value != null && isNotEmpty(this.options);\n        },\n        virtualScrollerDisabled() {\n            return !this.virtualScrollerOptions;\n        }\n    },\n    directives: {\n        ripple: Ripple\n    },\n    components: {\n        InputText,\n        VirtualScroller,\n        Portal,\n        InputIcon,\n        IconField,\n        TimesIcon,\n        ChevronDownIcon,\n        SpinnerIcon,\n        SearchIcon,\n        CheckIcon,\n        BlankIcon\n    }\n};\n</script>\n", "<script>\nimport Select from 'primevue/select';\n\nexport default {\n    name: 'Dropdown',\n    extends: Select,\n    mounted() {\n        console.warn('Deprecated since v4. Use Select component instead.');\n    }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAAA,WAAe;EACXC,MAAM;EACN,WAASC;AACb;;ACVI,SAAAC,UAAA,GAAAC,mBAEK,OAFLC,WAEK;IAFAC,OAAM;IAAKC,QAAO;IAAKC,SAAQ;IAAYC,MAAK;IAAOC,OAAM;KAAqCC,KAAGC,IAAA,CAAA,GAAAC,OAAA,CAAA,MAAAA,OAAA,CAAA,IAAA,CACtGC,gBAAiE,QAAA;IAA3DR,OAAM;IAAIC,QAAO;IAAIE,MAAK;IAAe,gBAAa;;;;;;ACYpE,IAAAM,WAAe;EACXC,MAAM;EACN,WAASC;AACb;;AChBI,SAAAC,UAAA,GAAAC,mBAOK,OAPLC,WAOK;IAPAC,OAAM;IAAKC,QAAO;IAAKC,SAAQ;IAAYC,MAAK;IAAOC,OAAM;KAAqCC,KAAGC,IAAA,CAAA,GAAAC,OAAA,CAAA,MAAAA,OAAA,CAAA,IAAA,CACtGC,gBAKC,QAAA;IAJG,aAAU;IACV,aAAU;IACVC,GAAE;IACFN,MAAK;;;;;;ACJjB,IAAMO,QAAQ,SAARA,OAAKC,MAAA;AAAA,MAAMC,KAAED,KAAFC;AAAE,SAAAC,sIAAAA,OAQUD,GAAG,WAAW,GAACC,sBAAAA,EAAAA,OAC/BD,GAAG,sBAAsB,GAACC,gGAAAA,EAAAA,OAKbD,GAAG,sBAAsB,GAACC,wEAAAA,EAAAA,OAI5BD,GAAG,sBAAsB,GAACC,yFAAAA,EAAAA,OAIhBD,GAAG,sBAAsB,GAACC,UAAAA,EAAAA,OAAWD,GAAG,WAAW,GAACC,uFAAAA,EAAAA,OAItDD,GAAG,sBAAsB,GAAC,UAAA,EAAAC,OAAWD,GAAG,WAAW,GAAC,6EAAA,EAAAC,OAInED,GAAG,yBAAyB,GAAC,gBAAA,EAAAC,OACjCD,GAAG,yBAAyB,GAAC,iBAAA,EAAAC,OAC5BD,GAAG,yBAAyB,GAAC,gCAAA,EAAAC,OACdD,GAAG,yBAAyB,GAAC,kFAAA,EAAAC,OAIzCD,GAAG,yBAAyB,GAAC,gBAAA,EAAAC,OACjCD,GAAG,yBAAyB,GAAC,iBAAA,EAAAC,OAC5BD,GAAG,yBAAyB,GAACC,gCAAAA,EAAAA,OACdD,GAAG,yBAAyB,GAAC,cAAA;AAAA;AAI1D,IAAME,UAAU;EACZC,MAAM;AACV;AAEA,IAAA,iBAAeC,UAAUC,OAAO;EAC5BC,MAAM;EACNR;EACAI;AACJ,CAAC;;;AClDD,IAAA,WAAe;EACXK,MAAM;EACN,WAASC;EACTC,OAAOC;EACPC,SAAO,SAAPA,UAAU;AACN,WAAO;MACHC,cAAc;MACdC,iBAAiB;;EAEzB;AACJ;ACLA,IAAAC,WAAe;EACXP,MAAM;EACN,WAASQ;EACTC,cAAc;AAClB;;ACZI,SAAAC,UAAA,GAAAC,mBAEK,OAFLC,WAEK;IAFC,SAAOC,KAAEC,GAAA,MAAA;KAAkBD,KAAIE,KAAA,MAAA,CAAA,GAAA,CACjCC,WAAOH,KAAAI,QAAA,SAAA,CAAA,GAAA,EAAA;;;;;ACAf,IAAMC,WAAU;EACZC,MAAM;AACV;AAEA,IAAA,iBAAeC,UAAUC,OAAO;EAC5BC,MAAM;EACNJ,SAAAA;AACJ,CAAC;;;ACLD,IAAAK,YAAe;EACXC,MAAM;EACN,WAASC;EACTC,OAAOC;EACPC,OAAO;IACH,SAAO;;EAEXC,SAAO,SAAPA,WAAU;AACN,WAAO;MACHC,cAAc;MACdC,iBAAiB;;EAEzB;AACJ;ACRA,IAAAC,WAAe;EACXR,MAAM;EACN,WAASS;EACTC,cAAc;EACdC,UAAU;IACNC,gBAAc,SAAdA,iBAAiB;AACb,aAAO,CAAC,KAAKC,GAAG,MAAM,GAAG,KAAI,OAAA,CAAM;IACvC;EACJ;AACJ;;ACjBI,SAAAC,UAAA,GAAAC,mBAEM,QAFNC,WAEM;IAFC,SAAOC,SAAAL;KAAwBM,KAAIC,KAAA,MAAA,CAAA,GAAA,CACtCC,WAAOF,KAAAG,QAAA,SAAA,CAAA,GAAA,EAAA;;;;;ACAf,IAAMC,SAAQ,SAARA,OAAKC,MAAA;AAAA,MAAMC,KAAED,KAAFC;AAAE,SAAA,kDAAAC,OAEDD,GAAG,wCAAwC,GAAC,gBAAA,EAAAC,OACjDD,GAAG,mCAAmC,GAAC,4DAAA,EAAAC,OAInCD,GAAG,kCAAkC,GAACC,gBAAAA,EAAAA,OAC1CD,GAAG,kCAAkC,GAAC,iBAAA,EAAAC,OACrCD,GAAG,kCAAkC,GAAC,QAAA;AAAA;AAIpD,IAAME,MAkDL;AAED,IAAA,uBAAeC,UAAUC,OAAO;EAC5BC,MAAM;EACNH;EACAJ,OAAAA;AACJ,CAAC;;;ACnED,IAAAQ,YAAe;EACXC,MAAM;EACN,WAASC;EACTC,OAAO;IACHC,IAAI;MACAC,MAAMC;MACN,WAAS;;IAEbC,OAAO;IACP,SAAO;IACPC,OAAO;MACHH,MAAMI;MACN,WAAS;;IAEbC,UAAU;MACNL,MAAM,CAACM,QAAQF,KAAK;MACpB,WAAS;;IAEbG,cAAc;IACdC,aAAa;IACbC,aAAa;MACTT,MAAMC;MACN,WAAS;;IAEbS,mBAAmB;MACfV,MAAMM;MACN,WAAS;;IAEbK,OAAO;MACHX,MAAMM;MACN,WAAS;;IAEbM,aAAa;MACTZ,MAAMM;MACN,WAAS;;IAEbO,MAAM;MACFb,MAAMc;MACN,WAAS;;IAEbC,UAAU;MACNf,MAAMc;MACN,WAAS;;IAEbE,gBAAgB;MACZhB,MAAMc;MACN,WAAS;;IAEbG,SAAS;MACLjB,MAAMI;MACN,WAAS;;IAEbc,SAAS;MACLlB,MAAMc;MACN,WAAS;;IAEbK,YAAY;MACRnB,MAAMc;MACN,WAAS;;IAEbM,YAAY;MACRpB,MAAMc;MACN,WAAS;;IAEbO,UAAU;MACNrB,MAAMM;MACN,WAAS;;IAEbgB,QAAQ;MACJtB,MAAMc;MACN,WAAS;;IAEbS,MAAM;MACFvB,MAAMM;MACN,WAAS;;IAEbkB,YAAY;MACRxB,MAAMc;MACN,WAAS;;IAEbW,UAAU;MACNzB,MAAMc;MACN,WAAS;IACb;;EAEJZ,OAAOwB;EACPC,SAAO,SAAPA,WAAU;AACN,WAAO;MACHC,oBAAoB;MACpBC,iBAAiB;;;EAGzBC,aAAW,SAAXA,cAAc;AAAA,QAAAC;AACVL,yBAAqBM,QAAQ;MAAEC,QAAKF,wBAAE,KAAKG,qBAAe,QAAAH,0BAAA,WAAAA,wBAApBA,sBAAsBI,SAAG,QAAAJ,0BAAA,SAAA,SAAzBA,sBAA2BE;IAAM,CAAC;EAC5E;AACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjDA,IAAAG,WAAe;EACXxC,MAAM;EACN,WAASyC;EACTC,cAAc;EACdC,OAAO,CAAC,4BAA4B,UAAU,uBAAuB,WAAW;EAChFC,MAAI,SAAJA,OAAO;AACH,QAAMC,OAAO,KAAKC,OAAM;AAExB,WAAO;MACHC,OAAOF,OAAO;QAAEG,MAAM;QAAGC,MAAM;MAAE,IAAI;MACrCC,MAAML,OAAO;QAAEG,MAAM;QAAGC,MAAM;MAAE,IAAI;MACpCE,MAAMN,OAAO;QAAEG,MAAM;QAAGC,MAAM;MAAE,IAAI;MACpCG,oBAAoBP,OAAO;QAAEG,MAAM;QAAGC,MAAM;MAAE,IAAI;MAClDI,eAAeR,OAAO;QAAES,KAAK;QAAGC,MAAM;MAAE,IAAI;MAC5CC,qBAAqB,KAAK1C;MAC1B2C,WAAW,KAAKnC;MAChBoC,WAAW,CAAA;MACXC,aAAa,CAAA;MACbC,cAAc,CAAA;;;EAGtBC,SAAS;EACTC,SAAS;EACTT,eAAe;EACfU,eAAe;EACfC,eAAe;EACfC,cAAc;EACdC,eAAe;EACfC,qBAAqB;EACrBC,sBAAsB;EACtBC,gBAAgB;EAChBC,eAAe,CAAA;EACfC,gBAAgB;EAChBC,aAAa;EACbC,OAAO;IACH3D,mBAAAA,SAAAA,kBAAkB4D,UAAU;AACxB,WAAKlB,sBAAsBkB;;IAE/BpD,SAAO,SAAPA,QAAQoD,UAAUC,UAAU;AACxB,UAAI,KAAK1D,QAAQyD,aAAaC,YAAYD,aAAa,KAAKjB,WAAW;AACnE,aAAKA,YAAYiB;MACrB;;IAEJnE,OAAK,SAALA,MAAMmE,UAAUC,UAAU;AACtB,UAAI,CAACA,YAAYA,SAASC,YAAYF,YAAY,CAAA,GAAIE,QAAQ;AAC1D,aAAKC,KAAI;AACT,aAAKC,kBAAiB;MAC1B;;IAEJrE,UAAQ,SAARA,WAAW;AACP,WAAKoE,KAAI;AACT,WAAKC,kBAAiB;;IAE1BjE,aAAW,SAAXA,cAAc;AACV,WAAKwC,gBAAgB,KAAKP,OAAM,IAAK;QAAEQ,KAAK;QAAGC,MAAM;MAAE,IAAI;;IAE/D5C,cAAY,SAAZA,eAAe;AACX,WAAKkE,KAAI;AACT,WAAKC,kBAAiB;;IAE1BlE,aAAW,SAAXA,cAAc;AACV,WAAKiE,KAAI;AACT,WAAKC,kBAAiB;IAC1B;;EAEJC,SAAO,SAAPA,UAAU;AACN,SAAKC,SAAQ;AAEb,SAAK3B,gBAAgB,KAAKP,OAAM,IAAK;MAAEQ,KAAK;MAAGC,MAAM;IAAE,IAAI;AAC3D,SAAKe,gBAAgB,KAAKA,iBAAiB,CAAA;;EAE/CW,SAAO,SAAPA,UAAU;AACN,KAAC,KAAKT,eAAe,KAAKQ,SAAQ;;EAEtCE,WAAS,SAATA,YAAY;AACR,SAAKC,qBAAoB;AAEzB,SAAKX,cAAc;;EAEvBY,SAAS;IACLJ,UAAQ,SAARA,WAAW;AACP,UAAIK,UAAU,KAAKxB,OAAO,GAAG;AACzB,aAAKyB,aAAa,KAAKxB,OAAO;AAC9B,aAAKe,KAAI;AACT,aAAKC,kBAAiB;AACtB,aAAKS,mBAAkB;AAEvB,aAAKtB,eAAeuB,SAAS,KAAK3B,OAAO;AACzC,aAAKK,gBAAgBuB,UAAU,KAAK5B,OAAO;AAC3C,aAAKM,sBAAsBqB,SAAS,KAAK1B,OAAO;AAChD,aAAKM,uBAAuBqB,UAAU,KAAK3B,OAAO;AAClD,aAAKU,cAAc;MACvB;;IAEJK,MAAI,SAAJA,OAAO;AACH,UAAI,CAAC,KAAK1D,UAAU;AAChB,aAAKuE,QAAO;AACZ,aAAKC,iBAAgB;AACrB,aAAKC,cAAa;MACtB;;IAEJC,YAAU,SAAVA,aAAa;AACT,aAAO,KAAKhF,gBAAgB;;IAEhCiF,cAAY,SAAZA,eAAe;AACX,aAAO,KAAKjF,gBAAgB;;IAEhCiC,QAAM,SAANA,SAAS;AACL,aAAO,KAAKjC,gBAAgB;;IAEhCkF,UAAAA,SAAAA,SAASC,UAAS;AAEd,WAAKnC,WAAW,KAAKA,QAAQkC,SAASC,QAAO;;IAEjDC,eAAAA,SAAAA,cAAcC,OAA0B;AAAA,UAAAC,QAAA;AAAA,UAAnBC,WAASC,UAAAzB,SAAA,KAAAyB,UAAA,CAAA,MAAAC,SAAAD,UAAA,CAAA,IAAE;AAC5B,UAAMxD,OAAO,KAAKC,OAAM;AACxB,UAAMyD,aAAa,KAAKT,aAAY;AACpC,UAAMU,QAAQ3D,OAAOqD,MAAMO,MAAM,SAACC,GAAC;AAAA,eAAKA,IAAI;MAAE,CAAA,IAAIR,QAAQ;AAE1D,UAAIM,OAAO;AACP,YAAMzD,QAAQ,KAAKA;AACnB,YAAA4D,gBAA0C,KAAK9C,SAAO+C,wBAAAD,cAA9CE,WAAAA,YAAUD,0BAAE,SAAA,IAACA,uBAAAE,wBAAAH,cAAEI,YAAAA,aAAAA,0BAAa,SAAA,IAAAD;AACpC,YAAAE,wBAA8B,KAAKC,kBAAiB,GAA5CnG,qBAAkBkG,sBAAlBlG;AACR,YAAMoG,aAAa,KAAKC,mBAAkB;AAC1C,YAAM1G,YAAW,KAAKA;AACtB,YAAM2G,iBAAiB,SAAjBA,kBAAa;AAAA,cAAKC,SAAKhB,UAAAzB,SAAA,KAAAyB,UAAA,CAAA,MAAAC,SAAAD,UAAA,CAAA,IAAI;AAAC,cAAEiB,QAAKjB,UAAAzB,SAAAyB,IAAAA,UAAA,CAAA,IAAAC;AAAA,iBAAMe,UAAUC,QAAQ,IAAID;;AACrE,YAAME,iBAAiB,SAAjBA,gBAAkBC,QAAQC,OAAOC,OAAK;AAAA,iBAAKF,SAASC,QAAQC;QAAK;AACvE,YAAM3B,YAAW,SAAXA,YAAO;AAAA,cAAKxC,OAAG8C,UAAAzB,SAAA,KAAAyB,UAAA,CAAA,MAAAC,SAAAD,UAAA,CAAA,IAAI;AAAC,cAAE/C,MAAE+C,UAAAzB,SAAA,KAAAyB,UAAA,CAAA,MAAAC,SAAAD,UAAA,CAAA,IAAI;AAAC,iBAAKF,MAAKJ,SAAS;YAAExC;YAAMD;YAAK8C;UAAS,CAAC;QAAC;AAC9E,YAAIuB,WAAW9E,OAAO;UAAEG,MAAM;UAAGC,MAAM;QAAE,IAAI;AAC7C,YAAIoB,iBAAiB,OACjBuD,kBAAkB;AAEtB,YAAI/E,MAAM;AACN8E,qBAAW;YAAE3E,MAAMoE,eAAelB,MAAM,CAAC,GAAGpF,mBAAkB,CAAC,CAAC;YAAGmC,MAAMmE,eAAelB,MAAM,CAAC,GAAGpF,mBAAkB,CAAC,CAAC;;AACtHiF,UAAAA,UAASwB,eAAeI,SAAS1E,MAAMxC,UAAS,CAAC,GAAGyG,WAAW3D,IAAI,GAAGgE,eAAeI,SAAS3E,MAAMvC,UAAS,CAAC,GAAGyG,WAAW5D,GAAG,CAAC;AAChIsE,4BAAkB,KAAKvE,cAAcC,QAAQuD,aAAa,KAAKxD,cAAcE,SAASwD;AACtF1C,2BAAiBsD,SAAS3E,SAASD,MAAMC,QAAQ2E,SAAS1E,SAASF,MAAME;QAC7E,OAAO;AACH0E,qBAAWP,eAAelB,OAAOpF,kBAAiB;AAClDyF,uBAAaR,UAASwB,eAAeI,UAAUlH,WAAUyG,WAAW3D,IAAI,GAAGsD,SAAS,IAAId,UAASgB,YAAYQ,eAAeI,UAAUlH,WAAUyG,WAAW5D,GAAG,CAAC;AAC/JsE,4BAAkB,KAAKvE,mBAAmBkD,aAAaQ,aAAaF;AACpExC,2BAAiBsD,aAAa5E;QAClC;AAEA,aAAKsB,iBAAiBA;AACtBuD,4BAAoB,KAAK7E,QAAQ4E;MACrC;;IAEJE,cAAY,SAAZA,aAAa3B,OAAO4B,IAAuB;AAAA,UAAAC,SAAA;AAAA,UAAnB3B,WAASC,UAAAzB,SAAA,KAAAyB,UAAA,CAAA,MAAAC,SAAAD,UAAA,CAAA,IAAE;AAC/B,UAAIyB,IAAI;AACJ,YAAMjF,OAAO,KAAKC,OAAM;AACxB,YAAMyD,aAAa,KAAKT,aAAY;AACpC,YAAMU,QAAQ3D,OAAOqD,MAAMO,MAAM,SAACC,GAAC;AAAA,iBAAKA,IAAI;QAAE,CAAA,IAAIR,QAAQ;AAE1D,YAAIM,OAAO;AACP,cAAAwB,wBAA4B,KAAKC,iBAAgB,GAAzClF,QAAKiF,sBAALjF,OAAOmF,WAAAA,sBAAAA;AACf,cAAMnC,YAAW,SAAXA,YAAO;AAAA,gBAAKxC,OAAG8C,UAAAzB,SAAA,KAAAyB,UAAA,CAAA,MAAAC,SAAAD,UAAA,CAAA,IAAI;AAAC,gBAAE/C,MAAE+C,UAAAzB,SAAA,KAAAyB,UAAA,CAAA,MAAAC,SAAAD,UAAA,CAAA,IAAI;AAAC,mBAAK0B,OAAKhC,SAAS;cAAExC;cAAMD;cAAK8C;YAAS,CAAC;UAAC;AAC9E,cAAM+B,YAAYL,OAAO;AACzB,cAAMM,UAAUN,OAAO;AAEvB,cAAIK,WAAW;AACX,gBAAItF,MAAM;AACN,kBAAIqF,SAASnF,MAAMC,OAAOD,MAAMC,OAAOkD,MAAM,CAAC,GAAG;AAC7CH,gBAAAA,UAASmC,SAASnF,MAAME,OAAO,KAAKxC,SAAS,CAAC,IAAIyH,SAASnF,MAAMC,OAAO,KAAK,KAAKvC,SAAS,CAAC,CAAC;cACjG,WAAWyH,SAASnF,MAAME,OAAOF,MAAME,OAAOiD,MAAM,CAAC,GAAG;AACpDH,gBAAAA,WAAUmC,SAASnF,MAAME,OAAO,KAAK,KAAKxC,SAAS,CAAC,GAAGyH,SAASnF,MAAMC,OAAO,KAAKvC,SAAS,CAAC,CAAC;cACjG;YACJ,OAAO;AACH,kBAAIyH,SAASnF,QAAQA,QAAQmD,OAAO;AAChC,oBAAMmC,OAAOH,SAASnF,QAAQ,KAAK,KAAKtC;AAExC8F,6BAAaR,UAASsC,KAAK,CAAC,IAAItC,UAAS,GAAGsC,GAAG;cACnD;YACJ;qBACOD,SAAS;AAChB,gBAAIvF,MAAM;AACN,kBAAIqF,SAAShF,KAAKF,OAAOD,MAAMC,QAAQkD,MAAM,CAAC,IAAI,GAAG;AACjDH,gBAAAA,UAASmC,SAASnF,MAAME,OAAO,KAAKxC,SAAS,CAAC,IAAIyH,SAASnF,MAAMC,OAAO,KAAK,KAAKvC,SAAS,CAAC,CAAC;cACjG,WAAWyH,SAAShF,KAAKD,OAAOF,MAAME,QAAQiD,MAAM,CAAC,IAAI,GAAG;AACxDH,gBAAAA,WAAUmC,SAASnF,MAAME,OAAO,KAAK,KAAKxC,SAAS,CAAC,GAAGyH,SAASnF,MAAMC,OAAO,KAAKvC,SAAS,CAAC,CAAC;cACjG;YACJ,OAAO;AACH,kBAAIyH,SAAShF,OAAOH,SAASmD,QAAQ,GAAG;AACpC,oBAAMmC,SAAOH,SAASnF,QAAQ,KAAK,KAAKtC;AAExC8F,6BAAaR,UAASsC,OAAK,CAAC,IAAItC,UAAS,GAAGsC,KAAG;cACnD;YACJ;UACJ;QACJ;MACJ,OAAO;AACH,aAAKpC,cAAcC,OAAOE,QAAQ;MACtC;;IAEJ6B,kBAAgB,SAAhBA,mBAAmB;AACf,UAAMK,2BAA2B,SAA3BA,0BAA4BC,MAAMd,OAAK;AAAA,eAAKe,KAAKC,MAAMF,QAAQd,SAASc,KAAK;MAAC;AAEpF,UAAIG,kBAAkB,KAAK3F;AAC3B,UAAI4F,iBAAiB;AAErB,UAAI,KAAK9E,SAAS;AACd,YAAMhB,OAAO,KAAKC,OAAM;AACxB,YAAMyD,aAAa,KAAKT,aAAY;AACpC,YAAA8C,iBAAkC,KAAK/E,SAA/BgD,YAAS+B,eAAT/B,WAAWE,aAAW6B,eAAX7B;AAEnB,YAAIlE,MAAM;AACN6F,4BAAkB;YAAE1F,MAAMsF,yBAAyBzB,WAAW,KAAKpG,SAAS,CAAC,CAAC;YAAGwC,MAAMqF,yBAAyBvB,YAAY,KAAKtG,SAAS,CAAC,CAAC;;AAC5IkI,2BAAiB;YAAE3F,MAAM0F,gBAAgB1F,OAAO,KAAKI,mBAAmBJ;YAAMC,MAAMyF,gBAAgBzF,OAAO,KAAKG,mBAAmBH;;QACvI,OAAO;AACH,cAAM4F,YAAYtC,aAAaQ,aAAaF;AAE5C6B,4BAAkBJ,yBAAyBO,WAAW,KAAKpI,QAAQ;AACnEkI,2BAAiBD,kBAAkB,KAAKtF;QAC5C;MACJ;AAEA,aAAO;QACHL,OAAO,KAAKA;QACZG,MAAM,KAAKA;QACXgF,UAAU;UACNnF,OAAO2F;UACPxF,MAAMyF;QACV;;;IAGR1B,mBAAiB,SAAjBA,oBAAoB;AAChB,UAAMpE,OAAO,KAAKC,OAAM;AACxB,UAAMyD,aAAa,KAAKT,aAAY;AACpC,UAAMrF,YAAW,KAAKA;AACtB,UAAMyG,aAAa,KAAKC,mBAAkB;AAC1C,UAAM2B,eAAe,KAAKjF,UAAU,KAAKA,QAAQkF,cAAc7B,WAAW3D,OAAO;AACjF,UAAMyF,gBAAgB,KAAKnF,UAAU,KAAKA,QAAQoF,eAAe/B,WAAW5D,MAAM;AAClF,UAAM4F,8BAA8B,SAA9BA,6BAA+BC,cAAcC,WAAS;AAAA,eAAKZ,KAAKa,KAAKF,gBAAgBC,aAAaD,aAAa;MAAC;AACtH,UAAMG,6BAA6B,SAA7BA,4BAA8BC,WAAS;AAAA,eAAKf,KAAKa,KAAKE,YAAY,CAAC;MAAC;AAC1E,UAAMnG,qBAAqBP,OACrB;QAAEG,MAAMkG,4BAA4BF,eAAevI,UAAS,CAAC,CAAC;QAAGwC,MAAMiG,4BAA4BJ,cAAcrI,UAAS,CAAC,CAAC;UAC5HyI,4BAA4B3C,aAAauC,eAAeE,eAAevI,SAAQ;AAErF,UAAMK,qBAAoB,KAAK0C,wBAAwBX,OAAO,CAACyG,2BAA2BlG,mBAAmBJ,IAAI,GAAGsG,2BAA2BlG,mBAAmBH,IAAI,CAAC,IAAIqG,2BAA2BlG,kBAAkB;AAExN,aAAO;QAAEA;QAAoBtC,mBAAAA;;;IAEjC6E,kBAAgB,SAAhBA,mBAAmB;AAAA,UAAA6D,SAAA;AACf,UAAM3G,OAAO,KAAKC,OAAM;AACxB,UAAMC,QAAQ,KAAKA;AACnB,UAAA0G,yBAAkD,KAAKxC,kBAAiB,GAAhE7D,qBAAkBqG,uBAAlBrG,oBAAoBtC,qBAAgB2I,uBAAhB3I;AAC5B,UAAM4I,gBAAgB,SAAhBA,eAAiBlC,QAAQmC,MAAMrC,OAAK;AAAA,YAAEsC,UAAQvD,UAAAzB,SAAA,KAAAyB,UAAA,CAAA,MAAAC,SAAAD,UAAA,CAAA,IAAE;AAAK,eAAKmD,OAAKK,QAAQrC,SAASmC,QAAQnC,SAASF,QAAQ,IAAI,KAAKA,OAAOsC,OAAO;MAAC;AACvI,UAAM1G,OAAOL,OACP;QAAEG,MAAM0G,cAAc3G,MAAMC,MAAMI,mBAAmBJ,MAAMlC,mBAAkB,CAAC,CAAC;QAAGmC,MAAMyG,cAAc3G,MAAME,MAAMG,mBAAmBH,MAAMnC,mBAAkB,CAAC,GAAG,IAAI;UACrK4I,cAAc3G,OAAOK,oBAAoBtC,kBAAiB;AAEhE,WAAKoC,OAAOA;AACZ,WAAKE,qBAAqBA;AAC1B,WAAKI,sBAAsB1C;AAC3B,WAAKgJ,MAAM,4BAA4B,KAAKtG,mBAAmB;AAE/D,UAAI,KAAKhC,YAAY;AACjB,aAAKkC,YAAYb,OAAOrC,MAAMuJ,KAAK;UAAEnF,QAAQxB,mBAAmBJ;SAAM,EAAEgH,IAAI,WAAA;AAAA,iBAAMxJ,MAAMuJ,KAAK;YAAEnF,QAAQxB,mBAAmBH;UAAK,CAAC;QAAC,CAAA,IAAIzC,MAAMuJ,KAAK;UAAEnF,QAAQxB;QAAmB,CAAC;MAClL;AAEA,UAAI,KAAKnC,MAAM;AACXgJ,gBAAQC,QAAO,EAAGC,KAAK,WAAM;AAAA,cAAAC;AACzBZ,iBAAKlF,gBAAgB;YACjBvB,OAAOyG,OAAK7H,OAAQkB,OAAO;cAAEG,MAAM;cAAGC,MAAMF,MAAME;gBAAS,IAAKF;YAChEG,MAAMsF,KAAK6B,IAAIb,OAAK7H,OAAO6H,OAAK7H,OAAOuB,QAAMkH,eAAAZ,OAAKjJ,WAAK,QAAA6J,iBAAA,SAAA,SAAVA,aAAYxF,WAAU,CAAC;;AAGxE4E,iBAAKM,MAAM,aAAaN,OAAKlF,aAAa;QAC9C,CAAC;MACL;;IAEJQ,mBAAiB,SAAjBA,oBAAoB;AAAA,UAAAwF,SAAA;AAChB,UAAI,KAAKzI,YAAY,CAAC,KAAK4B,WAAW;AAClCwG,gBAAQC,QAAO,EAAGC,KAAK,WAAM;AACzB,cAAIG,OAAKxG,SAAS;AACd,gBAAMjB,OAAOyH,OAAKxH,OAAM;AACxB,gBAAMyD,aAAa+D,OAAKxE,aAAY;AACpC,gBAAMyE,WAAWD,OAAKzE,WAAU;AAEhCyE,mBAAKxG,QAAQxD,MAAMkK,YAAYF,OAAKxG,QAAQxD,MAAMmK,WAAW;AAC7DH,mBAAKxG,QAAQxD,MAAMoK,WAAW;AAC9BJ,mBAAKzG,QAAQvD,MAAMqK,UAAU;AAO7B,gBAAAC,OAAwB,CAACpF,SAAS8E,OAAKzG,OAAO,GAAG4B,UAAU6E,OAAKzG,OAAO,CAAC,GAAjEgH,QAAKD,KAAA,CAAA,GAAEE,SAAMF,KAAA,CAAA;AAEpB,aAAC/H,QAAQ0D,gBAAgB+D,OAAKzG,QAAQvD,MAAMuK,QAAQA,QAAQP,OAAKrG,eAAe4G,QAAQ,OAAOP,OAAK1J,eAAe0J,OAAKrG,eAAe;AACvI,aAACpB,QAAQ0H,cAAcD,OAAKzG,QAAQvD,MAAMwK,SAASA,SAASR,OAAKpG,gBAAgB4G,SAAS,OAAOR,OAAK3J,gBAAgB2J,OAAKpG,gBAAgB;AAE3IoG,mBAAKxG,QAAQxD,MAAMkK,YAAYF,OAAKxG,QAAQxD,MAAMmK,WAAW;AAC7DH,mBAAKxG,QAAQxD,MAAMoK,WAAW;AAC9BJ,mBAAKzG,QAAQvD,MAAMqK,UAAU;UACjC;QACJ,CAAC;MACL;;IAEJd,SAAO,SAAPA,UAA0B;AAAA,UAAAkB,OAAAC;AAAA,UAAlB9H,OAAAA,UAAAA,SAAAA,KAAAA,UAAAA,CAAAA,MAAAA,SAAAA,UAAAA,CAAAA,IAAO;AAAC,UAAE+H,SAAM5E,UAAAzB,SAAAyB,IAAAA,UAAA,CAAA,IAAAC;AACpB,aAAO,KAAK/F,QAAQiI,KAAK6B,IAAIY,WAASF,QAAC,KAAK1J,WAAW,KAAKd,MAAM,CAAC,OAAC,QAAAwK,UAA9BA,SAAAA,SAAAA,MAAiCnG,WAAU,MAAIoG,cAAA,KAAKzK,WAAK,QAAAyK,gBAAA,SAAA,SAAVA,YAAYpG,WAAU,GAAG1B,IAAI,IAAI;;IAE1HiE,oBAAkB,SAAlBA,qBAAqB;AACjB,UAAI,KAAKrD,SAAS;AACd,YAAMxD,QAAQ4K,iBAAiB,KAAKpH,OAAO;AAC3C,YAAMP,OAAO4H,WAAW7K,MAAM8K,WAAW,IAAI5C,KAAK6C,IAAIF,WAAW7K,MAAMiD,IAAI,KAAK,GAAG,CAAC;AACpF,YAAM+H,QAAQH,WAAW7K,MAAMiL,YAAY,IAAI/C,KAAK6C,IAAIF,WAAW7K,MAAMgL,KAAK,KAAK,GAAG,CAAC;AACvF,YAAMhI,MAAM6H,WAAW7K,MAAMkL,UAAU,IAAIhD,KAAK6C,IAAIF,WAAW7K,MAAMgD,GAAG,KAAK,GAAG,CAAC;AACjF,YAAMmI,SAASN,WAAW7K,MAAMoL,aAAa,IAAIlD,KAAK6C,IAAIF,WAAW7K,MAAMmL,MAAM,KAAK,GAAG,CAAC;AAE1F,eAAO;UAAElI;UAAM+H;UAAOhI;UAAKmI;UAAQE,GAAGpI,OAAO+H;UAAOM,GAAGtI,MAAMmI;;MACjE;AAEA,aAAO;QAAElI,MAAM;QAAG+H,OAAO;QAAGhI,KAAK;QAAGmI,QAAQ;QAAGE,GAAG;QAAGC,GAAG;;;IAE5DlG,SAAO,SAAPA,UAAU;AAAA,UAAAmG,SAAA;AACN,UAAI,KAAKhI,SAAS;AACd,YAAMhB,OAAO,KAAKC,OAAM;AACxB,YAAMyD,aAAa,KAAKT,aAAY;AACpC,YAAMgG,gBAAgB,KAAKjI,QAAQiI;AACnC,YAAMjB,QAAQ,KAAKjK,eAAAA,GAAAA,OAAkB,KAAKiD,QAAQkF,eAAe+C,cAAc/C,aAAe,IAAA;AAC9F,YAAM+B,SAAS,KAAKnK,gBAAa,GAAAoL,OAAM,KAAKlI,QAAQoF,gBAAgB6C,cAAc7C,cAAgB,IAAA;AAClG,YAAM+C,UAAU,SAAVA,SAAWC,OAAOC,QAAM;AAAA,iBAAML,OAAKhI,QAAQvD,MAAM2L,KAAK,IAAIC;;AAEhE,YAAIrJ,QAAQ0D,YAAY;AACpByF,kBAAQ,UAAUlB,MAAM;AACxBkB,kBAAQ,SAASnB,KAAK;QAC1B,OAAO;AACHmB,kBAAQ,UAAUlB,MAAM;QAC5B;MACJ;;IAEJlF,eAAa,SAAbA,gBAAgB;AAAA,UAAAuG,SAAA;AACZ,UAAM5L,SAAQ,KAAKA;AAEnB,UAAIA,QAAO;AACP,YAAMsC,OAAO,KAAKC,OAAM;AACxB,YAAMyD,aAAa,KAAKT,aAAY;AACpC,YAAMoB,aAAa,KAAKC,mBAAkB;AAC1C,YAAM6E,UAAU,SAAVA,SAAWC,OAAOC,QAAQzE,OAAK;AAAA,cAAEC,QAAIrB,UAAAzB,SAAA,KAAAyB,UAAA,CAAA,MAAAC,SAAAD,UAAA,CAAA,IAAI;AAAC,iBAAM8F,OAAKxI,cAAUyI,cAAAA,cAAA,CAAA,GAASD,OAAKxI,WAAW,GAAA0I,gBAAAN,CAAAA,GAAAA,GAAAA,OAAWE,KAAK,IAAMC,UAAU,CAAA,GAAItH,SAAS6C,QAAQC,QAAQ,IAAK,CAAG;;AAEnK,YAAI7E,MAAM;AACNmJ,kBAAQ,UAAUzL,QAAO,KAAKE,SAAS,CAAC,GAAGyG,WAAW0E,CAAC;AACvDI,kBAAQ,SAAS,KAAK3K,WAAWd,OAAM,CAAC,GAAG,KAAKE,SAAS,CAAC,GAAGyG,WAAWyE,CAAC;QAC7E,OAAO;AACHpF,uBAAayF,QAAQ,SAAS,KAAK3K,WAAWd,QAAO,KAAKE,UAAUyG,WAAWyE,CAAC,IAAIK,QAAQ,UAAUzL,QAAO,KAAKE,UAAUyG,WAAW0E,CAAC;QAC5I;MACJ;;IAEJU,oBAAAA,SAAAA,mBAAmBjE,KAAK;AAAA,UAAAkE,SAAA;AACpB,UAAI,KAAKzI,WAAW,CAAC,KAAKlC,YAAY;AAClC,YAAMiB,OAAO,KAAKC,OAAM;AACxB,YAAMyD,aAAa,KAAKT,aAAY;AACpC,YAAM/C,QAAQsF,MAAMA,IAAItF,QAAQ,KAAKA;AACrC,YAAMyJ,wBAAwB,SAAxBA,uBAAyBhF,QAAQC,OAAK;AAAA,iBAAKD,SAASC;QAAK;AAC/D,YAAMgF,eAAe,SAAfA,gBAAW;AAAA,cAAKC,KAACrG,UAAAzB,SAAA,KAAAyB,UAAA,CAAA,MAAAC,SAAAD,UAAA,CAAA,IAAI;AAAC,cAAEsG,KAAGtG,UAAAzB,SAAA,KAAAyB,UAAA,CAAA,MAAAC,SAAAD,UAAA,CAAA,IAAE;AAAC,iBAAMkG,OAAK3I,eAAawI,cAAAA,cAAOG,CAAAA,GAAAA,OAAK3I,YAAY,GAAK;YAAEgJ,WAAS,eAAAb,OAAiBW,IAAEX,MAAAA,EAAAA,OAAOY,IAAE,QAAA;UAAS,CAAE;;AAE3I,YAAI9J,MAAM;AACN4J,uBAAaD,sBAAsBzJ,MAAME,MAAM,KAAKxC,SAAS,CAAC,CAAC,GAAG+L,sBAAsBzJ,MAAMC,MAAM,KAAKvC,SAAS,CAAC,CAAC,CAAC;QACzH,OAAO;AACH,cAAMoM,eAAeL,sBAAsBzJ,OAAO,KAAKtC,QAAQ;AAE/D8F,uBAAakG,aAAaI,cAAc,CAAC,IAAIJ,aAAa,GAAGI,YAAY;QAC7E;MACJ;;IAEJC,wBAAAA,SAAAA,uBAAuBC,OAAO;AAAA,UAAAC,SAAA;AAC1B,UAAMC,SAASF,MAAME;AACrB,UAAMpK,OAAO,KAAKC,OAAM;AACxB,UAAMyD,aAAa,KAAKT,aAAY;AACpC,UAAMoB,aAAa,KAAKC,mBAAkB;AAC1C,UAAM+F,qBAAqB,SAArBA,oBAAsB3E,MAAMb,OAAK;AAAA,eAAMa,OAAQA,OAAOb,QAAQa,OAAOb,QAAQa,OAAQ;;AAC3F,UAAM4E,wBAAwB,SAAxBA,uBAAyB5E,MAAMd,OAAK;AAAA,eAAKe,KAAKC,MAAMF,QAAQd,SAASc,KAAK;MAAC;AAEjF,UAAM6E,wBAAwB,SAAxBA,uBAAyBC,eAAe7F,QAAQ8F,OAAO3D,MAAMrC,OAAOiG,sBAAyB;AAC/F,eAAOF,iBAAiB/F,QAAQA,QAAQiG,uBAAuBD,QAAQ3D,OAAOrC,QAAQE,SAASF,QAAQ;;AAG3G,UAAMF,iBAAiB,SAAjBA,gBAAkBiG,eAAeG,eAAehG,QAAQ8F,OAAO3D,MAAMrC,OAAOiG,sBAAyB;AACvG,YAAIF,iBAAiB/F,MAAO,QAAO;YAC9B,QAAOkB,KAAK6C,IAAI,GAAGkC,uBAAwBF,gBAAgBG,gBAAgBhG,SAAS6F,gBAAgB/F,QAAS+F,gBAAgBG,gBAAgBhG,SAAS6F,gBAAgB,IAAI/F,KAAK;;AAGxL,UAAMoC,gBAAgB,SAAhBA,eAAiB2D,eAAe7F,QAAQ8F,OAAO3D,MAAMrC,OAAOsC,SAAY;AAC1E,YAAI6D,YAAYjG,SAASmC,OAAO,IAAIrC;AAEpC,YAAI+F,iBAAiB/F,OAAO;AACxBmG,uBAAanG,QAAQ;QACzB;AAEA,eAAO0F,OAAKnD,QAAQ4D,WAAW7D,OAAO;;AAG1C,UAAM/C,YAAYqG,mBAAmBD,OAAOpG,WAAWK,WAAW5D,GAAG;AACrE,UAAMyD,aAAamG,mBAAmBD,OAAOlG,YAAYG,WAAW3D,IAAI;AAExE,UAAIoE,WAAW9E,OAAO;QAAEG,MAAM;QAAGC,MAAM;MAAE,IAAI;AAC7C,UAAIyK,UAAU,KAAKxK;AACnB,UAAImB,iBAAiB;AACrB,UAAIsJ,eAAe,KAAKtK;AAExB,UAAIR,MAAM;AACN,YAAM+K,eAAe,KAAKvK,cAAcC,OAAOuD;AAC/C,YAAMgH,gBAAgB,KAAKxK,cAAcE,QAAQwD;AAEjD,YAAI,CAAC,KAAKnF,cAAe,KAAKA,eAAegM,gBAAgBC,gBAAiB;AAC1E,cAAMC,eAAe;YAAE9K,MAAMmK,sBAAsBtG,WAAW,KAAKpG,SAAS,CAAC,CAAC;YAAGwC,MAAMkK,sBAAsBpG,YAAY,KAAKtG,SAAS,CAAC,CAAC;;AACzI,cAAMsN,eAAe;YACjB/K,MAAMoK,sBAAsBU,aAAa9K,MAAM,KAAKD,MAAMC,MAAM,KAAKE,KAAKF,MAAM,KAAKI,mBAAmBJ,MAAM,KAAKQ,oBAAoB,CAAC,GAAGoK,YAAY;YACvJ3K,MAAMmK,sBAAsBU,aAAa7K,MAAM,KAAKF,MAAME,MAAM,KAAKC,KAAKD,MAAM,KAAKG,mBAAmBH,MAAM,KAAKO,oBAAoB,CAAC,GAAGqK,aAAa;;AAG5JlG,qBAAW;YACP3E,MAAMoE,eAAe0G,aAAa9K,MAAM+K,aAAa/K,MAAM,KAAKD,MAAMC,MAAM,KAAKE,KAAKF,MAAM,KAAKI,mBAAmBJ,MAAM,KAAKQ,oBAAoB,CAAC,GAAGoK,YAAY;YACnK3K,MAAMmE,eAAe0G,aAAa7K,MAAM8K,aAAa9K,MAAM,KAAKF,MAAME,MAAM,KAAKC,KAAKD,MAAM,KAAKG,mBAAmBH,MAAM,KAAKO,oBAAoB,CAAC,GAAGqK,aAAa;;AAExKH,oBAAU;YACN1K,MAAM0G,cAAcoE,aAAa9K,MAAM2E,SAAS3E,MAAM,KAAKE,KAAKF,MAAM,KAAKI,mBAAmBJ,MAAM,KAAKQ,oBAAoB,CAAC,CAAC;YAC/HP,MAAMyG,cAAcoE,aAAa7K,MAAM0E,SAAS1E,MAAM,KAAKC,KAAKD,MAAM,KAAKG,mBAAmBH,MAAM,KAAKO,oBAAoB,CAAC,GAAG,IAAI;;AAGzIa,2BAAiBsD,SAAS3E,SAAS,KAAKD,MAAMC,QAAQ0K,QAAQ1K,SAAS,KAAKE,KAAKF,QAAQ2E,SAAS1E,SAAS,KAAKF,MAAME,QAAQyK,QAAQzK,SAAS,KAAKC,KAAKD,QAAQ,KAAKoB;AACtKsJ,yBAAe;YAAErK,KAAKuD;YAAWtD,MAAMwD;;QAC3C;MACJ,OAAO;AACH,YAAM8B,YAAYtC,aAAaQ,aAAaF;AAC5C,YAAMmH,sBAAsB,KAAK3K,iBAAiBwF;AAElD,YAAI,CAAC,KAAKjH,cAAe,KAAKA,cAAcoM,qBAAsB;AAC9D,cAAMF,iBAAeX,sBAAsBtE,WAAW,KAAKpI,QAAQ;AACnE,cAAMsN,iBAAeX,sBAAsBU,gBAAc,KAAK/K,OAAO,KAAKG,MAAM,KAAKE,oBAAoB,KAAKI,qBAAqBwK,mBAAmB;AAEtJrG,qBAAWP,eAAe0G,gBAAcC,gBAAc,KAAKhL,OAAO,KAAKG,MAAM,KAAKE,oBAAoB,KAAKI,qBAAqBwK,mBAAmB;AACnJN,oBAAUhE,cAAcoE,gBAAcnG,UAAU,KAAKzE,MAAM,KAAKE,oBAAoB,KAAKI,mBAAmB;AAC5Ga,2BAAiBsD,aAAa,KAAK5E,SAAS2K,YAAY,KAAKxK,QAAQ,KAAKmB;AAC1EsJ,yBAAe9E;QACnB;MACJ;AAEA,aAAO;QACH9F,OAAO4E;QACPzE,MAAMwK;QACNrJ;QACAwE,WAAW8E;;;IAGnBM,gBAAAA,SAAAA,eAAelB,OAAO;AAClB,UAAAmB,wBAAmD,KAAKpB,uBAAuBC,KAAK,GAA5EhK,QAAKmL,sBAALnL,OAAOG,OAAIgL,sBAAJhL,MAAMmB,iBAAc6J,sBAAd7J,gBAAgBwE,YAAAA,sBAAAA;AAErC,UAAIxE,gBAAgB;AAChB,YAAM8J,WAAW;UAAEpL;UAAOG;;AAE1B,aAAKoJ,mBAAmB6B,QAAQ;AAEhC,aAAKpL,QAAQA;AACb,aAAKG,OAAOA;AACZ,aAAKG,gBAAgBwF;AAErB,aAAKiB,MAAM,uBAAuBqE,QAAQ;AAE1C,YAAI,KAAKlN,QAAQ,KAAKmN,cAAcrL,KAAK,GAAG;AAAA,cAAAsL,cAAAC;AACxC,cAAMhK,gBAAgB;YAClBvB,OAAO,KAAKpB,OAAO6G,KAAK6B,IAAI,KAAKkE,eAAexL,KAAK,IAAI,KAAKpB,SAAO0M,eAAI,KAAC9N,WAAK8N,QAAAA,iBAAA,SAAA,SAAVA,aAAYzJ,WAAU,KAAK,KAAKjD,IAAI,IAAIoB;YAC7GG,MAAMsF,KAAK6B,IAAI,KAAK1I,QAAQ,KAAK4M,eAAexL,KAAK,IAAI,KAAK,KAAKpB,OAAOuB,QAAMoL,eAAA,KAAK/N,WAAK,QAAA+N,iBAAA,SAAA,SAAVA,aAAY1J,WAAU,CAAC;;AAE3G,cAAM4J,qBAAqB,KAAKlK,cAAcvB,UAAUuB,cAAcvB,SAAS,KAAKuB,cAAcpB,SAASoB,cAAcpB;AAEzHsL,gCAAsB,KAAK1E,MAAM,aAAaxF,aAAa;AAC3D,eAAKA,gBAAgBA;QACzB;MACJ;;IAEJmK,UAAAA,SAAAA,SAAS1B,OAAO;AAAA,UAAA2B,SAAA;AACZ,WAAK5E,MAAM,UAAUiD,KAAK;AAE1B,UAAI,KAAKhM,OAAO;AACZ,YAAI,KAAKgD,eAAe;AACpB4K,uBAAa,KAAK5K,aAAa;QACnC;AAEA,YAAI,KAAKqK,cAAa,GAAI;AACtB,cAAI,CAAC,KAAK3K,aAAa,KAAKjC,YAAY;AACpC,gBAAAoN,yBAA2B,KAAK9B,uBAAuBC,KAAK,GAApD1I,iBAAauK,uBAAbvK;AACR,gBAAMwK,UAAUxK,mBAAmB,KAAK1C,OAAO,KAAKyM,cAAa,IAAK;AAEtES,wBAAY,KAAKpL,YAAY;UACjC;AAEA,eAAKM,gBAAgB+K,WAAW,WAAM;AAClCJ,mBAAKT,eAAelB,KAAK;AAEzB,gBAAI2B,OAAKjL,aAAaiL,OAAKlN,eAAe,CAACkN,OAAKzN,QAAQyN,OAAKpN,YAAYgF,SAAY;AACjFoI,qBAAKjL,YAAY;AACjBiL,qBAAKvL,OAAOuL,OAAKH,eAAc;YACnC;UACJ,GAAG,KAAKxN,KAAK;QACjB;MACJ,OAAO;AACH,aAAKkN,eAAelB,KAAK;MAC7B;;IAEJgC,UAAQ,SAARA,WAAW;AAAA,UAAAC,UAAA;AACP,UAAI,KAAKhL,eAAe;AACpB2K,qBAAa,KAAK3K,aAAa;MACnC;AAEA,WAAKA,gBAAgB8K,WAAW,WAAM;AAClC,YAAIzJ,UAAU2J,QAAKnL,OAAO,GAAG;AACzB,cAAMhB,OAAOmM,QAAKlM,OAAM;AACxB,cAAMyH,WAAWyE,QAAKnJ,WAAU;AAChC,cAAMU,aAAayI,QAAKlJ,aAAY;AACpC,cAAAmJ,QAAwB,CAACzJ,SAASwJ,QAAKnL,OAAO,GAAG4B,UAAUuJ,QAAKnL,OAAO,CAAC,GAAjEgH,QAAKoE,MAAA,CAAA,GAAEnE,SAAMmE,MAAA,CAAA;AACpB,cAAOC,cAA8BrE,UAAUmE,QAAK/K,cAAhCkL,eAA8CrE,WAAWkE,QAAK9K;AAClF,cAAMkL,SAASvM,OAAOqM,eAAeC,eAAe5I,aAAa2I,cAAc3E,WAAW4E,eAAe;AAEzG,cAAIC,QAAQ;AACRJ,oBAAKxL,sBAAsBwL,QAAKlO;AAChCkO,oBAAK/K,eAAe4G;AACpBmE,oBAAK9K,gBAAgB4G;AACrBkE,oBAAK7K,sBAAsBqB,SAASwJ,QAAKlL,OAAO;AAChDkL,oBAAK5K,uBAAuBqB,UAAUuJ,QAAKlL,OAAO;AAElDkL,oBAAKnK,KAAI;UACb;QACJ;MACJ,GAAG,KAAK7D,WAAW;;IAEvBuE,oBAAkB,SAAlBA,qBAAqB;AACjB,UAAI,CAAC,KAAKhB,gBAAgB;AACtB,aAAKA,iBAAiB,KAAKwK,SAASM,KAAK,IAAI;AAE7CC,eAAOC,iBAAiB,UAAU,KAAKhL,cAAc;AACrD+K,eAAOC,iBAAiB,qBAAqB,KAAKhL,cAAc;MACpE;;IAEJY,sBAAoB,SAApBA,uBAAuB;AACnB,UAAI,KAAKZ,gBAAgB;AACrB+K,eAAOE,oBAAoB,UAAU,KAAKjL,cAAc;AACxD+K,eAAOE,oBAAoB,qBAAqB,KAAKjL,cAAc;AACnE,aAAKA,iBAAiB;MAC1B;;IAEJkL,YAAAA,SAAAA,WAAWC,eAAe;AACtB,UAAMC,SAAS,KAAKpP,SAAS,CAAA,GAAIqE;AACjC,UAAMsB,QAAQ,KAAKpD,OAAM,IAAK,KAAKC,MAAMC,OAAO0M,gBAAgB,KAAK3M,QAAQ2M;AAE7E,aAAO;QACHxJ;QACAyJ;QACA5M,OAAOmD,UAAU;QACjBhD,MAAMgD,UAAUyJ,QAAQ;QACxBC,MAAM1J,QAAQ,MAAM;QACpB2J,KAAK3J,QAAQ,MAAM;;;IAG3B4J,kBAAgB,SAAhBA,iBAAiB5J,OAAO6J,YAAY;AAChC,UAAIJ,QAAQ,KAAKjM,UAAUkB;AAE3B,aAAAwH,cAAA;QACIlG;QACAyJ;QACA5M,OAAOmD,UAAU;QACjBhD,MAAMgD,UAAUyJ,QAAQ;QACxBC,MAAM1J,QAAQ,MAAM;QACpB2J,KAAK3J,QAAQ,MAAM;MAAC,GACjB6J,UAAS;;IAGpBxB,gBAAAA,SAAAA,eAAexL,OAAO;AAClB,aAAOyF,KAAKC,QAAQ1F,UAAI,QAAJA,UAAI,SAAJA,QAAS,KAAKA,SAAS,KAAKS,sBAAsB,MAAM,KAAK7B,QAAQ,EAAE;;IAE/FyM,eAAAA,SAAAA,cAAcrL,OAAO;AACjB,aAAO,KAAKpB,QAAQ,CAAC,KAAKV,OAAO,KAAKkC,SAAS,KAAKoL,eAAexL,UAAAA,QAAAA,UAAAA,SAAAA,QAAS,KAAKA,KAAK,IAAI;;IAE9FuC,cAAAA,SAAAA,aAAa0K,IAAI;AACb,WAAKlM,UAAUkM,MAAM,KAAKlM,WAAWmM,WAAW,KAAKpM,SAAS,6BAA6B;;IAE/FqM,YAAAA,SAAAA,WAAWF,IAAI;AACX,WAAKnM,UAAUmM;;IAEnBG,YAAAA,SAAAA,WAAWH,IAAI;AACX,WAAKlM,UAAUkM;IACnB;;EAEJI,UAAU;IACNC,gBAAc,SAAdA,kBAAiB;AACb,aAAO,CACH,qBACA,KAAI,OAAA,GACJ;QACI,4BAA4B,KAAK3O;QACjC,wCAAwC,KAAKoB,OAAM;QACnD,oDAAoD,KAAKgD,aAAY;MACzE,CAAA;;IAGRwK,cAAY,SAAZA,eAAe;AACX,aAAO,CACH,6BACA;QACI,6BAA6B,KAAK7M;MACtC,CAAA;;IAGR8M,aAAW,SAAXA,cAAc;AACV,aAAO,CACH,4BACA;QACI,iCAAiC,CAAC,KAAKC,OAAOC;MAClD,CAAA;;IAGRC,aAAW,SAAXA,cAAc;AAAA,UAAAC,UAAA;AACV,UAAI,KAAKpQ,SAAS,CAAC,KAAKkD,WAAW;AAC/B,YAAI,KAAKX,OAAM,EAAI,QAAO,KAAKvC,MAAMqQ,MAAM,KAAKhP,aAAa,IAAI,KAAKmB,MAAMC,MAAM,KAAKE,KAAKF,IAAI,EAAEgH,IAAI,SAAC6G,MAAI;AAAA,iBAAMF,QAAKtP,UAAUwP,OAAOA,KAAKD,MAAMD,QAAK/O,aAAa,IAAI+O,QAAK5N,MAAME,MAAM0N,QAAKzN,KAAKD,IAAI;QAAC,CAAC;iBAChM,KAAK6C,aAAY,KAAM,KAAKzE,QAAS,QAAO,KAAKd;YACrD,QAAO,KAAKA,MAAMqQ,MAAM,KAAKhP,aAAa,IAAI,KAAKmB,OAAO,KAAKG,IAAI;MAC5E;AAEA,aAAO,CAAA;;IAEX4N,YAAU,SAAVA,aAAa;AACT,aAAO,KAAKrN,YAAa,KAAKrC,iBAAiB,KAAKsC,YAAY,CAAA,IAAM,KAAKgN;;IAE/EK,eAAa,SAAbA,gBAAgB;AACZ,UAAI,KAAK1P,SAAS;AACd,YAAMwB,OAAO,KAAKC,OAAM;AACxB,YAAMyD,aAAa,KAAKT,aAAY;AAEpC,YAAIjD,QAAQ0D,YAAY;AACpB,iBAAO,KAAK9C,aAAa,KAAKrC,iBAAkByB,OAAO,KAAKa,UAAU,CAAC,IAAI,KAAKA,YAAa,KAAKrC,QAAQuP,MAAM/N,OAAO,KAAKE,MAAME,OAAO,KAAKF,OAAOF,OAAO,KAAKK,KAAKD,OAAO,KAAKC,IAAI;QAC1L;MACJ;AAEA,aAAO,KAAK7B;IAChB;;EAEJ2P,YAAY;IACRC,aAAaA;EACjB;AACJ;;;;UClrBqBC,KAAQ/P,YACrBgQ,UAAA,GAAAC,mBAmCK,OAnCLC,WAmCK;;IAnCCC,KAAKC,SAAUrB;IAAG,SAAOqB,SAAclB;IAAG5O,UAAUyP,KAAQzP;IAAGnB,OAAO4Q,KAAK5Q;IAAGmO,UAAM,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;aAAE8C,SAAQ9C,YAAA8C,SAAA9C,SAAA+C,MAAAD,UAAAlL,SAAA;;KAAU6K,KAAIO,KAAA,MAAA,CAAA,GAAA,CAC9GC,WAsBMR,KAAAV,QAAA,WAAA;IApBDmB,YAAYJ,SAAYjB;IACxB/P,OAAOgR,SAAWb;IAClBkB,gBAAgBL,SAAU9B;IAC1BnO,SAASuQ,MAASpO;IAClBqM,kBAAkByB,SAAgBzB;IAClCrP,UAAUyQ,KAAQzQ;IAClBuC,MAAMuO,SAAUT;IAChBzP,SAASkQ,SAAaR;IACtBZ,YAAYoB,SAAUpB;IACtBxM,aAAakO,MAAWlO;IACxBC,cAAciO,MAAYjO;IAC1B2G,UAAUgH,SAAU1L,WAAA;IACpBU,YAAYgL,SAAYzL,aAAA;IACxBjD,MAAM0O,SAAMzO,OAAA;KAfjB,WAAA;AAAA,WAsBM,CALFgP,gBAIK,OAJLT,WAIK;MAJCC,KAAKC,SAAUpB;MAAG,SAAOoB,SAAYjB;MAAGhQ,OAAOuR,MAAYjO;OAAUsN,KAAGa,IAAA,SAAA,CAAA,GAAA,EAC1EZ,UAAA,IAAA,GAAAC,mBAEUY,UAFwB,MAAAC,WAAAV,SAAAb,aAAhB,SAAAG,MAAM3K,OAAK;aACzBwL,WAAkER,KAAAV,QAAA,QAAA;aADjBtK;QAC9B2K;QAAa7K,SAASuL,SAAU9B,WAACvJ,KAAK;;;MAI1DgL,KAAU3P,cAArB4P,UAAA,GAAAC,mBAAyG,OAAzGC,WAAyG;;IAAlF,SAAM;IAA4B/Q,OAAOuR,MAAWlO;KAAUuN,KAAGa,IAAA,QAAA,CAAA,GAAA,MAAA,EAAA,KAAA,mBAAA,IAAA,IAAA,GAAA,CAC5Eb,KAAAA,kBAAkBA,KAAAA,cAAcW,MAASpO,aAArD0N,UAAA,GAAAC,mBASK,OATLC,WASK;;IATmD,SAAOE,SAAWhB;KAAUW,KAAGa,IAAA,QAAA,CAAA,GAAA,CACnEb,KAAAA,UAAUA,KAAMV,OAACC,UAC7BU,UAAA,IAAA,GAAAC,mBAEUY,UAFqB;IAAAE,KAAA;KAAAD,WAAAJ,MAAAnO,WAAb,SAAAyO,GAAGjM,OAAK;WACtBwL,WAAiHR,KAAAV,QAAA,UAAA;WADrEtK;MACvBF,SAASuL,SAAAA,iBAAiBrL,OAAOqL,SAAMzO,OAAA,KAAA;QAAAsP,SAAiBlB,KAAoBmB,qBAACpP;OAAG;;4CAG7GyO,WAEMR,KAAAA,QAAAA,eAAAA,CAAAA,GAFN,WAAA;AAAA,WAEM,CADFoB,YAAsFC,wBAAtFlB,WAAsF;MAAzEmB,MAAA;MAAK,SAAM;OAAyCtB,KAAGa,IAAA,aAAA,CAAA,GAAA,MAAA,EAAA,CAAA;8EAKpFX,mBAGUY,UAAA;IAAAE,KAAA;EAAA,GAAA,CAFNR,WAAYR,KAAAV,QAAA,SAAA,GACZkB,WAAiFR,KAAAV,QAAA,WAAA;IAA3DjQ,OAAO2Q,KAAK3Q;IAAGyC,MAAMkO,KAAK3Q;IAAGc,SAASkQ,SAAaR;;;;;;ACvCjF,IAAM0B,SAAQ,SAARA,OAAKC,MAAA;AAAA,MAAMC,KAAED,KAAFC;AAAE,SAAA,oIAAAC,OAMDD,GAAG,mBAAmB,GAACC,2BAAAA,EAAAA,OACjBD,GAAG,qBAAqB,GAAC,gCAAA,EAAAC,OACpBD,GAAG,4BAA4B,GAACC,UAAAA,EAAAA,OAAWD,GAAG,4BAA4B,GAAC,iBAAA,EAAAC,OAAkBD,GAAG,4BAA4B,GAAC,2BAAA,EAAAC,OAClID,GAAG,4BAA4B,GAACC,eAAAA,EAAAA,OAAgBD,GAAG,4BAA4B,GAAC,wBAAA,EAAAC,OACnFD,GAAG,sBAAsB,GAACC,sDAAAA,EAAAA,OAE7BD,GAAG,eAAe,GAACC,gEAAAA,EAAAA,OAIjBD,GAAG,2BAA2B,GAAC,kEAAA,EAAAC,OAI/BD,GAAG,2BAA2B,GAACC,qBAAAA,EAAAA,OACjCD,GAAG,0BAA0B,GAAC,kBAAA,EAAAC,OACjCD,GAAG,yBAAyB,GAACC,GAAAA,EAAAA,OAAID,GAAG,yBAAyB,GAACC,GAAAA,EAAAA,OAAID,GAAG,yBAAyB,GAAC,yBAAA,EAAAC,OACxFD,GAAG,0BAA0B,GAAC,wDAAA,EAAAC,OAIlCD,GAAG,0BAA0B,GAACC,+EAAAA,EAAAA,OAI9BD,GAAG,gCAAgC,GAAC,iFAAA,EAAAC,OAIpCD,GAAG,gCAAgC,GAAC,mDAAA,EAAAC,OAIlCD,GAAG,6BAA6B,GAACC,mEAAAA,EAAAA,OAKnCD,GAAG,4BAA4B,GAAC,+GAAA,EAAAC,OAOrCD,GAAG,yBAAyB,GAACC,2BAAAA,EAAAA,OAClBD,GAAG,uBAAuB,GAAC,0KAAA,EAAAC,OAStCD,GAAG,uBAAuB,GAAC,gBAAA,EAAAC,OAC3BD,GAAG,uBAAuB,GAACC,kCAAAA,EAAAA,OACTD,GAAG,sBAAsB,GAACC,gCAAAA,EAAAA,OAC5BD,GAAG,sBAAsB,GAAC,qJAAA,EAAAC,OASxCD,GAAG,kBAAkB,GAAC,GAAA,EAAAC,OAAID,GAAG,kBAAkB,GAACC,oEAAAA,EAAAA,OAGlDD,GAAG,cAAc,GAAC,+HAAA,EAAAC,OAOlBD,GAAG,0BAA0B,GAACC,0EAAAA,EAAAA,OAI9BD,GAAG,kCAAkC,GAAC,qGAAA,EAAAC,OAIbD,GAAG,kBAAkB,GAAC,8DAAA,EAAAC,OAI/CD,GAAG,uBAAuB,GAACC,sRAAAA,EAAAA,OAoBtBD,GAAG,2BAA2B,GAAC,gBAAA,EAAAC,OACpCD,GAAG,sBAAsB,GAAC,2BAAA,EAAAC,OACfD,GAAG,6BAA6B,GAACC,wBAAAA,EAAAA,OACpCD,GAAG,8BAA8B,GAACC,qBAAAA,EAAAA,OACrCD,GAAG,uBAAuB,GAAC,2CAAA,EAAAC,OAI9BD,GAAG,4BAA4B,GAACC,qLAAAA,EAAAA,OAchCD,GAAG,6BAA6B,GAAC,qBAAA,EAAAC,OAC9BD,GAAG,gCAAgC,GAACC,gBAAAA,EAAAA,OACzCD,GAAG,2BAA2B,GAACC,sBAAAA,EAAAA,OACzBD,GAAG,iCAAiC,GAAC,sGAAA,EAAAC,OAOzCD,GAAG,qBAAqB,GAACC,cAAAA,EAAAA,OAC7BD,GAAG,iBAAiB,GAAC,oQAAA,EAAAC,OAajBD,GAAG,uBAAuB,GAAC,qCAAA,EAAAC,OAE7BD,GAAG,qBAAqB,GAACC,8DAAAA,EAAAA,OAETD,GAAG,4BAA4B,GAAC,UAAA,EAAAC,OAAWD,GAAG,4BAA4B,GAACC,iBAAAA,EAAAA,OAAkBD,GAAG,4BAA4B,GAAC,4BAAA,EAAAC,OACjID,GAAG,4BAA4B,GAAC,kBAAA,EAAAC,OAAmBD,GAAG,4BAA4B,GAACC,wBAAAA,EAAAA,OACvFD,GAAG,6BAA6B,GAAC,sGAAA,EAAAC,OAIpCD,GAAG,gCAAgC,GAAC,gBAAA,EAAAC,OACzCD,GAAG,2BAA2B,GAACC,uEAAAA,EAAAA,OAI1BD,GAAG,mCAAmC,GAACC,gBAAAA,EAAAA,OAC5CD,GAAG,8BAA8B,GAAC,+EAAA,EAAAC,OAI7BD,GAAG,yCAAyC,GAACC,gBAAAA,EAAAA,OAClDD,GAAG,oCAAoC,GAAC,2FAAA,EAAAC,OAK1BD,GAAG,+BAA+B,GAACC,4BAAAA,EAAAA,OACrCD,GAAG,6BAA6B,GAACC,gBAAAA,EAAAA,OAC7CD,GAAG,wBAAwB,GAAC,kDAAA,EAAAC,OAI1BD,GAAG,8BAA8B,GAAC,uHAAA,EAAAC,OAShCD,GAAG,qBAAqB,GAACC,wBAAAA,EAAAA,OACrBD,GAAG,qBAAqB,GAAC,yBAAA,EAAAC,OACxBD,GAAG,qBAAqB,GAAC,oEAAA,EAAAC,OAI9BD,GAAG,qBAAqB,GAACC,gBAAAA,EAAAA,OAC7BD,GAAG,qBAAqB,GAAC,iBAAA,EAAAC,OACxBD,GAAG,qBAAqB,GAACC,yDAAAA,EAAAA,OAItBD,GAAG,qBAAqB,GAACC,wBAAAA,EAAAA,OACrBD,GAAG,qBAAqB,GAAC,yBAAA,EAAAC,OACxBD,GAAG,qBAAqB,GAACC,oEAAAA,EAAAA,OAI9BD,GAAG,qBAAqB,GAACC,gBAAAA,EAAAA,OAC7BD,GAAG,qBAAqB,GAAC,iBAAA,EAAAC,OACxBD,GAAG,qBAAqB,GAAC,QAAA;AAAA;AAIvC,IAAME,WAAU;EACZC,MAAM,SAANA,KAAIC,OAAA;AAAA,QAAKC,WAAQD,MAARC,UAAUC,QAAKF,MAALE,OAAOC,QAAKH,MAALG;AAAK,WAAO,CAClC,uCACA;MACI,cAAcD,MAAME;MACpB,aAAaH,SAASI;MACtB,oBAAoBJ,SAASK,aAAa;MAC1C,WAAWH,MAAMI;MACjB,yBAAyBN,SAASO;MAClC,wBAAwBL,MAAMI,WAAWJ,MAAMM;MAC/C,iBAAiBN,MAAMM;MACvB,kBAAkBR,SAASS;MAC3B,+BAA+BR,MAAMS,SAAS;MAC9C,+BAA+BT,MAAMS,SAAS;IAClD,CAAC;EACJ;EACDC,OAAO,SAAPA,MAAKC,OAAA;AAAA,QAAKZ,WAAQY,MAARZ,UAAUC,QAAKW,MAALX;AAAK,WAAO,CAC5B,kBACA;MACI,iBAAiB,CAACA,MAAMY,YAAYb,SAASW,UAAUV,MAAMa;MAC7D,wBAAwB,CAACb,MAAMY,YAAY,CAACb,SAASe,OAAO,OAAO,MAAMf,SAASW,UAAU,kBAAkBX,SAASW,MAAMK,WAAW;IAC5I,CAAC;EACJ;EACDC,WAAW;EACXC,UAAU;EACVC,aAAa;EACbC,cAAc;EACdC,SAAS;EACTC,QAAQ;EACRC,UAAU;EACVC,eAAe;EACfC,MAAM;EACNC,aAAa;EACbC,kBAAkB;EAClBC,QAAQ,SAARA,OAAMC,OAAA;AAAA,QAAK7B,WAAQ6B,MAAR7B,UAAUC,QAAK4B,MAAL5B,OAAOC,QAAK2B,MAAL3B,OAAO0B,UAAMC,MAAND,QAAQE,gBAAaD,MAAbC;AAAa,WAAO,CAC3D,mBACA;MACI,4BAA4B9B,SAAS+B,WAAWH,OAAM,KAAK3B,MAAM+B;MACjE,WAAW9B,MAAM+B,uBAAuBH;MACxC,cAAc9B,SAASkC,iBAAiBN,OAAM;IAClD,CAAC;EACJ;EACDO,aAAa;EACbC,iBAAiB;EACjBC,iBAAiB;EACjBC,cAAc;AAClB;AAEA,IAAA,cAAeC,UAAUC,OAAO;EAC5BC,MAAM;EACNhD,OAAAA;EACAI,SAAAA;AACJ,CAAC;;;ACpRD,IAAA6C,YAAe;EACXC,MAAM;EACN,WAASC;EACTC,OAAO;IACHC,SAASC;IACTC,aAAa,CAACC,QAAQC,QAAQ;IAC9BC,aAAa,CAACF,QAAQC,QAAQ;IAC9BE,gBAAgB,CAACH,QAAQC,QAAQ;IACjCG,kBAAkB,CAACJ,QAAQC,QAAQ;IACnCI,qBAAqB,CAACL,QAAQC,QAAQ;IACtCK,cAAc;MACVC,MAAMP;MACN,WAAS;;IAEbQ,QAAQC;IACRC,mBAAmBV;IACnBW,cAAcX;IACdY,iBAAiB;MACbL,MAAMP;MACN,WAAS;;IAEba,cAAc;MACVN,MAAMT;MACN,WAAS;;IAEbgB,UAAUL;IACVM,aAAa;MACTR,MAAMP;MACN,WAAS;;IAEbgB,SAAS;IACTC,WAAW;MACPV,MAAME;MACN,WAAS;;IAEbS,SAAS;MACLX,MAAMP;MACN,WAAS;;IAEbmB,YAAY;MACRZ,MAAM,CAACP,QAAQoB,MAAM;MACrB,WAAS;;IAEbC,YAAY;MACRd,MAAMa;MACN,WAAS;;IAEbE,SAAS;MACLf,MAAMP;MACN,WAAS;;IAEbuB,YAAY;MACRhB,MAAM,CAACP,QAAQoB,MAAM;MACrB,WAAS;;IAEbI,YAAY;MACRjB,MAAMa;MACN,WAAS;;IAEbK,YAAY;MACRlB,MAAM,CAACP,QAAQoB,MAAM;MACrB,WAAS;;IAEbM,cAAc;MACVnB,MAAMa;MACN,WAAS;;IAEbO,cAAc;MACVpB,MAAM,CAACP,QAAQoB,MAAM;MACrB,WAAS;;IAEbQ,YAAY;MACRrB,MAAMa;MACN,WAAS;;IAEbS,UAAU;MACNtB,MAAM,CAACP,QAAQoB,MAAM;MACrB,WAAS;;IAEbU,SAAS;MACLvB,MAAME;MACN,WAAS;;IAEbsB,WAAW;MACPxB,MAAMP;MACN,WAASgC;;IAEbC,cAAc;MACV1B,MAAMP;MACN,WAASgC;;IAEbE,YAAY;MACR3B,MAAMP;MACN,WAASgC;;IAEbG,aAAa;MACT5B,MAAMP;MACN,WAASgC;;IAEbI,mBAAmB;MACf7B,MAAME;MACN,WAAS;;IAEb4B,oBAAoB;MAChB9B,MAAME;MACN,WAAS;;IAEb6B,wBAAwB;MACpB/B,MAAMa;MACN,WAAS;;IAEbmB,iBAAiB;MACbhC,MAAME;MACN,WAAS;;IAEb+B,iBAAiB;MACbjC,MAAME;MACN,WAAS;;IAEbgC,eAAe;MACXlC,MAAME;MACN,WAAS;;IAEbiC,cAAc;MACVnC,MAAME;MACN,WAAS;;IAEbkC,mBAAmB;MACfpC,MAAME;MACN,WAAS;;IAEbmC,WAAW;MACPrC,MAAME;MACN,WAAS;;IAEboC,eAAe;MACXtC,MAAMP;MACN,WAAS;;IAEb8C,kBAAkB;MACdvC,MAAMP;MACN,WAAS;;IAEb+C,uBAAuB;MACnBxC,MAAMP;MACN,WAAS;;IAEbgD,oBAAoB;MAChBzC,MAAMP;MACN,WAAS;;IAEbiD,cAAc;MACV1C,MAAMP;MACN,WAAS;;IAEbkD,UAAU;MACN3C,MAAM4C;MACN,WAAS;;IAEbC,WAAW;MACP7C,MAAMP;MACN,WAAS;;IAEbqD,gBAAgB;MACZ9C,MAAMP;MACN,WAAS;IACb;;EAEJsD,OAAOC;EACPC,SAAO,SAAPA,WAAU;AACN,WAAO;MACHC,WAAW;MACXC,iBAAiB;;EAEzB;AACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiCA,IAAAC,WAAe;EACXjE,MAAM;EACN,WAASkE;EACTC,cAAc;EACdC,OAAO,CAAC,UAAU,SAAS,QAAQ,eAAe,eAAe,QAAQ,QAAQ,QAAQ;EACzFC,sBAAsB;EACtBC,eAAe;EACfC,gBAAgB;EAChBC,oBAAoB;EACpBC,SAAS;EACTC,MAAM;EACNC,iBAAiB;EACjBC,eAAe;EACfC,aAAa;EACbC,qBAAqB;EACrBC,MAAI,SAAJA,QAAO;AACH,WAAO;MACHC,IAAI,KAAKC,OAAOD;MAChBE,SAAS;MACTC,SAAS;MACTC,oBAAoB;MACpBC,aAAa;MACbC,gBAAgB;;;EAGxBC,OAAO;IACH,aAAa,SAAbC,SAAuBC,UAAU;AAC7B,WAAKT,KAAKS,YAAYC,kBAAiB;;IAE3CC,YAAU,SAAVA,aAAa;AACT,WAAKb,sBAAsB;;IAE/B3E,SAAO,SAAPA,UAAU;AACN,WAAKyF,gBAAe;IACxB;;EAEJC,SAAO,SAAPA,WAAU;AACN,SAAKb,KAAK,KAAKA,MAAMU,kBAAiB;AACtC,SAAKE,gBAAe;AACpB,SAAKE,uBAAsB;;EAE/BC,SAAO,SAAPA,WAAU;AACN,QAAI,KAAKT,kBAAkB,KAAKR,qBAAqB;AACjD,WAAKkB,aAAa,KAAKC,wBAAuB,CAAE;IACpD;AAEA,SAAKnB,sBAAsB;;EAE/BoB,eAAa,SAAbA,gBAAgB;AACZ,SAAKC,2BAA0B;AAC/B,SAAKC,qBAAoB;AACzB,SAAKC,yBAAwB;AAE7B,QAAI,KAAK/B,eAAe;AACpB,WAAKA,cAAcgC,QAAO;AAC1B,WAAKhC,gBAAgB;IACzB;AAEA,QAAI,KAAKG,SAAS;AACd8B,aAAOC,MAAM,KAAK/B,OAAO;AACzB,WAAKA,UAAU;IACnB;;EAEJgC,SAAS;IACLC,gBAAc,SAAdA,eAAeC,OAAOC,IAAI;AACtB,aAAO,KAAKC,0BAA0BF,QAAQC,MAAMA,GAAGD,KAAK,EAAE,OAAO;;IAEzEG,gBAAAA,SAAAA,eAAeC,SAAQ;AACnB,aAAO,KAAK1G,cAAc2G,iBAAiBD,SAAQ,KAAK1G,WAAW,IAAI0G;;IAE3EE,gBAAAA,SAAAA,eAAeF,SAAQ;AACnB,aAAO,KAAKvG,cAAcwG,iBAAiBD,SAAQ,KAAKvG,WAAW,IAAIuG;;IAE3EG,oBAAkB,SAAlBA,mBAAmBH,SAAQJ,OAAO;AAC9B,cAAQ,KAAKrF,UAAU0F,iBAAiBD,SAAQ,KAAKzF,OAAO,IAAI,KAAKwF,eAAeC,OAAM,KAAK,MAAMJ;;IAEzGQ,kBAAgB,SAAhBA,iBAAiBJ,SAAQK,aAAaT,OAAOU,KAAK;AAC9C,aAAO,KAAKC,IAAID,KAAK;QACjBE,SAAS;UACLR,QAAAA;UACAJ;UACAa,UAAU,KAAKC,WAAWV,OAAM;UAChC5B,SAAS,KAAKC,uBAAuB,KAAKsB,eAAeC,OAAOS,WAAW;UAC3EM,UAAU,KAAKC,iBAAiBZ,OAAM;QAC1C;MACJ,CAAC;;IAELY,kBAAAA,SAAAA,iBAAiBZ,SAAQ;AACrB,aAAO,KAAKtG,iBAAiBuG,iBAAiBD,SAAQ,KAAKtG,cAAc,IAAI;;IAEjFmH,eAAAA,SAAAA,cAAcb,SAAQ;AAClB,aAAO,KAAKrG,oBAAoBqG,QAAOc,eAAed,QAAOe;;IAEjEC,qBAAAA,SAAAA,oBAAoBF,aAAa;AAC7B,aAAOb,iBAAiBa,aAAa,KAAKnH,gBAAgB;;IAE9DsH,wBAAAA,SAAAA,uBAAuBH,aAAa;AAChC,aAAOb,iBAAiBa,aAAa,KAAKlH,mBAAmB;;IAEjEsH,iBAAAA,SAAAA,gBAAgBtB,OAAO;AAAA,UAAAuB,QAAA;AACnB,cAAQ,KAAKxH,mBAAmBiG,QAAQ,KAAKwB,eAAeC,MAAM,GAAGzB,KAAK,EAAE7F,OAAO,SAACiG,SAAM;AAAA,eAAKmB,MAAKN,cAAcb,OAAM;MAAC,CAAA,EAAEsB,SAAS1B,SAAS;;IAEjJ2B,MAAAA,SAAAA,KAAKC,SAAS;AACV,WAAKC,MAAM,aAAa;AACxB,WAAKlD,iBAAiB;AACtB,WAAKF,qBAAqB,KAAKA,uBAAuB,KAAK,KAAKA,qBAAqB,KAAKvC,kBAAkB,KAAK4F,4BAA2B,IAAK,KAAKrH,WAAW,KAAK,KAAK6E,wBAAuB;AAElMsC,iBAAWG,MAAM,KAAKC,MAAMC,UAAU;;IAE1CC,MAAAA,SAAAA,KAAKN,SAAS;AAAA,UAAAO,SAAA;AACV,UAAMC,QAAQ,SAARA,SAAc;AAChBD,eAAKN,MAAM,aAAa;AACxBM,eAAKxD,iBAAiB;AACtBwD,eAAK5D,UAAU;AACf4D,eAAK1D,qBAAqB;AAC1B0D,eAAKjE,cAAc;AAEnBiE,eAAKpG,sBAAsBoG,OAAKzD,cAAc;AAC9CkD,mBAAWG,MAAMI,OAAKH,MAAMC,UAAU;;AAG1CI,iBAAW,WAAM;AACbD,cAAK;MACT,GAAG,CAAC;;IAERE,SAAAA,SAAAA,QAAQC,OAAO;AACX,UAAI,KAAKxB,UAAU;AAEf;MACJ;AAEA,WAAKvC,UAAU;AAEf,UAAI,KAAKG,gBAAgB;AACrB,aAAKF,qBAAqB,KAAKA,uBAAuB,KAAK,KAAKA,qBAAqB,KAAKvC,kBAAkB,KAAK4F,4BAA2B,IAAK,KAAKrH,WAAW,KAAK,KAAK6E,wBAAuB;AAClM,aAAKD,aAAa,KAAKZ,kBAAkB;MAC7C;AAEA,WAAKoD,MAAM,SAASU,KAAK;;IAE7BC,QAAAA,SAAAA,OAAOD,OAAO;AAAA,UAAAE,uBAAAC;AACV,WAAKlE,UAAU;AACf,WAAKC,qBAAqB;AAC1B,WAAKP,cAAc;AACnB,WAAK2D,MAAM,QAAQU,KAAK;AACxB,OAAAE,yBAAAC,kBAAA,KAAKC,WAAUH,YAAM,QAAAC,0BAAA,UAArBA,sBAAAG,KAAAF,iBAAwBH,KAAK;;IAEjCM,WAAAA,SAAAA,UAAUN,OAAO;AACb,UAAI,KAAKxB,YAAY+B,UAAS,GAAI;AAC9BP,cAAMQ,eAAc;AAEpB;MACJ;AAEA,UAAMC,UAAUT,MAAMS,WAAWT,MAAMU;AAEvC,cAAQV,MAAMW,MAAI;QACd,KAAK;AACD,eAAKC,eAAeZ,KAAK;AACzB;QAEJ,KAAK;AACD,eAAKa,aAAab,OAAO,KAAK9H,QAAQ;AACtC;QAEJ,KAAK;QACL,KAAK;AACD,eAAK4I,eAAed,OAAO,KAAK9H,QAAQ;AACxC;QAEJ,KAAK;AACD,eAAK6I,UAAUf,OAAO,KAAK9H,QAAQ;AACnC;QAEJ,KAAK;AACD,eAAK8I,SAAShB,OAAO,KAAK9H,QAAQ;AAClC;QAEJ,KAAK;AACD,eAAK+I,cAAcjB,KAAK;AACxB;QAEJ,KAAK;AACD,eAAKkB,YAAYlB,KAAK;AACtB;QAEJ,KAAK;AACD,eAAKmB,WAAWnB,OAAO,KAAK9H,QAAQ;AACpC;QAEJ,KAAK;QACL,KAAK;AACD,eAAKkJ,WAAWpB,KAAK;AACrB;QAEJ,KAAK;AACD,eAAKqB,YAAYrB,KAAK;AACtB;QAEJ,KAAK;AACD,eAAKsB,SAAStB,KAAK;AACnB;QAEJ,KAAK;AACD,eAAKuB,eAAevB,OAAO,KAAK9H,QAAQ;AACxC;QAEJ,KAAK;QACL,KAAK;AAED;QAEJ;AACI,cAAI,CAACuI,WAAWe,qBAAqBxB,MAAM7B,GAAG,GAAG;AAC7C,aAAC,KAAK/B,kBAAkB,KAAKgD,KAAI;AACjC,aAAC,KAAKlH,YAAY,KAAKuJ,cAAczB,OAAOA,MAAM7B,GAAG;UACzD;AAEA;MACR;AAEA,WAAKnC,UAAU;;IAEnB0F,iBAAAA,SAAAA,gBAAgB1B,OAAO;AACnB,UAAM2B,QAAQ3B,MAAM4B,OAAOD;AAE3B,WAAKhG,cAAc;AACnB,UAAMkG,UAAU,KAAKJ,cAAczB,OAAO2B,KAAK;AAE/C,OAACE,YAAY,KAAK3F,qBAAqB;AAEvC,WAAK4F,YAAY9B,OAAO2B,KAAK;AAE7B,OAAC,KAAKvF,kBAAkB2F,WAAWJ,KAAK,KAAK,KAAKvC,KAAI;;IAE1D4C,kBAAAA,SAAAA,iBAAiBhC,OAAO;AACpB,UAAI,KAAKxB,YAAY,KAAKtF,SAAS;AAC/B;MACJ;AAEA,UAAI8G,MAAM4B,OAAOK,YAAY,WAAWjC,MAAM4B,OAAOM,aAAa,iBAAiB,MAAM,eAAelC,MAAM4B,OAAOO,QAAQ,+BAA+B,GAAG;AAC3J;MACJ,WAAW,CAAC,KAAK5G,WAAW,CAAC,KAAKA,QAAQ6G,SAASpC,MAAM4B,MAAM,GAAG;AAC9D,aAAKxF,iBAAiB,KAAKuD,KAAK,IAAI,IAAI,KAAKP,KAAK,IAAI;MAC1D;AAEA,WAAKpD,UAAU;;IAEnBqG,cAAAA,SAAAA,aAAarC,OAAO;AAChB,WAAK8B,YAAY9B,OAAO,IAAI;AAC5B,WAAKvG,uBAAuB,KAAK0C,cAAc;;IAEnDmG,oBAAAA,SAAAA,mBAAmBtC,OAAO;AACtB,UAAMuC,cAAcvC,MAAMwC,kBAAkB,KAAK/C,MAAMC,aAAa+C,yBAAyB,KAAKlH,SAAS,wCAAwC,IAAI,KAAKkE,MAAMC;AAElKF,YAAM+C,WAAW;;IAErBG,mBAAAA,SAAAA,kBAAkB1C,OAAO;AACrB,UAAMuC,cAAcvC,MAAMwC,kBAAkB,KAAK/C,MAAMC,aAAaiD,wBAAwB,KAAKpH,SAAS,wCAAwC,IAAI,KAAKkE,MAAMC;AAEjKF,YAAM+C,WAAW;;IAErBK,gBAAc,SAAdA,eAAe5C,OAAOnC,SAAuB;AAAA,UAAfgF,SAAOC,UAAA3D,SAAA,KAAA2D,UAAA,CAAA,MAAA1J,SAAA0J,UAAA,CAAA,IAAE;AACnC,UAAMnB,QAAQ,KAAK5D,eAAeF,OAAM;AAExC,WAAKiE,YAAY9B,OAAO2B,KAAK;AAC7BkB,gBAAU,KAAKlD,KAAK,IAAI;;IAE5BoD,mBAAiB,SAAjBA,kBAAkB/C,OAAOvC,OAAO;AAC5B,UAAI,KAAK3D,cAAc;AACnB,aAAKkJ,yBAAyBhD,OAAOvC,KAAK;MAC9C;;IAEJwF,gBAAAA,SAAAA,eAAejD,OAAO;AAClB,UAAM2B,QAAQ3B,MAAM4B,OAAOD;AAE3B,WAAKxF,cAAcwF;AACnB,WAAKzF,qBAAqB;AAC1B,WAAKoD,MAAM,UAAU;QAAE4D,eAAelD;QAAO2B;MAAM,CAAC;AAEpD,OAAC,KAAKhE,2BAA2B,KAAKlC,gBAAgB0H,cAAc,CAAC;;IAEzEC,iBAAAA,SAAAA,gBAAgBpD,OAAO;AAGnB,UAAIA,MAAMqD,YAAa;AAEvB,cAAQrD,MAAMW,MAAI;QACd,KAAK;AACD,eAAKC,eAAeZ,KAAK;AACzB;QAEJ,KAAK;AACD,eAAKa,aAAab,OAAO,IAAI;AAC7B;QAEJ,KAAK;QACL,KAAK;AACD,eAAKc,eAAed,OAAO,IAAI;AAC/B;QAEJ,KAAK;AACD,eAAKe,UAAUf,OAAO,IAAI;AAC1B;QAEJ,KAAK;AACD,eAAKgB,SAAShB,OAAO,IAAI;AACzB;QAEJ,KAAK;QACL,KAAK;AACD,eAAKoB,WAAWpB,KAAK;AACrB;QAEJ,KAAK;AACD,eAAKqB,YAAYrB,KAAK;AACtB;QAEJ,KAAK;AACD,eAAKsB,SAAStB,OAAO,IAAI;AACzB;MAIR;;IAEJsD,cAAY,SAAZA,eAAe;AACX,WAAKpH,qBAAqB;;IAE9BqH,iBAAe,SAAfA,kBAAkB;AACd,UAAI,KAAKnH,gBAAgB;AACrB,aAAKoH,aAAY;MACrB;;IAEJC,gBAAAA,SAAAA,eAAezD,OAAO;AAClB0D,sBAAgBC,KAAK,iBAAiB;QAClCT,eAAelD;QACf4B,QAAQ,KAAKgC;MACjB,CAAC;;IAELC,kBAAAA,SAAAA,iBAAiB7D,OAAO;AACpB,cAAQA,MAAMW,MAAI;QACd,KAAK;AACD,eAAKU,YAAYrB,KAAK;AACtB;MAIR;;IAEJY,gBAAAA,SAAAA,eAAeZ,OAAO;AAClB,UAAI,CAAC,KAAK5D,gBAAgB;AACtB,aAAKgD,KAAI;AACT,aAAKlH,YAAY,KAAK8K,yBAAyBhD,OAAO,KAAKjD,wBAAuB,CAAE;MACxF,OAAO;AACH,YAAM+G,cAAc,KAAK5H,uBAAuB,KAAK,KAAK6H,oBAAoB,KAAK7H,kBAAkB,IAAI,KAAKF,UAAU,KAAKgI,qBAAoB,IAAK,KAAKzE,4BAA2B;AAEtL,aAAKyD,yBAAyBhD,OAAO8D,WAAW;MACpD;AAEA9D,YAAMQ,eAAc;;IAExBK,cAAAA,SAAAA,aAAab,OAAmC;AAAA,UAA5BiE,qBAAAA,UAAAA,SAAAA,KAAAA,UAAAA,CAAAA,MAAAA,SAAAA,UAAAA,CAAAA,IAAqB;AACrC,UAAIjE,MAAMkE,UAAU,CAACD,oBAAoB;AACrC,YAAI,KAAK/H,uBAAuB,IAAI;AAChC,eAAK0G,eAAe5C,OAAO,KAAKf,eAAe,KAAK/C,kBAAkB,CAAC;QAC3E;AAEA,aAAKE,kBAAkB,KAAKuD,KAAI;AAChCK,cAAMQ,eAAc;MACxB,OAAO;AACH,YAAMsD,cAAc,KAAK5H,uBAAuB,KAAK,KAAKiI,oBAAoB,KAAKjI,kBAAkB,IAAI,KAAKF,UAAU,KAAKoI,oBAAmB,IAAK,KAAKC,2BAA0B;AAEpL,aAAKrB,yBAAyBhD,OAAO8D,WAAW;AAEhD,SAAC,KAAK1H,kBAAkB,KAAKgD,KAAI;AACjCY,cAAMQ,eAAc;MACxB;;IAEJM,gBAAAA,SAAAA,eAAed,OAAmC;AAAA,UAA5BiE,qBAAAA,UAAAA,SAAAA,KAAAA,UAAAA,CAAAA,MAAAA,SAAAA,UAAAA,CAAAA,IAAqB;AACvCA,6BAAuB,KAAK/H,qBAAqB;;IAErD6E,WAAAA,SAAAA,UAAUf,OAAmC;AAAA,UAA5BiE,qBAAAA,UAAAA,SAAAA,KAAAA,UAAAA,CAAAA,MAAAA,SAAAA,UAAAA,CAAAA,IAAqB;AAClC,UAAIA,oBAAoB;AACpB,YAAMrC,SAAS5B,MAAMsE;AAErB,YAAItE,MAAMuE,UAAU;AAChB3C,iBAAO4C,kBAAkB,GAAGxE,MAAM4B,OAAO6C,cAAc;QAC3D,OAAO;AACH7C,iBAAO4C,kBAAkB,GAAG,CAAC;AAC7B,eAAKtI,qBAAqB;QAC9B;MACJ,OAAO;AACH,aAAK8G,yBAAyBhD,OAAO,KAAKgE,qBAAoB,CAAE;AAEhE,SAAC,KAAK5H,kBAAkB,KAAKgD,KAAI;MACrC;AAEAY,YAAMQ,eAAc;;IAExBQ,UAAAA,SAAAA,SAAShB,OAAmC;AAAA,UAA5BiE,qBAAAA,UAAAA,SAAAA,KAAAA,UAAAA,CAAAA,MAAAA,SAAAA,UAAAA,CAAAA,IAAqB;AACjC,UAAIA,oBAAoB;AACpB,YAAMrC,SAAS5B,MAAMsE;AAErB,YAAItE,MAAMuE,UAAU;AAChB3C,iBAAO4C,kBAAkBxE,MAAM4B,OAAO6C,gBAAgB7C,OAAOD,MAAMxC,MAAM;QAC7E,OAAO;AACH,cAAMuF,MAAM9C,OAAOD,MAAMxC;AAEzByC,iBAAO4C,kBAAkBE,KAAKA,GAAG;AACjC,eAAKxI,qBAAqB;QAC9B;MACJ,OAAO;AACH,aAAK8G,yBAAyBhD,OAAO,KAAKoE,oBAAmB,CAAE;AAE/D,SAAC,KAAKhI,kBAAkB,KAAKgD,KAAI;MACrC;AAEAY,YAAMQ,eAAc;;IAExBU,aAAAA,SAAAA,YAAYlB,OAAO;AACf,WAAKlD,aAAa,CAAC;AACnBkD,YAAMQ,eAAc;;IAExBS,eAAAA,SAAAA,cAAcjB,OAAO;AACjB,WAAKlD,aAAa,KAAKmC,eAAeE,SAAS,CAAC;AAChDa,YAAMQ,eAAc;;IAExBY,YAAAA,SAAAA,WAAWpB,OAAO;AACd,UAAI,CAAC,KAAK5D,gBAAgB;AACtB,aAAKF,qBAAqB;AAC1B,aAAK0E,eAAeZ,KAAK;MAC7B,OAAO;AACH,YAAI,KAAK9D,uBAAuB,IAAI;AAChC,eAAK0G,eAAe5C,OAAO,KAAKf,eAAe,KAAK/C,kBAAkB,CAAC;QAC3E;AAEA,aAAKyD,KAAI;MACb;AAEAK,YAAMQ,eAAc;;IAExBW,YAAAA,SAAAA,WAAWnB,OAAmC;AAAA,UAA5BiE,qBAAAA,UAAAA,SAAAA,KAAAA,UAAAA,CAAAA,MAAAA,SAAAA,UAAAA,CAAAA,IAAqB;AACnC,OAACA,sBAAsB,KAAK7C,WAAWpB,KAAK;;IAEhDqB,aAAAA,SAAAA,YAAYrB,OAAO;AACf,WAAK5D,kBAAkB,KAAKuD,KAAK,IAAI;AACrCK,YAAMQ,eAAc;AACpBR,YAAM2E,gBAAe;;IAEzBrD,UAAAA,SAAAA,SAAStB,OAAmC;AAAA,UAA5BiE,qBAAAA,UAAAA,SAAAA,KAAAA,UAAAA,CAAAA,MAAAA,SAAAA,UAAAA,CAAAA,IAAqB;AACjC,UAAI,CAACA,oBAAoB;AACrB,YAAI,KAAK7H,kBAAkB,KAAKwI,qBAAoB,GAAI;AACpDpF,gBAAM,KAAKC,MAAMoF,oCAAoC;AAErD7E,gBAAMQ,eAAc;QACxB,OAAO;AACH,cAAI,KAAKtE,uBAAuB,IAAI;AAChC,iBAAK0G,eAAe5C,OAAO,KAAKf,eAAe,KAAK/C,kBAAkB,CAAC;UAC3E;AAEA,eAAKE,kBAAkB,KAAKuD,KAAK,KAAK/H,MAAM;QAChD;MACJ;;IAEJ2J,gBAAAA,SAAAA,eAAevB,OAAmC;AAAA,UAA5BiE,qBAAAA,UAAAA,SAAAA,KAAAA,UAAAA,CAAAA,MAAAA,SAAAA,UAAAA,CAAAA,IAAqB;AACvC,UAAIA,oBAAoB;AACpB,SAAC,KAAK7H,kBAAkB,KAAKgD,KAAI;MACrC;;IAEJ0F,gBAAAA,SAAAA,eAAeC,IAAI;AAAA,UAAAC,SAAA;AACf3H,aAAO4H,IAAI,WAAWF,IAAI,KAAKG,UAAUC,OAAOC,OAAO7J,OAAO;AAE9D8J,eAASN,IAAI;QAAEO,UAAU;QAAYC,KAAK;QAAKC,MAAM;MAAI,CAAC;AAC1D,WAAKhC,aAAY;AACjB,WAAK1G,aAAY;AAEjBgD,iBAAW,WAAM;AACbkF,eAAKpL,mBAAmBoL,OAAKpN,UAAU4H,MAAMwF,OAAKvF,MAAMgG,YAAY7B,GAAG;SACxE,CAAC;;IAER8B,qBAAmB,SAAnBA,sBAAsB;AAClB,WAAKC,yBAAwB;AAC7B,WAAKC,mBAAkB;AACvB,WAAKC,mBAAkB;AAEvB,WAAKvG,MAAM,MAAM;;IAErBwG,gBAAc,SAAdA,iBAAiB;AAAA,UAAAC,SAAA;AACb,WAAK9I,2BAA0B;AAC/B,WAAK+I,qBAAoB;AACzB,WAAK9I,qBAAoB;AAEzB,UAAI,KAAKtD,mBAAmB,KAAKhC,UAAU,CAAC,KAAKM,UAAU;AACvD,aAAK+N,UAAU,WAAM;AACjBzG,gBAAMuG,OAAKtG,MAAMgG,YAAY7B,GAAG;QACpC,CAAC;MACL;AAEA,WAAKtE,MAAM,MAAM;AACjB,WAAK/D,UAAU;;IAEnB2K,qBAAAA,SAAAA,oBAAoBnB,IAAI;AACpB1H,aAAOC,MAAMyH,EAAE;;IAEnBvB,cAAY,SAAZA,eAAe;AACX,UAAI,KAAKvK,aAAa,QAAQ;AAC1BkN,yBAAiB,KAAK5K,SAAS,KAAKqI,GAAG;MAC3C,OAAO;AACH,aAAKrI,QAAQb,MAAM0L,WAAWC,cAAc,KAAKzC,GAAG,IAAI;AACxD0C,yBAAiB,KAAK/K,SAAS,KAAKqI,GAAG;MAC3C;;IAEJ+B,0BAAwB,SAAxBA,2BAA2B;AAAA,UAAAY,SAAA;AACvB,UAAI,CAAC,KAAKpL,sBAAsB;AAC5B,aAAKA,uBAAuB,SAAC6E,OAAU;AACnC,cAAIuG,OAAKnK,kBAAkBmK,OAAKhL,WAAW,CAACgL,OAAK3C,IAAIxB,SAASpC,MAAM4B,MAAM,KAAK,CAAC2E,OAAKhL,QAAQ6G,SAASpC,MAAM4B,MAAM,GAAG;AACjH2E,mBAAK5G,KAAI;UACb;;AAGJ6G,iBAASC,iBAAiB,SAAS,KAAKtL,oBAAoB;MAChE;;IAEJ8B,4BAA0B,SAA1BA,6BAA6B;AACzB,UAAI,KAAK9B,sBAAsB;AAC3BqL,iBAASE,oBAAoB,SAAS,KAAKvL,oBAAoB;AAC/D,aAAKA,uBAAuB;MAChC;;IAEJyK,oBAAkB,SAAlBA,qBAAqB;AAAA,UAAAe,SAAA;AACjB,UAAI,CAAC,KAAKvL,eAAe;AACrB,aAAKA,gBAAgB,IAAIwL,8BAA8B,KAAKnH,MAAMoH,WAAW,WAAM;AAC/E,cAAIF,OAAKvK,gBAAgB;AACrBuK,mBAAKhH,KAAI;UACb;QACJ,CAAC;MACL;AAEA,WAAKvE,cAAcwK,mBAAkB;;IAEzCI,sBAAoB,SAApBA,uBAAuB;AACnB,UAAI,KAAK5K,eAAe;AACpB,aAAKA,cAAc4K,qBAAoB;MAC3C;;IAEJH,oBAAkB,SAAlBA,sBAAqB;AAAA,UAAAiB,SAAA;AACjB,UAAI,CAAC,KAAKzL,gBAAgB;AACtB,aAAKA,iBAAiB,WAAM;AACxB,cAAIyL,OAAK1K,kBAAkB,CAAC2K,cAAa,GAAI;AACzCD,mBAAKnH,KAAI;UACb;;AAGJqH,eAAOP,iBAAiB,UAAU,KAAKpL,cAAc;MACzD;;IAEJ6B,sBAAoB,SAApBA,wBAAuB;AACnB,UAAI,KAAK7B,gBAAgB;AACrB2L,eAAON,oBAAoB,UAAU,KAAKrL,cAAc;AACxD,aAAKA,iBAAiB;MAC1B;;IAEJuB,wBAAsB,SAAtBA,yBAAyB;AAAA,UAAAqK,SAAA;AACrB,UAAI,CAAC,KAAK/O,YAAY,CAAC,KAAKoD,oBAAoB;AAC5C,YAAM4L,SAAQV,SAASW,cAAa,cAAAC,OAAe,KAAK1O,SAAO,IAAA,CAAI;AAEnE,YAAIwO,UAASG,UAAUH,MAAK,GAAG;AAC3B,eAAK5L,qBAAqB,WAAM;AAC5BkE,kBAAMyH,OAAKxH,MAAMC,UAAU;;AAG/BwH,UAAAA,OAAMT,iBAAiB,SAAS,KAAKnL,kBAAkB;QAC3D;MACJ;;IAEJ6B,0BAAwB,SAAxBA,2BAA2B;AACvB,UAAI,KAAK7B,oBAAoB;AACzB,YAAM4L,SAAQV,SAASW,cAAa,cAAAC,OAAe,KAAK1O,SAAO,IAAA,CAAI;AAEnE,YAAIwO,UAASG,UAAUH,MAAK,GAAG;AAC3BA,UAAAA,OAAMR,oBAAoB,SAAS,KAAKpL,kBAAkB;QAC9D;MACJ;;IAEJsJ,sBAAoB,SAApBA,uBAAuB;AACnB,aAAO0C,qBAAqB,KAAK/L,SAAS,wCAAwC,EAAE4D,SAAS;;IAEjGoI,iBAAAA,SAAAA,gBAAgB1J,SAAQ;AAAA,UAAA2J;AACpB,aAAO,KAAKC,cAAc5J,OAAM,KAAK,OAAO,KAAKD,eAAeC,OAAM,MAAM,cAAO2J,uBAAK,KAAK5J,eAAeC,OAAM,OAAC2J,QAAAA,yBAAA,SAAA,SAA3BA,qBAA6BE,kBAAkB,KAAK3P,YAAY,EAAE4P,WAAW,KAAKhM,YAAY+L,kBAAkB,KAAK3P,YAAY,CAAC;;IAE9N0P,eAAAA,SAAAA,cAAc5J,SAAQ;AAClB,aAAOkE,WAAWlE,OAAM,KAAK,EAAE,KAAKY,iBAAiBZ,OAAM,KAAK,KAAKa,cAAcb,OAAM;;IAE7F+J,uBAAAA,SAAAA,sBAAsB/J,SAAQ;AAC1B,aAAO,KAAK4J,cAAc5J,OAAM,KAAK,KAAKU,WAAWV,OAAM;;IAE/DU,YAAAA,SAAAA,WAAWV,SAAQ;AACf,aAAOgK,OAAO,KAAKC,SAAS,KAAK/J,eAAeF,OAAM,GAAG,KAAKkK,WAAW;;IAE7E/D,sBAAoB,SAApBA,uBAAuB;AAAA,UAAAgE,SAAA;AACnB,aAAO,KAAK/I,eAAegJ,UAAU,SAACpK,SAAM;AAAA,eAAKmK,OAAKP,cAAc5J,OAAM;OAAE;;IAEhFuG,qBAAmB,SAAnBA,sBAAsB;AAAA,UAAA8D,UAAA;AAClB,aAAOC,cAAc,KAAKlJ,gBAAgB,SAACpB,SAAM;AAAA,eAAKqK,QAAKT,cAAc5J,OAAM;OAAE;;IAErFkG,qBAAAA,SAAAA,oBAAoBtG,OAAO;AAAA,UAAA2K,UAAA;AACvB,UAAMC,qBAAqB5K,QAAQ,KAAKwB,eAAeE,SAAS,IAAI,KAAKF,eAAeC,MAAMzB,QAAQ,CAAC,EAAEwK,UAAU,SAACpK,SAAM;AAAA,eAAKuK,QAAKX,cAAc5J,OAAM;OAAG,IAAE;AAE7J,aAAOwK,qBAAqB,KAAKA,qBAAqB5K,QAAQ,IAAIA;;IAEtE0G,qBAAAA,SAAAA,oBAAoB1G,OAAO;AAAA,UAAA6K,UAAA;AACvB,UAAMD,qBAAqB5K,QAAQ,IAAI0K,cAAc,KAAKlJ,eAAeC,MAAM,GAAGzB,KAAK,GAAG,SAACI,SAAM;AAAA,eAAKyK,QAAKb,cAAc5J,OAAM;OAAC,IAAI;AAEpI,aAAOwK,qBAAqB,KAAKA,qBAAqB5K;;IAE1DV,yBAAuB,SAAvBA,0BAA0B;AAAA,UAAAwL,UAAA;AACtB,aAAO,KAAKC,UAAU,KAAKvJ,eAAegJ,UAAU,SAACpK,SAAM;AAAA,eAAK0K,QAAKX,sBAAsB/J,OAAM;OAAC,IAAI;;IAE1G0B,6BAA2B,SAA3BA,8BAA8B;AAC1B,UAAMkJ,gBAAgB,KAAK1L,wBAAuB;AAElD,aAAO0L,gBAAgB,IAAI,KAAKzE,qBAAoB,IAAKyE;;IAE7DpE,4BAA0B,SAA1BA,6BAA6B;AACzB,UAAMoE,gBAAgB,KAAK1L,wBAAuB;AAElD,aAAO0L,gBAAgB,IAAI,KAAKrE,oBAAmB,IAAKqE;;IAE5DhH,eAAa,SAAbA,cAAczB,OAAO0I,OAAM;AAAA,UAAAC,UAAA;AACvB,WAAKhN,eAAe,KAAKA,eAAe,MAAM+M;AAE9C,UAAI5E,cAAc;AAClB,UAAIjC,UAAU;AAEd,UAAIE,WAAW,KAAKpG,WAAW,GAAG;AAC9B,YAAI,KAAKO,uBAAuB,IAAI;AAChC4H,wBAAc,KAAK7E,eAAeC,MAAM,KAAKhD,kBAAkB,EAAE+L,UAAU,SAACpK,SAAM;AAAA,mBAAK8K,QAAKpB,gBAAgB1J,OAAM;WAAE;AACpHiG,wBAAcA,gBAAgB,KAAK,KAAK7E,eAAeC,MAAM,GAAG,KAAKhD,kBAAkB,EAAE+L,UAAU,SAACpK,SAAM;AAAA,mBAAK8K,QAAKpB,gBAAgB1J,OAAM;UAAC,CAAA,IAAIiG,cAAc,KAAK5H;QACtK,OAAO;AACH4H,wBAAc,KAAK7E,eAAegJ,UAAU,SAACpK,SAAM;AAAA,mBAAK8K,QAAKpB,gBAAgB1J,OAAM;WAAE;QACzF;AAEA,YAAIiG,gBAAgB,IAAI;AACpBjC,oBAAU;QACd;AAEA,YAAIiC,gBAAgB,MAAM,KAAK5H,uBAAuB,IAAI;AACtD4H,wBAAc,KAAKvE,4BAA2B;QAClD;AAEA,YAAIuE,gBAAgB,IAAI;AACpB,eAAKd,yBAAyBhD,OAAO8D,WAAW;QACpD;MACJ;AAEA,UAAI,KAAKpI,eAAe;AACpBkN,qBAAa,KAAKlN,aAAa;MACnC;AAEA,WAAKA,gBAAgBoE,WAAW,WAAM;AAClC6I,gBAAKhN,cAAc;AACnBgN,gBAAKjN,gBAAgB;SACtB,GAAG;AAEN,aAAOmG;;IAEXmB,0BAAwB,SAAxBA,yBAAyBhD,OAAOvC,OAAO;AACnC,UAAI,KAAKvB,uBAAuBuB,OAAO;AACnC,aAAKvB,qBAAqBuB;AAC1B,aAAKX,aAAY;AAEjB,YAAI,KAAKjD,eAAe;AACpB,eAAK+I,eAAe5C,OAAO,KAAKf,eAAexB,KAAK,GAAG,KAAK;QAChE;MACJ;;IAEJX,cAAY,SAAZA,gBAAyB;AAAA,UAAA+L,UAAA;AAAA,UAAZpL,QAAAA,UAAAA,SAAAA,KAAAA,UAAAA,CAAAA,MAAAA,SAAAA,UAAAA,CAAAA,IAAQ;AACjB,WAAKwI,UAAU,WAAM;AACjB,YAAMnK,KAAK2B,UAAU,KAAC,GAAA2J,OAAOyB,QAAK/M,IAAE,GAAA,EAAAsL,OAAI3J,KAAK,IAAKoL,QAAKC;AACvD,YAAMC,UAAUC,WAAWH,QAAKrN,MAAI,UAAA4L,OAAYtL,IAAE,IAAA,CAAI;AAEtD,YAAIiN,SAAS;AACTA,kBAAQE,kBAAkBF,QAAQE,eAAe;YAAEC,OAAO;YAAWC,QAAQ;UAAQ,CAAC;QAC1F,WAAW,CAACN,QAAKlL,yBAAyB;AACtCkL,kBAAKpN,mBAAmBoN,QAAKpN,gBAAgB0H,cAAc1F,UAAU,KAAKA,QAAQoL,QAAK3M,kBAAkB;QAC7G;MACJ,CAAC;;IAELQ,iBAAe,SAAfA,kBAAkB;AACd,UAAI,KAAK7C,iBAAiB,KAAKF,mBAAmB,CAAC,KAAK6O,SAAS;AAC7D,aAAKtM,qBAAqB,KAAKqD,4BAA2B;AAC1D,aAAKqD,eAAe,MAAM,KAAK3D,eAAe,KAAK/C,kBAAkB,GAAG,KAAK;MACjF;;IAEJ4F,aAAW,SAAXA,YAAY9B,OAAO2B,OAAO;AACtB,WAAKyH,WAAWzH,OAAO3B,KAAK;AAC5B,WAAKV,MAAM,UAAU;QAAE4D,eAAelD;QAAO2B;MAAM,CAAC;;IAExD0H,aAAAA,SAAAA,YAAYpS,UAAS;AAAA,UAAAqS,UAAA;AACjB,cAAQrS,YAAW,CAAA,GAAIsS,OAAO,SAACC,QAAQ3L,SAAQJ,OAAU;AACrD+L,eAAOC,KAAK;UAAE9K,aAAad;UAAQe,OAAO;UAAMnB;QAAM,CAAC;AAEvD,YAAMhG,sBAAsB6R,QAAKxK,uBAAuBjB,OAAM;AAE9DpG,+BAAuBA,oBAAoBiS,QAAQ,SAACC,GAAC;AAAA,iBAAKH,OAAOC,KAAKE,CAAC;SAAE;AAEzE,eAAOH;SACR,CAAA,CAAE;;IAETI,YAAAA,SAAAA,WAAW7E,IAAI;AACX,WAAKxJ,UAAUwJ;;IAEnB8E,SAAO,SAAPA,QAAQ9E,IAAI+E,aAAY;AACpB,WAAKtO,OAAOuJ;AACZ+E,MAAAA,eAAcA,YAAW/E,EAAE;;IAE/BgF,oBAAAA,SAAAA,mBAAmBhF,IAAI;AACnB,WAAKtJ,kBAAkBsJ;IAC3B;;EAEJiF,UAAU;IACN/K,gBAAc,SAAdA,iBAAiB;AAAA,UAAAgL,UAAA;AACb,UAAMhT,WAAU,KAAKO,mBAAmB,KAAK6R,YAAY,KAAKpS,OAAO,IAAI,KAAKA,WAAW,CAAA;AAEzF,UAAI,KAAKkF,aAAa;AAClB,YAAM+N,kBAAkBC,cAAcvS,OAAOX,UAAS,KAAKmT,cAAc,KAAKjO,aAAa,KAAKnE,iBAAiB,KAAKD,YAAY;AAElI,YAAI,KAAKP,kBAAkB;AACvB,cAAM6S,eAAe,KAAKpT,WAAW,CAAA;AACrC,cAAMqT,WAAW,CAAA;AAEjBD,uBAAaX,QAAQ,SAAC9K,OAAU;AAC5B,gBAAM2L,gBAAgBN,QAAKnL,uBAAuBF,KAAK;AACvD,gBAAM4L,gBAAgBD,cAAc3S,OAAO,SAAC6S,MAAI;AAAA,qBAAKP,gBAAgBQ,SAASD,IAAI;aAAE;AAEpF,gBAAID,cAAcrL,SAAS,EAAGmL,UAASb,KAAIkB,eAAAA,eAAA,CAAA,GAAM/L,KAAK,GAAAgM,CAAAA,GAAAA,iBAAG,CAAA,GAAA,OAAOX,QAAKxS,wBAAwB,WAAWwS,QAAKxS,sBAAsB,SAAOoT,mBAAOL,aAAa,CAAA,CAAA,CAAG;UACrK,CAAC;AAED,iBAAO,KAAKnB,YAAYiB,QAAQ;QACpC;AAEA,eAAOJ;MACX;AAEA,aAAOjT;;;IAGX6T,mBAAiB,SAAjBA,oBAAoB;AAChB,aAAO,KAAKtC;;IAEhBtB,OAAK,SAALA,SAAQ;AACJ,UAAM6D,sBAAsB,KAAKhO,wBAAuB;AAExD,aAAOgO,wBAAwB,KAAK,KAAKnN,eAAe,KAAKqB,eAAe8L,mBAAmB,CAAC,IAAI,KAAK5S,eAAe;;IAE5H6S,oBAAkB,SAAlBA,qBAAqB;AACjB,UAAMD,sBAAsB,KAAKhO,wBAAuB;AAExD,aAAOgO,wBAAwB,KAAK,KAAKnN,eAAe,KAAKqB,eAAe8L,mBAAmB,CAAC,IAAI,KAAKjD,WAAW;;IAExHC,aAAW,SAAXA,cAAc;AACV,aAAO,KAAKzQ,cAAc,OAAO,KAAKc;;IAE1CgS,cAAY,SAAZA,eAAe;AACX,aAAO,KAAKnS,gBAAgB,CAAC,KAAKd,WAAW;;IAEjD8T,yBAAuB,SAAvBA,0BAA0B;AACtB,aAAOlJ,WAAW,KAAK9C,cAAc,IAAI,KAAKiM,kBAAkBC,WAAW,OAAO,KAAKlM,eAAeE,MAAM,IAAI,KAAKiM;;IAEzHF,mBAAiB,SAAjBA,oBAAoB;AAChB,aAAO,KAAKjR,iBAAiB,KAAKiL,UAAUC,OAAOkG,OAAOC,iBAAiB;;IAE/EF,wBAAsB,SAAtBA,yBAAyB;AACrB,aAAO,KAAKhR,sBAAsB,KAAK8K,UAAUC,OAAOkG,OAAOE,sBAAsB,KAAKrG,UAAUC,OAAOkG,OAAOjR,sBAAsB;;IAE5IoR,kBAAgB,SAAhBA,mBAAmB;AACf,aAAO,KAAKnR,gBAAgB,KAAK6K,UAAUC,OAAOkG,OAAOhR,gBAAgB;;IAE7EoR,sBAAoB,SAApBA,uBAAuB;AACnB,aAAO,KAAKvR,oBAAoB,KAAKgL,UAAUC,OAAOkG,OAAOnR,oBAAoB;;IAErFwR,2BAAyB,SAAzBA,4BAA4B;AACxB,aAAO,KAAKvR,yBAAyB,KAAK+K,UAAUC,OAAOkG,OAAOlR,yBAAyB;;IAE/FwR,qBAAmB,SAAnBA,sBAAsB;AAClB,aAAO,KAAKnD,UAAU,KAAKiD,qBAAqBN,WAAW,OAAO,GAAG,IAAI,KAAKO;;IAElF5C,iBAAe,SAAfA,kBAAkB;AACd,aAAO,KAAK5M,uBAAuB,KAAC,GAAAkL,OAAO,KAAKtL,IAAE,GAAA,EAAAsL,OAAI,KAAKlL,kBAAkB,IAAK;;IAEtF0P,aAAW,SAAXA,cAAc;AAAA,UAAAC,UAAA;AACV,aAAO,KAAK5M,eAAerH,OAAO,SAACiG,SAAM;AAAA,eAAK,CAACgO,QAAKnN,cAAcb,OAAM;MAAC,CAAA,EAAEsB;;IAE/E2M,oBAAkB,SAAlBA,qBAAqB;AACjB,aAAO,KAAKzT,aAAa,KAAKyP,WAAW,QAAQ/F,WAAW,KAAK9K,OAAO;;IAE5E0G,yBAAuB,SAAvBA,0BAA0B;AACtB,aAAO,CAAC,KAAKjE;IACjB;;EAEJqS,YAAY;IACRC,QAAQC;;EAEZC,YAAY;IACRC,WAAAA;IACAC,iBAAAA;IACAC,QAAAA;IACAC,WAAAA;IACAC,WAAAA;IACAC,WAAAA;IACAC,iBAAAA;IACAC,aAAAA;IACAC,YAAAA;IACAC,WAAAA;IACAC,WAAAA;EACJ;AACJ;;;;;;;;;;;;;;;;;;ACpgCI,SAAAC,UAAA,GAAAC,mBA2LK,OA3LLC,WA2LK;IA3LAC,KAAI;IAAanR,IAAIoR,MAAEpR;IAAG,SAAOqR,KAAEC,GAAA,MAAA;IAAWC,SAAK,OAAA,EAAA,MAAA,OAAA,EAAA,IAAA,WAAA;aAAEC,SAAgBtL,oBAAAsL,SAAAtL,iBAAAuL,MAAAD,UAAAxK,SAAA;;KAAUqK,KAAIK,KAAA,MAAA,CAAA,GAAA,CAE1EL,KAAQjV,YADlB4U,UAAA,GAAAC,mBAyBC,SAzBDC,WAyBC;;IAvBGC,KAAI;IACHnR,IAAIqR,KAAQzU,WAAGyU,KAAO7U;IACvBX,MAAK;IACJ,SAAQ,CAAAwV,KAAAC,GAAa,OAAA,GAAAD,KAAA5U,YAAY4U,KAAUxU,UAAA;IAC3C+B,OAAK,CAAGyS,KAAU1U,YAAE0U,KAAUvU,UAAA;IAC9B+I,OAAO2L,SAAkBtC;IACzB7S,aAAagV,KAAWhV;IACxBmC,UAAQ,CAAG6S,KAAO3O,WAAI2O,KAAS7S,WAAA;IAC/BkE,UAAU2O,KAAQ3O;IACnBiP,cAAa;IACbC,MAAK;IACJ,cAAYP,KAAS3S;IACrB,mBAAiB2S,KAAc1S;IAChC,iBAAc;IACb,iBAAeyS,MAAc9Q;IAC7B,iBAAe8Q,MAACpR,KAAA;IAChB,yBAAuBoR,MAAAjR,UAAUqR,SAAAxE,kBAAkB1P;IACnD,gBAAc+T,KAAMQ,WAAKvU;IACzB2G,SAAK,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;aAAEuN,SAAOvN,WAAAuN,SAAAvN,QAAAwN,MAAAD,UAAAxK,SAAA;IAAA;IACd7C,QAAI,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;aAAEqN,SAAMrN,UAAAqN,SAAArN,OAAAsN,MAAAD,UAAAxK,SAAA;IAAA;IACZ8K,WAAO,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;aAAEN,SAAShN,aAAAgN,SAAAhN,UAAAiN,MAAAD,UAAAxK,SAAA;IAAA;IAClB+K,SAAK,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;aAAEP,SAAe5L,mBAAA4L,SAAA5L,gBAAA6L,MAAAD,UAAAxK,SAAA;;KACfqK,KAAG/O,IAAA,OAAA,CAAA,GAAA,MAAA,IAAA0P,UAAA,MAEfhB,UAAA,GAAAC,mBAqBM,QArBNC,WAqBM;;IAnBFC,KAAI;IACHnR,IAAIqR,KAAQzU,WAAGyU,KAAO7U;IACtB,SAAQ,CAAA6U,KAAAC,GAAa,OAAA,GAAAD,KAAA5U,YAAY4U,KAAUxU,UAAA;IAC3C+B,OAAK,CAAGyS,KAAU1U,YAAE0U,KAAUvU,UAAA;IAC9B0B,UAAQ,CAAG6S,KAAO3O,WAAI2O,KAAS7S,WAAA;IAChCoT,MAAK;IACJ,cAAYP,KAAU3S,cAAI8S,SAAAA,UAA2B,iBAAAlU,SAAYkU,SAAKpG;IACtE,mBAAiBiG,KAAc1S;IAChC,iBAAc;IACb,iBAAeyS,MAAc9Q;IAC7B,iBAAe8Q,MAACpR,KAAA;IAChB,yBAAuBoR,MAAAjR,UAAUqR,SAAAxE,kBAAkB1P;IACnD,iBAAe+T,KAAQ3O;IACvBuB,SAAK,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;aAAEuN,SAAOvN,WAAAuN,SAAAvN,QAAAwN,MAAAD,UAAAxK,SAAA;IAAA;IACd7C,QAAI,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;aAAEqN,SAAMrN,UAAAqN,SAAArN,OAAAsN,MAAAD,UAAAxK,SAAA;IAAA;IACZ8K,WAAO,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;aAAEN,SAAShN,aAAAgN,SAAAhN,UAAAiN,MAAAD,UAAAxK,SAAA;;KACXqK,KAAG/O,IAAA,OAAA,CAAA,GAAA,CAEX2P,WAAiIZ,KAAAa,QAAA,SAAA;IAA7GrM,OAAOwL,KAAOrF;IAAG3P,aAAagV,KAAWhV;KAA7D,WAAA;AAAA,QAAA8V;AAAA,WAAiI,CAA/DC,gBAAAC,gBAAAb,SAAApG,UAAM,iBAAA,OAAA,kBAA2BoG,SAAKpG,WAAA+G,QAAAA,oBAAAA,SAAAA,kBAAA,OAAA,GAAA,CAAA,CAAA;yBAEhGX,SAAkBxB,qBAA9BiC,WAEMZ,KAAAa,QAAA,aAAA;;IAF4C,SAAA,eAAOb,KAAEC,GAAA,WAAA,CAAA;IAAgBgB,eAAed,SAAYjL;KAAtG,WAAA;AAAA,WAEM,EAAA,UAAA,GADFgM,YAAkLC,wBAAlKnB,KAAUhU,YAAA,MAAA,WAAA,GAA1B6T,WAAkL;MAAnIC,KAAI;MAAa,SAAK,CAAGE,KAAEC,GAAA,WAAA,GAAeD,KAAShU,SAAA;MAAIkU,SAAOC,SAAYjL;OAAU8K,KAAG/O,IAAA,WAAA,GAAA;MAAe,mBAAgB;;qCAEzKmQ,gBAQK,OARLvB,WAQK;IARC,SAAOG,KAAEC,GAAA,UAAA;KAAsBD,KAAG/O,IAAA,UAAA,CAAA,GAAA,CACxB+O,KAAOjU,UAAnB6U,WAGMZ,KAAAa,QAAA,eAAA;;IAHmC,SAAA,eAAOb,KAAEC,GAAA,aAAA,CAAA;KAAlD,WAAA;AAAA,WAGM,CAFUD,KAAW5T,eAAvBuT,UAAA,GAAAC,mBAA8H,QAA9HC,WAA8H;;MAApG,SAAK,CAAGG,KAAEC,GAAA,aAAA,GAAA,WAA4BD,KAAW5T,WAAA;MAAG,eAAY;OAAe4T,KAAG/O,IAAA,aAAA,CAAA,GAAA,MAAA,EAAA,MAC5G0O,UAAA,GAAAuB,YAAoGG,wBAApGxB,WAAoG;;MAA/E,SAAOG,KAAEC,GAAA,aAAA;MAAiBqB,MAAA;MAAK,eAAY;OAAetB,KAAG/O,IAAA,aAAA,CAAA,GAAA,MAAA,IAAA,CAAA,OAAA,CAAA,EAAA;OAEtF2P,WAEMZ,KAAAa,QAAA,gBAAA;;IAF4B,SAAA,eAAOb,KAAEC,GAAA,cAAA,CAAA;KAA3C,WAAA;AAAA,WAEM,EAAA,UAAA,GADFiB,YAAwJC,wBAAxInB,KAAa9T,eAAA,SAAA,iBAAA,GAA7B2T,WAAwJ;MAA5F,SAAK,CAAGG,KAAEC,GAAA,cAAA,GAAkBD,KAAY9T,YAAA;MAAG,eAAY;OAAe8T,KAAG/O,IAAA,cAAA,CAAA,GAAA,MAAA,IAAA,CAAA,OAAA,CAAA,EAAA;YAG7IsQ,YA6HQC,mBAAA;IA7HC1V,UAAUkU,KAAQlU;EAAA,GAAA;uBACvB,WAAA;AAAA,aA2HY,CA3HZyV,YA2HYE,YA3HZ5B,WA2HY;QA3HAlW,MAAK;QAAuB+X,SAAOvB,SAAcxI;QAAGgK,cAAaxB,SAAmB5H;QAAGqJ,SAAOzB,SAAcxH;QAAGkJ,cAAa1B,SAAmBpH;SAAUiH,KAAG/O,IAAA,YAAA,CAAA,GAAA;2BACpK,WAAA;AAAA,iBAyHK,CAzHM8O,MAAc9Q,kBAAzB0Q,UAAA,GAAAC,mBAyHK,OAzHLC,WAyHK;;YAzHuBC,KAAKK,SAAU1D;YAAG,SAAQ,CAAAuD,KAAAC,GAAe,SAAA,GAAAD,KAAAtU,YAAYsU,KAAYpU,YAAA;YAAI2B,OAAK,CAAGyS,KAAUnU,YAAEmU,KAAYrU,YAAA;YAAIuU,SAAK,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;qBAAEC,SAAc7J,kBAAA6J,SAAA7J,eAAA8J,MAAAD,UAAAxK,SAAA;YAAA;YAAG8K,WAAO,OAAA,EAAA,MAAA,OAAA,EAAA,IAAA,WAAA;qBAAEN,SAAgBzJ,oBAAAyJ,SAAAzJ,iBAAA0J,MAAAD,UAAAxK,SAAA;;aAAUqK,KAAG/O,IAAA,SAAA,CAAA,GAAA,CAC/LmQ,gBAUO,QAVPvB,WAUO;YATHC,KAAI;YACJS,MAAK;YACL,eAAY;YACZ,SAAM;YACLpT,UAAU;YACVyF,SAAK,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;qBAAEuN,SAAkBhL,sBAAAgL,SAAAhL,mBAAAiL,MAAAD,UAAAxK,SAAA;;aAClBqK,KAAG/O,IAAA,wBAAA,GAAA;YACV,4BAA0B;YAC1B,2BAAyB;yBAE9B2P,WAAqEZ,KAAAa,QAAA,UAAA;YAAhDrM,OAAOwL,KAAOrF;YAAG7Q,SAASqW,SAAcrO;cAClDkO,KAAMvV,UAAjBkV,UAAA,GAAAC,mBA+BK,OA/BLC,WA+BK;;YA/Be,SAAOG,KAAEC,GAAA,QAAA;aAAoBD,KAAG/O,IAAA,QAAA,CAAA,GAAA,CAChDsQ,YA0BWO,sBAAA;YA1BCC,UAAU/B,KAAQ+B;YAAGC,IAAIhC,KAAG/O,IAAA,mBAAA;;+BACpC,WAAA;AAAA,qBAkBC,CAlBDsQ,YAkBCU,sBAAA;gBAjBGnC,KAAI;gBACJtV,MAAK;gBACJgK,OAAOuL,MAAW/Q;gBAClBkT,gBAAa/B,SAAe/J;gBAC5B+L,gBAAahC,SAAe/J;gBAC5B,SAAA,eAAO4J,KAAEC,GAAA,UAAA,CAAA;gBACTjV,aAAagV,KAAiBrV;gBAC9ByX,SAASpC,KAAOoC;gBAChBL,UAAU/B,KAAQ+B;gBACnBxB,MAAK;gBACLD,cAAa;gBACZ,aAAWP,MAACpR,KAAA;gBACZ,yBAAuBwR,SAAexE;gBACtC8E,WAASN,SAAelK;gBACxBnD,QAAMqN,SAAYhK;gBAClBuK,SAAOP,SAAcrK;gBACrBkM,IAAIhC,KAAG/O,IAAA,UAAA;uMAEZsQ,YAKWc,sBAAA;gBALCN,UAAU/B,KAAQ+B;gBAAGC,IAAIhC,KAAG/O,IAAA,uBAAA;;mCACpC,WAAA;AAAA,yBAGM,CAHN2P,WAGMZ,KAAAA,QAAAA,cAAAA,CAAAA,GAHN,WAAA;AAAA,2BAGM,CAFUA,KAAU7T,cAAtBwT,UAAA,GAAAC,mBAAwE,QAAxEC,WAAwE;;sBAA/C,SAAOG,KAAU7T;uBAAU6T,KAAG/O,IAAA,YAAA,CAAA,GAAA,MAAA,EAAA,MACvD0O,UAAA,GAAAuB,YAA+CoB,uBAAAA,eAAAA,WAAAA;;uBAApBtC,KAAG/O,IAAA,YAAA,CAAA,CAAA,GAAA,MAAA,EAAA,EAAA;;;;;;;qCAI1CmQ,gBAEM,QAFNvB,WAEM;YAFAU,MAAK;YAAS,aAAU;YAAS,SAAM;UAA8B,GAAAP,KAAA/O,IAA4B,oBAAA,GAAA;YAAA,4BAA0B;8BAC1HkP,SAAsBrC,uBAAA,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,mBAAA,IAAA,IAAA,GAGjCsD,gBAyDK,OAzDLvB,WAyDK;YAzDC,SAAOG,KAAEC,GAAA,eAAA;YAAoB1S,OAAK;cAAA,cAAkB4S,SAAsB3P,0BAAIwP,KAAazV,eAAA;YAAA;aAAgByV,KAAG/O,IAAA,eAAA,CAAA,GAAA,CAChHsQ,YAuDiBgB,4BAvDjB1C,WAuDiB;YAvDCC,KAAKK,SAAAvD;aAA4BoD,KAAsBzT,wBAAA;YAAGiW,OAAOrC,SAAcrO;YAAGvE,OAAK;sBAAYyS,KAAazV;;YAAI4C,UAAU;YAAKkE,UAAU8O,SAAuB3P;YAAGwR,IAAIhC,KAAG/O,IAAA,iBAAA;;YAC3KwR,SACbC,QAAA,SAAAC,MAAA;AAAA,kBADwBC,aAAUD,KAAVC,YAAYjG,cAAUgG,KAAVhG,YAAY6F,SAAKG,KAALH,OAAOK,iBAAcF,KAAdE,gBAAgBC,eAAYH,KAAZG,cAAcC,YAAOJ,KAAPI;AAAO,qBAAA,CAC5F3B,gBAgDI,MAhDJvB,WAgDI;gBAhDCC,KAAG,SAAHA,IAAMlI,IAAE;AAAA,yBAAKuI,SAAAA,QAAQvI,IAAI+E,WAAU;gBAAA;gBAAIhO,IAAIoR,MAACpR,KAAA;gBAAc,SAAK,CAAGqR,KAAEC,GAAA,MAAA,GAAU2C,UAAU;gBAAIrV,OAAOuV;gBAAcvC,MAAK;iBAAkBP,KAAG/O,IAAA,MAAA,CAAA,GAAA,EAC5I0O,UAAA,IAAA,GAAAC,mBAwCUoD,UAxCsB,MAAAC,WAAAT,QAAd,SAAA9R,SAAQwS,GAAC;;kBAAkBlS,KAAAmP,SAAAtP,mBAAmBH,SAAQyP,SAAAA,eAAe+C,GAAGL,cAAc,CAAA;oBAE1F1C,SAAA5O,cAAcb,OAAM,KAD9BiP,UAAA,GAAAC,mBAWI,MAXJC,WAWI;;kBATClR,IAAIoR,MAACpR,KAAA,MAAUwR,SAAAA,eAAe+C,GAAGL,cAAc;kBAC/CtV,OAAiB;oBAAA4V,QAAAJ,YAAWA,YAAO,OAAW9W;;kBAC9C,SAAO+T,KAAEC,GAAA,aAAA;kBACVM,MAAK;;mBACGP,KAAG/O,IAAA,aAAA,CAAA,GAAA,CAEX2P,WAEMZ,KAAAa,QAAA,eAAA;kBAFoBnQ,QAAQA,QAAOc;kBAAclB,OAAO6P,SAAA9P,eAAe6S,GAAGL,cAAc;mBAA9F,WAAA;AAAA,yBAEM,CADFzB,gBAA0H,QAA1HvB,WAA0H;oBAAnH,SAAOG,KAAEC,GAAA,kBAAA;;qBAA8BD,KAAA/O,IAA4B,kBAAA,CAAA,GAAA+P,gBAAAb,SAAAzO,oBAAoBhB,QAAOc,WAAW,CAAA,GAAA,EAAA,CAAA;wCAGxH4R,gBAAAzD,UAAA,GAAAC,mBA0BI,MA1BJC,WA0BI;;kBAxBClR,IAAIoR,MAACpR,KAAA,MAAUwR,SAAAA,eAAe+C,GAAGL,cAAc;kBAE/C,SAAO7C,KAAAA,GAAe,UAAA;oBAAAtP,QAAAA;mCAAuByP,SAAc9P,eAAC6S,GAAGL,cAAc;kBAAA,CAAA;kBAC7EtV,OAAiB;oBAAA4V,QAAAJ,YAAWA,YAAO,OAAW9W;;kBAC/CsU,MAAK;kBACJ,cAAYJ,SAAc1P,eAACC,OAAM;kBACjC,iBAAeyP,SAAU/O,WAACV,OAAM;kBAChC,iBAAeyP,SAAgB7O,iBAACZ,OAAM;kBACtC,gBAAcyP,SAAW1B;kBACzB,iBAAe0B,SAAevO,gBAACuO,SAAAA,eAAe+C,GAAGL,cAAc,CAAA;kBAC/D3C,SAAO,SAAPA,QAAOmD,QAAA;AAAA,2BAAAlD,SAAA1K,eAAe4N,QAAQ3S,OAAM;;kBACpC4S,aAAS,SAATA,YAASD,QAAA;AAAA,2BAAElD,SAAAA,kBAAkBkD,QAAQlD,SAAc9P,eAAC6S,GAAGL,cAAc,CAAA;;kBACrE,mBAAiB1C,SAAU/O,WAACV,OAAM;kBAClC,kBAAgBqP,MAAiBhR,uBAAMoR,SAAAA,eAAe+C,GAAGL,cAAc;kBACvE,mBAAiB1C,SAAgB7O,iBAACZ,OAAM;;mBACjCyP,SAAArP,iBAAiBJ,SAAQmS,gBAAgBK,GAAC,QAAA,CAAA,GAAA,CAElClD,KAASnT,aAAA,UAAA,GAAzB+S,mBAGUoD,UAAA;kBAAAhS,KAAA;gBAAA,GAAA,CAFWmP,SAAA/O,WAAWV,OAAM,KAAlCiP,UAAA,GAAAuB,YAAqGqC,sBAArG1D,WAAqG;;kBAA/D,SAAOG,KAAEC,GAAA,iBAAA;;mBAA6BD,KAAG/O,IAAA,iBAAA,CAAA,GAAA,MAAA,IAAA,CAAA,OAAA,CAAA,MAC/E0O,UAAA,GAAAuB,YAAkFsC,sBAAlF3D,WAAkF;;kBAA/D,SAAOG,KAAEC,GAAA,iBAAA;;mBAA6BD,KAAG/O,IAAA,iBAAA,CAAA,GAAA,MAAA,IAAA,CAAA,OAAA,CAAA,EAAA,GAAA,EAAA,KAAA,mBAAA,IAAA,IAAA,GAEhE2P,WAEMZ,KAAAa,QAAA,UAAA;kBAFenQ,QAAQA;kBAASS,UAAUgP,SAAU/O,WAACV,OAAM;kBAAIJ,OAAO6P,SAAA9P,eAAe6S,GAAGL,cAAc;mBAA5G,WAAA;AAAA,yBAEM,CADFzB,gBAA+F,QAA/FvB,WAA+F;oBAAxF,SAAOG,KAAEC,GAAA,aAAA;;qBAAyBD,KAAG/O,IAAA,aAAA,CAAA,GAAA+P,gBAAoBb,SAAc1P,eAACC,OAAM,CAAA,GAAA,EAAA,CAAA;;yBAIvFqP,MAAA/Q,gBAAY,CAAKwT,UAAUA,UAASA,OAAMxQ,WAAK,MAAzD2N,UAAA,GAAAC,mBAEI,MAFJC,WAEI;;gBAFiE,SAAOG,KAAEC,GAAA,cAAA;gBAAkBM,MAAK;iBAAiBP,KAAG/O,IAAA,cAAA,GAAA;gBAAmB,4BAA0B;cAAI,CAAA,GAAA,CACtK2P,WAA2DZ,KAAAA,QAAAA,eAAAA,CAAAA,GAA3D,WAAA;AAAA,uBAA2D,CAAA,gBAAA,gBAA/BG,SAAuBlC,sBAAA,GAAA,CAAA,CAAA;0BAEvC,CAAA+B,KAAAlW,WAAYkW,KAAAlW,WAAWkW,KAAAlW,QAAQkI,WAAO,KAAtD2N,UAAA,GAAAC,mBAEI,MAFJC,WAEI;;gBAF2D,SAAOG,KAAEC,GAAA,cAAA;gBAAkBM,MAAK;iBAAiBP,KAAG/O,IAAA,cAAA,GAAA;gBAAmB,4BAA0B;cAAI,CAAA,GAAA,CAChK2P,WAA+CZ,KAAAA,QAAAA,SAAAA,CAAAA,GAA/C,WAAA;AAAA,uBAA+C,CAAA,gBAAA,gBAAzBG,SAAe9B,gBAAA,GAAA,CAAA,CAAA;;;;cAIjC2B,KAAAa,OAAO4C,SAAM;kBAAS;YAClClT,IAAAmS,QAAA,SAAAgB,OAAA;AAAA,kBAD4C5Z,WAAQ4Z,MAAR5Z;AAAQ,qBAAA,CACpD8W,WAA6CZ,KAAAa,QAAA,UAAA;gBAAxB/W,SAASA;cAAO,CAAA,CAAA;;;2EAIjD8W,WAAqEZ,KAAAa,QAAA,UAAA;YAAhDrM,OAAOwL,KAAOrF;YAAG7Q,SAASqW,SAAcrO;cAChD,CAAAkO,KAAAlW,WAAYkW,KAAAlW,WAAWkW,KAAAlW,QAAQkI,WAAK,KAAjD2N,UAAA,GAAAC,mBAEM,QAFNC,WAEM;;YAFqDU,MAAK;YAAS,aAAU;YAAS,SAAM;UAA8B,GAAAP,KAAA/O,IAA4B,oBAAA,GAAA;YAAA,4BAA0B;8BAC/KkP,SAAAA,gBAAAA,GAAAA,EAAAA,KAAAA,mBAAAA,IAAAA,IAAAA,GAEPiB,gBAEM,QAFNvB,WAEM;YAFAU,MAAK;YAAS,aAAU;YAAS,SAAM;UAA8B,GAAAP,KAAA/O,IAA+B,uBAAA,GAAA;YAAA,4BAA0B;8BAC7HkP,SAAAA,mBAAAA,GAAAA,EAAAA,GAEPiB,gBAUO,QAVPvB,WAUO;YATHC,KAAI;YACJS,MAAK;YACL,eAAY;YACZ,SAAM;YACLpT,UAAU;YACVyF,SAAK,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;qBAAEuN,SAAiB5K,qBAAA4K,SAAA5K,kBAAA6K,MAAAD,UAAAxK,SAAA;;aACjBqK,KAAG/O,IAAA,uBAAA,GAAA;YACV,4BAA0B;YAC1B,2BAAyB;;;;;;;;;;;;ACpLlD,IAAA0S,WAAe;EACXC,MAAM;EACN,WAASC;EACTC,SAAO,SAAPA,WAAU;AACNC,YAAQC,KAAK,oDAAoD;EACrE;AACJ;", "names": ["script", "name", "BaseIcon", "_openBlock", "_createElementBlock", "_mergeProps", "width", "height", "viewBox", "fill", "xmlns", "_ctx", "pti", "_cache", "_createElementVNode", "script", "name", "BaseIcon", "_openBlock", "_createElementBlock", "_mergeProps", "width", "height", "viewBox", "fill", "xmlns", "_ctx", "pti", "_cache", "_createElementVNode", "d", "theme", "_ref", "dt", "concat", "classes", "root", "BaseStyle", "extend", "name", "name", "BaseComponent", "style", "IconFieldStyle", "provide", "$pcIconField", "$parentInstance", "script", "BaseIconField", "inheritAttrs", "_openBlock", "_createElementBlock", "_mergeProps", "_ctx", "cx", "ptmi", "_renderSlot", "$slots", "classes", "root", "BaseStyle", "extend", "name", "script$1", "name", "BaseComponent", "style", "InputIconStyle", "props", "provide", "$pcInputIcon", "$parentInstance", "script", "BaseInputIcon", "inheritAttrs", "computed", "containerClass", "cx", "_openBlock", "_createElementBlock", "_mergeProps", "$options", "_ctx", "ptmi", "_renderSlot", "$slots", "theme", "_ref", "dt", "concat", "css", "BaseStyle", "extend", "name", "script$1", "name", "BaseComponent", "props", "id", "type", "String", "style", "items", "Array", "itemSize", "Number", "scrollHeight", "scrollWidth", "orientation", "numToleratedItems", "delay", "resizeDelay", "lazy", "Boolean", "disabled", "loaderDisabled", "columns", "loading", "showSpacer", "<PERSON><PERSON><PERSON><PERSON>", "tabindex", "inline", "step", "appendOnly", "autoSize", "VirtualScrollerStyle", "provide", "$pcVirtualScroller", "$parentInstance", "beforeMount", "_this$$primevueConfig", "loadCSS", "nonce", "$primevueConfig", "csp", "script", "BaseVirtualScroller", "inheritAttrs", "emits", "data", "both", "isBoth", "first", "rows", "cols", "last", "page", "numItemsInViewport", "lastScrollPos", "top", "left", "d_numToleratedItems", "d_loading", "loaderArr", "spacerStyle", "contentStyle", "element", "content", "scrollTimeout", "resizeTimeout", "defaultWidth", "defaultHeight", "defaultContentWidth", "defaultContentHeight", "isRangeChanged", "lazyLoadState", "resizeListener", "initialized", "watch", "newValue", "oldValue", "length", "init", "calculateAutoSize", "mounted", "viewInit", "updated", "unmounted", "unbindResizeListener", "methods", "isVisible", "setContentEl", "bindResizeListener", "getWidth", "getHeight", "setSize", "calculateOptions", "setSpacerSize", "isVertical", "isHorizontal", "scrollTo", "options", "scrollToIndex", "index", "_this", "behavior", "arguments", "undefined", "horizontal", "valid", "every", "i", "_this$element", "_this$element$scrollT", "scrollTop", "_this$element$scrollL", "scrollLeft", "_this$calculateNumIte", "calculateNumItems", "contentPos", "getContentPosition", "calculateFirst", "_index", "_numT", "calculateCoord", "_first", "_size", "_cpos", "newFirst", "isScrollChanged", "scrollInView", "to", "_this2", "_this$getRenderedRang", "getRenderedRange", "viewport", "isToStart", "isToEnd", "pos", "calculateFirstInViewport", "_pos", "Math", "floor", "firstInViewport", "lastInViewport", "_this$element2", "scrollPos", "contentWidth", "offsetWidth", "contentHeight", "offsetHeight", "calculateNumItemsInViewport", "_contentSize", "_itemSize", "ceil", "calculateNumToleratedItems", "_numItems", "_this3", "_this$calculateNumIte2", "calculateLast", "_num", "_isCols", "getLast", "$emit", "from", "map", "Promise", "resolve", "then", "_this3$items", "min", "_this4", "vertical", "minHeight", "min<PERSON><PERSON><PERSON>", "position", "contain", "_ref", "width", "height", "_ref2", "_this$items", "isCols", "getComputedStyle", "parseFloat", "paddingLeft", "max", "right", "paddingRight", "paddingTop", "bottom", "paddingBottom", "x", "y", "_this5", "parentElement", "concat", "setProp", "_name", "_value", "_this6", "_objectSpread", "_defineProperty", "setContentPosition", "_this7", "calculateTranslateVal", "setTransform", "_x", "_y", "transform", "translateVal", "onScrollPositionChange", "event", "_this8", "target", "calculateScrollPos", "calculateCurrentIndex", "calculateTriggerIndex", "_currentIndex", "_last", "_isScrollDownOrRight", "_triggerIndex", "lastValue", "newLast", "newScrollPos", "isScrollDown", "isScrollRight", "currentIndex", "triggerIndex", "isScrollDownOrRight", "onScrollChange", "_this$onScrollPositio", "newState", "isPageChanged", "_this$items2", "_this$items3", "getPageByFirst", "isLazyStateChanged", "onScroll", "_this9", "clearTimeout", "_this$onScrollPositio2", "changed", "setTimeout", "onResize", "_this10", "_ref3", "isDiffWidth", "isDiffHeight", "reinit", "bind", "window", "addEventListener", "removeEventListener", "getOptions", "renderedIndex", "count", "even", "odd", "getLoaderOptions", "extOptions", "el", "findSingle", "elementRef", "contentRef", "computed", "containerClass", "contentClass", "loaderClass", "$slots", "loader", "loadedItems", "_this11", "slice", "item", "loadedRows", "loadedColumns", "components", "SpinnerIcon", "_ctx", "_openBlock", "_createElementBlock", "_mergeProps", "ref", "$options", "apply", "ptmi", "_renderSlot", "styleClass", "getItemOptions", "$data", "_createElementVNode", "ptm", "_Fragment", "_renderList", "key", "_", "numCols", "d_numItemsInViewport", "_createVNode", "_component_SpinnerIcon", "spin", "theme", "_ref", "dt", "concat", "classes", "root", "_ref2", "instance", "props", "state", "disabled", "$invalid", "$variant", "focused", "$filled", "overlayVisible", "$fluid", "size", "label", "_ref3", "editable", "placeholder", "$slots", "length", "clearIcon", "dropdown", "loadingicon", "dropdownIcon", "overlay", "header", "pc<PERSON><PERSON><PERSON>", "listContainer", "list", "optionGroup", "optionGroupLabel", "option", "_ref4", "focusedOption", "isSelected", "highlightOnSelect", "focusedOptionIndex", "isOptionDisabled", "optionLabel", "optionCheckIcon", "optionBlankIcon", "emptyMessage", "BaseStyle", "extend", "name", "script$1", "name", "BaseInput", "props", "options", "Array", "optionLabel", "String", "Function", "optionValue", "optionDisabled", "optionGroupLabel", "optionGroupChildren", "scrollHeight", "type", "filter", "Boolean", "filterPlaceholder", "filterLocale", "filterMatchMode", "filterFields", "editable", "placeholder", "dataKey", "showClear", "inputId", "inputClass", "Object", "inputStyle", "labelId", "labelClass", "labelStyle", "panelClass", "overlayStyle", "overlayClass", "panelStyle", "appendTo", "loading", "clearIcon", "undefined", "dropdownIcon", "filterIcon", "loadingIcon", "resetFilterOnHide", "resetFilterOnClear", "virtualScrollerOptions", "autoOptionFocus", "autoFilterFocus", "selectOnFocus", "focusOnHover", "highlightOnSelect", "checkmark", "filterMessage", "selectionMessage", "emptySelectionMessage", "emptyFilterMessage", "emptyMessage", "tabindex", "Number", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "style", "SelectStyle", "provide", "$pcSelect", "$parentInstance", "script", "BaseSelect", "inheritAttrs", "emits", "outsideClickListener", "<PERSON><PERSON><PERSON><PERSON>", "resizeListener", "labelClickListener", "overlay", "list", "virtualScroller", "searchTimeout", "searchValue", "isModelValueChanged", "data", "id", "$attrs", "clicked", "focused", "focusedOptionIndex", "filterValue", "overlayVisible", "watch", "$attrsId", "newValue", "UniqueComponentId", "modelValue", "autoUpdateModel", "mounted", "bindLabelClickListener", "updated", "scrollInView", "findSelectedOptionIndex", "beforeUnmount", "unbindOutsideClickListener", "unbindResizeListener", "unbindLabelClickListener", "destroy", "ZIndex", "clear", "methods", "getOptionIndex", "index", "fn", "virtualScrollerDisabled", "getOptionLabel", "option", "resolveFieldData", "getOptionValue", "getOptionRenderKey", "getPTItemOptions", "itemOptions", "key", "ptm", "context", "selected", "isSelected", "disabled", "isOptionDisabled", "isOptionGroup", "optionGroup", "group", "getOptionGroupLabel", "getOptionGroupChildren", "getAriaPosInset", "_this", "visibleOptions", "slice", "length", "show", "isFocus", "$emit", "findFirstFocusedOptionIndex", "focus", "$refs", "focusInput", "hide", "_this2", "_hide", "setTimeout", "onFocus", "event", "onBlur", "_this$formField$onBlu", "_this$formField", "formField", "call", "onKeyDown", "isAndroid", "preventDefault", "metaKey", "ctrl<PERSON>ey", "code", "onArrowDownKey", "onArrowUpKey", "onArrowLeftKey", "onHomeKey", "onEndKey", "onPageDownKey", "onPageUpKey", "onSpaceKey", "onEnterKey", "onEscapeKey", "onTabKey", "onBackspaceKey", "isPrintableCharacter", "searchOptions", "onEditableInput", "value", "target", "matched", "updateModel", "isNotEmpty", "onContainerClick", "tagName", "getAttribute", "closest", "contains", "onClearClick", "onFirstHiddenFocus", "focusableEl", "relatedTarget", "getFirstFocusableElement", "onLastHiddenFocus", "getLastFocusableElement", "onOptionSelect", "isHide", "arguments", "onOptionMouseMove", "changeFocusedOptionIndex", "onFilterChange", "originalEvent", "scrollToIndex", "onFilterKeyDown", "isComposing", "onFilterBlur", "onFilterUpdated", "alignOverlay", "onOverlayClick", "OverlayEventBus", "emit", "$el", "onOverlayKeyDown", "optionIndex", "findNextOptionIndex", "findFirstOptionIndex", "pressedInInputText", "altKey", "findPrevOptionIndex", "findLastOptionIndex", "findLastFocusedOptionIndex", "currentTarget", "shift<PERSON>ey", "setSelectionRange", "selectionStart", "len", "stopPropagation", "hasFocusableElements", "firstHiddenFocusableElementOnOverlay", "onOverlayEnter", "el", "_this3", "set", "$primevue", "config", "zIndex", "addStyle", "position", "top", "left", "filterInput", "onOverlayAfterEnter", "bindOutsideClickListener", "bindScrollListener", "bindResizeListener", "onOverlayLeave", "_this4", "unbindScrollListener", "$nextTick", "onOverlayAfterLeave", "relativePosition", "min<PERSON><PERSON><PERSON>", "getOuterWidth", "absolutePosition", "_this5", "document", "addEventListener", "removeEventListener", "_this6", "ConnectedOverlayScrollHandler", "container", "_this7", "isTouchDevice", "window", "_this8", "label", "querySelector", "concat", "isVisible", "getFocusableElements", "isOptionMatched", "_this$getOptionLabel", "isValidOption", "toLocaleLowerCase", "startsWith", "isValidSelectedOption", "equals", "d_value", "equalityKey", "_this9", "findIndex", "_this10", "findLastIndex", "_this11", "matchedOptionIndex", "_this12", "_this13", "$filled", "selectedIndex", "char", "_this14", "clearTimeout", "_this15", "focusedOptionId", "element", "findSingle", "scrollIntoView", "block", "inline", "writeValue", "flatOptions", "_this16", "reduce", "result", "push", "for<PERSON>ach", "o", "overlayRef", "listRef", "contentRef", "virtualScrollerRef", "computed", "_this17", "filteredOptions", "FilterService", "searchFields", "optionGroups", "filtered", "groupChildren", "filteredItems", "item", "includes", "_objectSpread", "_defineProperty", "_toConsumableArray", "hasSelectedOption", "selectedOptionIndex", "editableInputValue", "filterResultMessageText", "filterMessageText", "replaceAll", "emptyFilterMessageText", "locale", "searchMessage", "emptySearchMessage", "emptyMessageText", "selectionMessageText", "emptySelectionMessageText", "selectedMessageText", "ariaSetSize", "_this18", "isClearIconVisible", "directives", "ripple", "<PERSON><PERSON><PERSON>", "components", "InputText", "VirtualScroller", "Portal", "InputIcon", "IconField", "TimesIcon", "ChevronDownIcon", "SpinnerIcon", "SearchIcon", "CheckIcon", "BlankIcon", "_openBlock", "_createElementBlock", "_mergeProps", "ref", "$data", "_ctx", "cx", "onClick", "$options", "apply", "ptmi", "autocomplete", "role", "invalid", "onKeydown", "onInput", "_hoisted_2", "_renderSlot", "$slots", "_$options$label", "_createTextVNode", "_toDisplayString", "clearCallback", "_createBlock", "_resolveDynamicComponent", "_createElementVNode", "_component_SpinnerIcon", "spin", "_createVNode", "_component_Portal", "_Transition", "onEnter", "onAfterEnter", "onLeave", "onAfterLeave", "_component_IconField", "unstyled", "pt", "_component_InputText", "onVnodeMounted", "onVnodeUpdated", "variant", "_component_InputIcon", "_component_SearchIcon", "_component_VirtualScroller", "items", "content", "_withCtx", "_ref", "styleClass", "getItemOptions", "contentStyle", "itemSize", "_Fragment", "_renderList", "i", "height", "_withDirectives", "$event", "onMousemove", "_component_CheckIcon", "_component_BlankIcon", "loader", "_ref2", "script", "name", "Select", "mounted", "console", "warn"]}