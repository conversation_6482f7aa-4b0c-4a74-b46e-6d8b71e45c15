# 太湖登录功能部署指南

## 概述

本文档描述了如何配置和部署太湖OAuth2.0登录功能。

## 配置信息

根据您提供的太湖OAuth配置：

- **太湖认证服务器**: `https://tai.it.tencent.com/api/auth-center/oauth2`
- **Client ID**: `api-docs`
- **回调地址**: `https://api-docs.woa.com/taihu_callback`
- **授权范围**: `openid offline`

## 环境配置

### 1. 生产环境配置 (.env.production)

```bash
# 太湖认证配置
TAIHU_CLIENT_ID=api-docs
TAIHU_CLIENT_SECRET=WNEK28UBJHHMN0D81GMPBHAWWZ4GEL2U  # 需要从太湖管理后台获取
TAIHU_AUTH_URL=https://tai.it.tencent.com/api/auth-center/oauth2
TAIHU_REDIRECT_URI=https://api-docs.woa.com/taihu_callback

# 前端配置
FRONTEND_URL=https://api-docs.woa.com
```

### 2. 开发环境配置 (.env.development)

```bash
# 太湖认证配置
TAIHU_CLIENT_ID=api-docs
TAIHU_CLIENT_SECRET=WNEK28UBJHHMN0D81GMPBHAWWZ4GEL2U
TAIHU_AUTH_URL=https://tai.it.tencent.com/api/auth-center/oauth2
TAIHU_REDIRECT_URI=https://api-docs.woa.com/taihu_callback

# 前端配置
FRONTEND_URL=http://localhost:3001  # 开发环境前端地址
```

## 部署步骤

### 1. 获取太湖Client Secret

1. 登录太湖管理后台
2. 找到应用 `api-docs`
3. 获取 Client Secret
4. 更新环境配置文件中的 `TAIHU_CLIENT_SECRET`

### 2. 后端部署

确保后端服务器能够：
- 处理 `/taihu_callback` 路由
- 访问太湖认证服务器
- 重定向到前端应用

### 3. 前端部署

确保前端应用：
- 部署在 `https://api-docs.woa.com`
- 包含 `/auth/callback` 路由处理页面
- 能够处理OAuth回调参数

## 登录流程

1. **用户点击登录** → 跳转到太湖登录页面
   ```
   https://tai.it.tencent.com/api/auth-center/oauth2/authorize?
   client_id=api-docs&
   response_type=code&
   scope=openid+offline&
   state=redirect_path&
   redirect_uri=https://api-docs.woa.com/taihu_callback
   ```

2. **太湖认证** → 用户在太湖页面完成登录

3. **回调处理** → 太湖重定向到 `https://api-docs.woa.com/taihu_callback?code=xxx&state=xxx`

4. **Token交换** → 后端使用授权码换取访问令牌

5. **前端重定向** → 后端重定向到前端回调页面，携带token信息

6. **登录完成** → 前端保存token并跳转到目标页面

## 安全注意事项

1. **Client Secret保护**: 确保Client Secret不会泄露到前端代码中
2. **HTTPS强制**: 生产环境必须使用HTTPS
3. **CORS配置**: 正确配置CORS允许的源
4. **Token验证**: 每次API调用都验证token有效性

## 测试

### 本地测试

1. 启动后端服务 (端口8001)
2. 启动前端服务 (端口3001)
3. 访问 `http://localhost:3001/login`
4. 点击太湖登录按钮

### 生产测试

1. 访问 `https://api-docs.woa.com/login`
2. 点击太湖登录按钮
3. 完成太湖认证流程
4. 验证登录状态和用户信息

## 故障排除

### 常见问题

1. **回调地址不匹配**: 确保太湖配置的回调地址与代码中一致
2. **Client Secret错误**: 检查环境变量配置
3. **CORS错误**: 检查后端CORS配置
4. **Token验证失败**: 检查太湖API调用

### 日志检查

查看后端日志中的以下信息：
- Token交换请求和响应
- 用户信息获取结果
- 错误信息和堆栈跟踪

## 更新记录

- 2024-01-XX: 初始版本，支持太湖OAuth2.0登录
- 配置了正确的太湖服务器地址和Client ID
- 实现了完整的OAuth流程和用户状态管理
