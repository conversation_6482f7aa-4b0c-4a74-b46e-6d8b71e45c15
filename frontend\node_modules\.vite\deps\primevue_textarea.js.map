{"version": 3, "sources": ["../../src/textarea/style/TextareaStyle.js", "../../src/textarea/BaseTextarea.vue", "../../src/textarea/Textarea.vue", "../../src/textarea/Textarea.vue?vue&type=template&id=1950f9e4&lang.js"], "sourcesContent": ["import BaseStyle from '@primevue/core/base/style';\n\nconst theme = ({ dt }) => `\n.p-textarea {\n    font-family: inherit;\n    font-feature-settings: inherit;\n    font-size: 1rem;\n    color: ${dt('textarea.color')};\n    background: ${dt('textarea.background')};\n    padding-block: ${dt('textarea.padding.y')};\n    padding-inline: ${dt('textarea.padding.x')};\n    border: 1px solid ${dt('textarea.border.color')};\n    transition: background ${dt('textarea.transition.duration')}, color ${dt('textarea.transition.duration')}, border-color ${dt('textarea.transition.duration')}, outline-color ${dt('textarea.transition.duration')}, box-shadow ${dt(\n    'textarea.transition.duration'\n)};\n    appearance: none;\n    border-radius: ${dt('textarea.border.radius')};\n    outline-color: transparent;\n    box-shadow: ${dt('textarea.shadow')};\n}\n\n.p-textarea:enabled:hover {\n    border-color: ${dt('textarea.hover.border.color')};\n}\n\n.p-textarea:enabled:focus {\n    border-color: ${dt('textarea.focus.border.color')};\n    box-shadow: ${dt('textarea.focus.ring.shadow')};\n    outline: ${dt('textarea.focus.ring.width')} ${dt('textarea.focus.ring.style')} ${dt('textarea.focus.ring.color')};\n    outline-offset: ${dt('textarea.focus.ring.offset')};\n}\n\n.p-textarea.p-invalid {\n    border-color: ${dt('textarea.invalid.border.color')};\n}\n\n.p-textarea.p-variant-filled {\n    background: ${dt('textarea.filled.background')};\n}\n\n.p-textarea.p-variant-filled:enabled:focus {\n    background: ${dt('textarea.filled.focus.background')};\n}\n\n.p-textarea:disabled {\n    opacity: 1;\n    background: ${dt('textarea.disabled.background')};\n    color: ${dt('textarea.disabled.color')};\n}\n\n.p-textarea::placeholder {\n    color: ${dt('textarea.placeholder.color')};\n}\n\n.p-textarea.p-invalid::placeholder {\n    color: ${dt('textarea.invalid.placeholder.color')};\n}\n\n.p-textarea-fluid {\n    width: 100%;\n}\n\n.p-textarea-resizable {\n    overflow: hidden;\n    resize: none;\n}\n\n.p-textarea-sm {\n    font-size: ${dt('textarea.sm.font.size')};\n    padding-block: ${dt('textarea.sm.padding.y')};\n    padding-inline: ${dt('textarea.sm.padding.x')};\n}\n\n.p-textarea-lg {\n    font-size: ${dt('textarea.lg.font.size')};\n    padding-block: ${dt('textarea.lg.padding.y')};\n    padding-inline: ${dt('textarea.lg.padding.x')};\n}\n`;\n\nconst classes = {\n    root: ({ instance, props }) => [\n        'p-textarea p-component',\n        {\n            'p-filled': instance.$filled,\n            'p-textarea-resizable ': props.autoResize,\n            'p-textarea-sm p-inputfield-sm': props.size === 'small',\n            'p-textarea-lg p-inputfield-lg': props.size === 'large',\n            'p-invalid': instance.$invalid,\n            'p-variant-filled': instance.$variant === 'filled',\n            'p-textarea-fluid': instance.$fluid\n        }\n    ]\n};\n\nexport default BaseStyle.extend({\n    name: 'textarea',\n    theme,\n    classes\n});\n", "<script>\nimport BaseInput from '@primevue/core/baseinput';\nimport TextareaStyle from 'primevue/textarea/style';\n\nexport default {\n    name: 'BaseTextarea',\n    extends: BaseInput,\n    props: {\n        autoResize: Boolean\n    },\n    style: TextareaStyle,\n    provide() {\n        return {\n            $pcTextarea: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <textarea :class=\"cx('root')\" :value=\"d_value\" :disabled=\"disabled\" :aria-invalid=\"invalid || undefined\" @input=\"onInput\" v-bind=\"attrs\"></textarea>\n</template>\n\n<script>\nimport { mergeProps } from 'vue';\nimport BaseTextarea from './BaseTextarea.vue';\n\nexport default {\n    name: 'Textarea',\n    extends: BaseTextarea,\n    inheritAttrs: false,\n    observer: null,\n    mounted() {\n        if (this.autoResize) {\n            this.observer = new ResizeObserver(() => {\n                this.resize();\n            });\n            this.observer.observe(this.$el);\n        }\n    },\n    updated() {\n        if (this.autoResize) {\n            this.resize();\n        }\n    },\n    beforeUnmount() {\n        if (this.observer) {\n            this.observer.disconnect();\n        }\n    },\n    methods: {\n        resize() {\n            if (!this.$el.offsetParent) return;\n\n            this.$el.style.height = 'auto';\n            this.$el.style.height = this.$el.scrollHeight + 'px';\n\n            if (parseFloat(this.$el.style.height) >= parseFloat(this.$el.style.maxHeight)) {\n                this.$el.style.overflowY = 'scroll';\n                this.$el.style.height = this.$el.style.maxHeight;\n            } else {\n                this.$el.style.overflow = 'hidden';\n            }\n        },\n        onInput(event) {\n            if (this.autoResize) {\n                this.resize();\n            }\n\n            this.writeValue(event.target.value, event);\n        }\n    },\n    computed: {\n        attrs() {\n            return mergeProps(\n                this.ptmi('root', {\n                    context: {\n                        filled: this.$filled,\n                        disabled: this.disabled\n                    }\n                }),\n                this.formField\n            );\n        }\n    }\n};\n</script>\n", "<template>\n    <textarea :class=\"cx('root')\" :value=\"d_value\" :disabled=\"disabled\" :aria-invalid=\"invalid || undefined\" @input=\"onInput\" v-bind=\"attrs\"></textarea>\n</template>\n\n<script>\nimport { mergeProps } from 'vue';\nimport BaseTextarea from './BaseTextarea.vue';\n\nexport default {\n    name: 'Textarea',\n    extends: BaseTextarea,\n    inheritAttrs: false,\n    observer: null,\n    mounted() {\n        if (this.autoResize) {\n            this.observer = new ResizeObserver(() => {\n                this.resize();\n            });\n            this.observer.observe(this.$el);\n        }\n    },\n    updated() {\n        if (this.autoResize) {\n            this.resize();\n        }\n    },\n    beforeUnmount() {\n        if (this.observer) {\n            this.observer.disconnect();\n        }\n    },\n    methods: {\n        resize() {\n            if (!this.$el.offsetParent) return;\n\n            this.$el.style.height = 'auto';\n            this.$el.style.height = this.$el.scrollHeight + 'px';\n\n            if (parseFloat(this.$el.style.height) >= parseFloat(this.$el.style.maxHeight)) {\n                this.$el.style.overflowY = 'scroll';\n                this.$el.style.height = this.$el.style.maxHeight;\n            } else {\n                this.$el.style.overflow = 'hidden';\n            }\n        },\n        onInput(event) {\n            if (this.autoResize) {\n                this.resize();\n            }\n\n            this.writeValue(event.target.value, event);\n        }\n    },\n    computed: {\n        attrs() {\n            return mergeProps(\n                this.ptmi('root', {\n                    context: {\n                        filled: this.$filled,\n                        disabled: this.disabled\n                    }\n                }),\n                this.formField\n            );\n        }\n    }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAEA,IAAMA,QAAQ,SAARA,OAAKC,MAAA;AAAA,MAAMC,KAAED,KAAFC;AAAE,SAAAC,qHAAAA,OAKND,GAAG,gBAAgB,GAACC,qBAAAA,EAAAA,OACfD,GAAG,qBAAqB,GAACC,wBAAAA,EAAAA,OACtBD,GAAG,oBAAoB,GAACC,yBAAAA,EAAAA,OACvBD,GAAG,oBAAoB,GAACC,2BAAAA,EAAAA,OACtBD,GAAG,uBAAuB,GAACC,gCAAAA,EAAAA,OACtBD,GAAG,8BAA8B,GAACC,UAAAA,EAAAA,OAAWD,GAAG,8BAA8B,GAACC,iBAAAA,EAAAA,OAAkBD,GAAG,8BAA8B,GAACC,kBAAAA,EAAAA,OAAmBD,GAAG,8BAA8B,GAACC,eAAAA,EAAAA,OAAgBD,GACjO,8BACJ,GAACC,+CAAAA,EAAAA,OAEoBD,GAAG,wBAAwB,GAACC,sDAAAA,EAAAA,OAE/BD,GAAG,iBAAiB,GAACC,yDAAAA,EAAAA,OAInBD,GAAG,6BAA6B,GAACC,yDAAAA,EAAAA,OAIjCD,GAAG,6BAA6B,GAACC,qBAAAA,EAAAA,OACnCD,GAAG,4BAA4B,GAAC,kBAAA,EAAAC,OACnCD,GAAG,2BAA2B,GAAC,GAAA,EAAAC,OAAID,GAAG,2BAA2B,GAAC,GAAA,EAAAC,OAAID,GAAG,2BAA2B,GAAC,yBAAA,EAAAC,OAC9FD,GAAG,4BAA4B,GAAC,qDAAA,EAAAC,OAIlCD,GAAG,+BAA+B,GAAC,0DAAA,EAAAC,OAIrCD,GAAG,4BAA4B,GAAC,wEAAA,EAAAC,OAIhCD,GAAG,kCAAkC,GAAC,mEAAA,EAAAC,OAKtCD,GAAG,8BAA8B,GAAC,gBAAA,EAAAC,OACvCD,GAAG,yBAAyB,GAAC,iDAAA,EAAAC,OAI7BD,GAAG,4BAA4B,GAAC,2DAAA,EAAAC,OAIhCD,GAAG,oCAAoC,GAAC,+JAAA,EAAAC,OAapCD,GAAG,uBAAuB,GAAC,wBAAA,EAAAC,OACvBD,GAAG,uBAAuB,GAAC,yBAAA,EAAAC,OAC1BD,GAAG,uBAAuB,GAAC,2CAAA,EAAAC,OAIhCD,GAAG,uBAAuB,GAAC,wBAAA,EAAAC,OACvBD,GAAG,uBAAuB,GAACC,yBAAAA,EAAAA,OAC1BD,GAAG,uBAAuB,GAAC,QAAA;AAAA;AAIjD,IAAME,UAAU;EACZC,MAAM,SAANA,KAAIC,OAAA;AAAA,QAAKC,WAAQD,MAARC,UAAUC,QAAKF,MAALE;AAAK,WAAO,CAC3B,0BACA;MACI,YAAYD,SAASE;MACrB,yBAAyBD,MAAME;MAC/B,iCAAiCF,MAAMG,SAAS;MAChD,iCAAiCH,MAAMG,SAAS;MAChD,aAAaJ,SAASK;MACtB,oBAAoBL,SAASM,aAAa;MAC1C,oBAAoBN,SAASO;IACjC,CAAC;EACJ;AACL;AAEA,IAAA,gBAAeC,UAAUC,OAAO;EAC5BC,MAAM;EACNjB;EACAI;AACJ,CAAC;;;AC/FD,IAAA,WAAe;EACXc,MAAM;EACN,WAASC;EACTC,OAAO;IACHC,YAAYC;;EAEhBC,OAAOC;EACPC,SAAO,SAAPA,UAAU;AACN,WAAO;MACHC,aAAa;MACbC,iBAAiB;;EAEzB;AACJ;ACTA,IAAAC,UAAe;EACXV,MAAM;EACN,WAASW;EACTC,cAAc;EACdC,UAAU;EACVC,SAAO,SAAPA,UAAU;AAAA,QAAAC,QAAA;AACN,QAAI,KAAKZ,YAAY;AACjB,WAAKU,WAAW,IAAIG,eAAe,WAAM;AACrCD,cAAKE,OAAM;MACf,CAAC;AACD,WAAKJ,SAASK,QAAQ,KAAKC,GAAG;IAClC;;EAEJC,SAAO,SAAPA,UAAU;AACN,QAAI,KAAKjB,YAAY;AACjB,WAAKc,OAAM;IACf;;EAEJI,eAAa,SAAbA,gBAAgB;AACZ,QAAI,KAAKR,UAAU;AACf,WAAKA,SAASS,WAAU;IAC5B;;EAEJC,SAAS;IACLN,QAAM,SAANA,SAAS;AACL,UAAI,CAAC,KAAKE,IAAIK,aAAc;AAE5B,WAAKL,IAAId,MAAMoB,SAAS;AACxB,WAAKN,IAAId,MAAMoB,SAAS,KAAKN,IAAIO,eAAe;AAEhD,UAAIC,WAAW,KAAKR,IAAId,MAAMoB,MAAM,KAAKE,WAAW,KAAKR,IAAId,MAAMuB,SAAS,GAAG;AAC3E,aAAKT,IAAId,MAAMwB,YAAY;AAC3B,aAAKV,IAAId,MAAMoB,SAAS,KAAKN,IAAId,MAAMuB;MAC3C,OAAO;AACH,aAAKT,IAAId,MAAMyB,WAAW;MAC9B;;IAEJC,SAAAA,SAAAA,QAAQC,OAAO;AACX,UAAI,KAAK7B,YAAY;AACjB,aAAKc,OAAM;MACf;AAEA,WAAKgB,WAAWD,MAAME,OAAOC,OAAOH,KAAK;IAC7C;;EAEJI,UAAU;IACNC,OAAK,SAALA,QAAQ;AACJ,aAAOC,WACH,KAAKC,KAAK,QAAQ;QACdC,SAAS;UACLC,QAAQ,KAAKC;UACbC,UAAU,KAAKA;QACnB;MACJ,CAAC,GACD,KAAKC,SACT;IACJ;EACJ;AACJ;;;ACjEI,SAAAC,UAAA,GAAAC,mBAAmJ,YAAnJC,WAAmJ;IAAxI,SAAOC,KAAEC,GAAA,MAAA;IAAWd,OAAOa,KAAOE;IAAGP,UAAUK,KAAQL;IAAG,gBAAcK,KAAMG,WAAKC;IAAYrB,SAAK,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;aAAEsB,SAAOtB,WAAAsB,SAAAtB,QAAAuB,MAAAD,UAAAE,SAAA;;KAAUF,SAAKhB,KAAA,GAAA,MAAA,IAAAmB,UAAA;;;", "names": ["theme", "_ref", "dt", "concat", "classes", "root", "_ref2", "instance", "props", "$filled", "autoResize", "size", "$invalid", "$variant", "$fluid", "BaseStyle", "extend", "name", "name", "BaseInput", "props", "autoResize", "Boolean", "style", "TextareaStyle", "provide", "$pcTextarea", "$parentInstance", "script", "BaseTextarea", "inheritAttrs", "observer", "mounted", "_this", "ResizeObserver", "resize", "observe", "$el", "updated", "beforeUnmount", "disconnect", "methods", "offsetParent", "height", "scrollHeight", "parseFloat", "maxHeight", "overflowY", "overflow", "onInput", "event", "writeValue", "target", "value", "computed", "attrs", "mergeProps", "ptmi", "context", "filled", "$filled", "disabled", "formField", "_openBlock", "_createElementBlock", "_mergeProps", "_ctx", "cx", "d_value", "invalid", "undefined", "$options", "apply", "arguments", "_hoisted_1"]}