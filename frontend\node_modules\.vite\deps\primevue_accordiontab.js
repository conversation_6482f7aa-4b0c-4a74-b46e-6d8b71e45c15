import {
  script
} from "./chunk-BXQMK4YT.js";
import "./chunk-B73TEBOQ.js";
import {
  BaseStyle
} from "./chunk-M2QMAZBN.js";
import "./chunk-LQERBOIJ.js";
import {
  renderSlot
} from "./chunk-U3LI7FBV.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/primevue/accordiontab/style/index.mjs
var AccordionTabStyle = BaseStyle.extend({
  name: "accordiontab"
});

// node_modules/primevue/accordiontab/index.mjs
var script$1 = {
  name: "BaseAccordionTab",
  "extends": script,
  props: {
    header: null,
    headerStyle: null,
    headerClass: null,
    headerProps: null,
    headerActionProps: null,
    contentStyle: null,
    contentClass: null,
    contentProps: null,
    disabled: Boolean
  },
  style: AccordionTabStyle,
  provide: function provide() {
    return {
      $pcAccordionTab: this,
      $parentInstance: this
    };
  }
};
var script2 = {
  name: "AccordionTab",
  "extends": script$1,
  inheritAttrs: false,
  mounted: function mounted() {
    console.warn("Deprecated since v4. Use the new structure of Accordion instead.");
  }
};
function render(_ctx, _cache, $props, $setup, $data, $options) {
  return renderSlot(_ctx.$slots, "default");
}
script2.render = render;
export {
  script2 as default
};
//# sourceMappingURL=primevue_accordiontab.js.map
