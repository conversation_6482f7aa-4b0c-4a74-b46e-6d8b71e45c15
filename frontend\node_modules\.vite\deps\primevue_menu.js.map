{"version": 3, "sources": ["../../src/menu/style/MenuStyle.js", "../../src/menu/BaseMenu.vue", "../../src/menu/Menuitem.vue", "../../src/menu/Menuitem.vue?vue&type=template&id=e64d2dd2&lang.js", "../../src/menu/Menu.vue", "../../src/menu/Menu.vue?vue&type=template&id=068ae026&lang.js"], "sourcesContent": ["import BaseStyle from '@primevue/core/base/style';\n\nconst theme = ({ dt }) => `\n.p-menu {\n    background: ${dt('menu.background')};\n    color: ${dt('menu.color')};\n    border: 1px solid ${dt('menu.border.color')};\n    border-radius: ${dt('menu.border.radius')};\n    min-width: 12.5rem;\n}\n\n.p-menu-list {\n    margin: 0;\n    padding: ${dt('menu.list.padding')};\n    outline: 0 none;\n    list-style: none;\n    display: flex;\n    flex-direction: column;\n    gap: ${dt('menu.list.gap')};\n}\n\n.p-menu-item-content {\n    transition: background ${dt('menu.transition.duration')}, color ${dt('menu.transition.duration')};\n    border-radius: ${dt('menu.item.border.radius')};\n    color: ${dt('menu.item.color')};\n}\n\n.p-menu-item-link {\n    cursor: pointer;\n    display: flex;\n    align-items: center;\n    text-decoration: none;\n    overflow: hidden;\n    position: relative;\n    color: inherit;\n    padding: ${dt('menu.item.padding')};\n    gap: ${dt('menu.item.gap')};\n    user-select: none;\n    outline: 0 none;\n}\n\n.p-menu-item-label {\n    line-height: 1;\n}\n\n.p-menu-item-icon {\n    color: ${dt('menu.item.icon.color')};\n}\n\n.p-menu-item.p-focus .p-menu-item-content {\n    color: ${dt('menu.item.focus.color')};\n    background: ${dt('menu.item.focus.background')};\n}\n\n.p-menu-item.p-focus .p-menu-item-icon {\n    color: ${dt('menu.item.icon.focus.color')};\n}\n\n.p-menu-item:not(.p-disabled) .p-menu-item-content:hover {\n    color: ${dt('menu.item.focus.color')};\n    background: ${dt('menu.item.focus.background')};\n}\n\n.p-menu-item:not(.p-disabled) .p-menu-item-content:hover .p-menu-item-icon {\n    color: ${dt('menu.item.icon.focus.color')};\n}\n\n.p-menu-overlay {\n    box-shadow: ${dt('menu.shadow')};\n}\n\n.p-menu-submenu-label {\n    background: ${dt('menu.submenu.label.background')};\n    padding: ${dt('menu.submenu.label.padding')};\n    color: ${dt('menu.submenu.label.color')};\n    font-weight: ${dt('menu.submenu.label.font.weight')};\n}\n\n.p-menu-separator {\n    border-block-start: 1px solid ${dt('menu.separator.border.color')};\n}\n`;\n\nconst classes = {\n    root: ({ props }) => [\n        'p-menu p-component',\n        {\n            'p-menu-overlay': props.popup\n        }\n    ],\n    start: 'p-menu-start',\n    list: 'p-menu-list',\n    submenuLabel: 'p-menu-submenu-label',\n    separator: 'p-menu-separator',\n    end: 'p-menu-end',\n    item: ({ instance }) => [\n        'p-menu-item',\n        {\n            'p-focus': instance.id === instance.focusedOptionId,\n            'p-disabled': instance.disabled()\n        }\n    ],\n    itemContent: 'p-menu-item-content',\n    itemLink: 'p-menu-item-link',\n    itemIcon: 'p-menu-item-icon',\n    itemLabel: 'p-menu-item-label'\n};\n\nexport default BaseStyle.extend({\n    name: 'menu',\n    theme,\n    classes\n});\n", "<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport MenuStyle from 'primevue/menu/style';\n\nexport default {\n    name: 'BaseMenu',\n    extends: BaseComponent,\n    props: {\n        popup: {\n            type: Boolean,\n            default: false\n        },\n        model: {\n            type: Array,\n            default: null\n        },\n        appendTo: {\n            type: [String, Object],\n            default: 'body'\n        },\n        autoZIndex: {\n            type: Boolean,\n            default: true\n        },\n        baseZIndex: {\n            type: Number,\n            default: 0\n        },\n        tabindex: {\n            type: Number,\n            default: 0\n        },\n        ariaLabel: {\n            type: String,\n            default: null\n        },\n        ariaLabelledby: {\n            type: String,\n            default: null\n        }\n    },\n    style: MenuStyle,\n    provide() {\n        return {\n            $pcMenu: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <li\n        v-if=\"visible()\"\n        :id=\"id\"\n        :class=\"[cx('item'), item.class]\"\n        role=\"menuitem\"\n        :style=\"item.style\"\n        :aria-label=\"label()\"\n        :aria-disabled=\"disabled()\"\n        v-bind=\"getPTOptions('item')\"\n        :data-p-focused=\"isItemFocused()\"\n        :data-p-disabled=\"disabled() || false\"\n    >\n        <div :class=\"cx('itemContent')\" @click=\"onItemClick($event)\" @mousemove=\"onItemMouseMove($event)\" v-bind=\"getPTOptions('itemContent')\">\n            <template v-if=\"!templates.item\">\n                <a v-ripple :href=\"item.url\" :class=\"cx('itemLink')\" :target=\"item.target\" tabindex=\"-1\" v-bind=\"getPTOptions('itemLink')\">\n                    <component v-if=\"templates.itemicon\" :is=\"templates.itemicon\" :item=\"item\" :class=\"cx('itemIcon')\" />\n                    <span v-else-if=\"item.icon\" :class=\"[cx('itemIcon'), item.icon]\" v-bind=\"getPTOptions('itemIcon')\" />\n                    <span :class=\"cx('itemLabel')\" v-bind=\"getPTOptions('itemLabel')\">{{ label() }}</span>\n                </a>\n            </template>\n            <component v-else-if=\"templates.item\" :is=\"templates.item\" :item=\"item\" :label=\"label()\" :props=\"getMenuItemProps(item)\"></component>\n        </div>\n    </li>\n</template>\n\n<script>\nimport { resolve } from '@primeuix/utils/object';\nimport BaseComponent from '@primevue/core/basecomponent';\nimport Ripple from 'primevue/ripple';\nimport { mergeProps } from 'vue';\n\nexport default {\n    name: 'Menuitem',\n    hostName: 'Menu',\n    extends: BaseComponent,\n    inheritAttrs: false,\n    emits: ['item-click', 'item-mousemove'],\n    props: {\n        item: null,\n        templates: null,\n        id: null,\n        focusedOptionId: null,\n        index: null\n    },\n    methods: {\n        getItemProp(processedItem, name) {\n            return processedItem && processedItem.item ? resolve(processedItem.item[name]) : undefined;\n        },\n        getPTOptions(key) {\n            return this.ptm(key, {\n                context: {\n                    item: this.item,\n                    index: this.index,\n                    focused: this.isItemFocused(),\n                    disabled: this.disabled()\n                }\n            });\n        },\n        isItemFocused() {\n            return this.focusedOptionId === this.id;\n        },\n        onItemClick(event) {\n            const command = this.getItemProp(this.item, 'command');\n\n            command && command({ originalEvent: event, item: this.item.item });\n            this.$emit('item-click', { originalEvent: event, item: this.item, id: this.id });\n        },\n        onItemMouseMove(event) {\n            this.$emit('item-mousemove', { originalEvent: event, item: this.item, id: this.id });\n        },\n        visible() {\n            return typeof this.item.visible === 'function' ? this.item.visible() : this.item.visible !== false;\n        },\n        disabled() {\n            return typeof this.item.disabled === 'function' ? this.item.disabled() : this.item.disabled;\n        },\n        label() {\n            return typeof this.item.label === 'function' ? this.item.label() : this.item.label;\n        },\n        getMenuItemProps(item) {\n            return {\n                action: mergeProps(\n                    {\n                        class: this.cx('itemLink'),\n                        tabindex: '-1'\n                    },\n                    this.getPTOptions('itemLink')\n                ),\n                icon: mergeProps(\n                    {\n                        class: [this.cx('itemIcon'), item.icon]\n                    },\n                    this.getPTOptions('itemIcon')\n                ),\n                label: mergeProps(\n                    {\n                        class: this.cx('itemLabel')\n                    },\n                    this.getPTOptions('itemLabel')\n                )\n            };\n        }\n    },\n    directives: {\n        ripple: Ripple\n    }\n};\n</script>\n", "<template>\n    <li\n        v-if=\"visible()\"\n        :id=\"id\"\n        :class=\"[cx('item'), item.class]\"\n        role=\"menuitem\"\n        :style=\"item.style\"\n        :aria-label=\"label()\"\n        :aria-disabled=\"disabled()\"\n        v-bind=\"getPTOptions('item')\"\n        :data-p-focused=\"isItemFocused()\"\n        :data-p-disabled=\"disabled() || false\"\n    >\n        <div :class=\"cx('itemContent')\" @click=\"onItemClick($event)\" @mousemove=\"onItemMouseMove($event)\" v-bind=\"getPTOptions('itemContent')\">\n            <template v-if=\"!templates.item\">\n                <a v-ripple :href=\"item.url\" :class=\"cx('itemLink')\" :target=\"item.target\" tabindex=\"-1\" v-bind=\"getPTOptions('itemLink')\">\n                    <component v-if=\"templates.itemicon\" :is=\"templates.itemicon\" :item=\"item\" :class=\"cx('itemIcon')\" />\n                    <span v-else-if=\"item.icon\" :class=\"[cx('itemIcon'), item.icon]\" v-bind=\"getPTOptions('itemIcon')\" />\n                    <span :class=\"cx('itemLabel')\" v-bind=\"getPTOptions('itemLabel')\">{{ label() }}</span>\n                </a>\n            </template>\n            <component v-else-if=\"templates.item\" :is=\"templates.item\" :item=\"item\" :label=\"label()\" :props=\"getMenuItemProps(item)\"></component>\n        </div>\n    </li>\n</template>\n\n<script>\nimport { resolve } from '@primeuix/utils/object';\nimport BaseComponent from '@primevue/core/basecomponent';\nimport Ripple from 'primevue/ripple';\nimport { mergeProps } from 'vue';\n\nexport default {\n    name: 'Menuitem',\n    hostName: 'Menu',\n    extends: BaseComponent,\n    inheritAttrs: false,\n    emits: ['item-click', 'item-mousemove'],\n    props: {\n        item: null,\n        templates: null,\n        id: null,\n        focusedOptionId: null,\n        index: null\n    },\n    methods: {\n        getItemProp(processedItem, name) {\n            return processedItem && processedItem.item ? resolve(processedItem.item[name]) : undefined;\n        },\n        getPTOptions(key) {\n            return this.ptm(key, {\n                context: {\n                    item: this.item,\n                    index: this.index,\n                    focused: this.isItemFocused(),\n                    disabled: this.disabled()\n                }\n            });\n        },\n        isItemFocused() {\n            return this.focusedOptionId === this.id;\n        },\n        onItemClick(event) {\n            const command = this.getItemProp(this.item, 'command');\n\n            command && command({ originalEvent: event, item: this.item.item });\n            this.$emit('item-click', { originalEvent: event, item: this.item, id: this.id });\n        },\n        onItemMouseMove(event) {\n            this.$emit('item-mousemove', { originalEvent: event, item: this.item, id: this.id });\n        },\n        visible() {\n            return typeof this.item.visible === 'function' ? this.item.visible() : this.item.visible !== false;\n        },\n        disabled() {\n            return typeof this.item.disabled === 'function' ? this.item.disabled() : this.item.disabled;\n        },\n        label() {\n            return typeof this.item.label === 'function' ? this.item.label() : this.item.label;\n        },\n        getMenuItemProps(item) {\n            return {\n                action: mergeProps(\n                    {\n                        class: this.cx('itemLink'),\n                        tabindex: '-1'\n                    },\n                    this.getPTOptions('itemLink')\n                ),\n                icon: mergeProps(\n                    {\n                        class: [this.cx('itemIcon'), item.icon]\n                    },\n                    this.getPTOptions('itemIcon')\n                ),\n                label: mergeProps(\n                    {\n                        class: this.cx('itemLabel')\n                    },\n                    this.getPTOptions('itemLabel')\n                )\n            };\n        }\n    },\n    directives: {\n        ripple: Ripple\n    }\n};\n</script>\n", "<template>\n    <Portal :appendTo=\"appendTo\" :disabled=\"!popup\">\n        <transition name=\"p-connected-overlay\" @enter=\"onEnter\" @leave=\"onLeave\" @after-leave=\"onAfterLeave\" v-bind=\"ptm('transition')\">\n            <div v-if=\"popup ? overlayVisible : true\" :ref=\"containerRef\" :id=\"id\" :class=\"cx('root')\" @click=\"onOverlayClick\" v-bind=\"ptmi('root')\">\n                <div v-if=\"$slots.start\" :class=\"cx('start')\" v-bind=\"ptm('start')\">\n                    <slot name=\"start\"></slot>\n                </div>\n                <ul\n                    :ref=\"listRef\"\n                    :id=\"id + '_list'\"\n                    :class=\"cx('list')\"\n                    role=\"menu\"\n                    :tabindex=\"tabindex\"\n                    :aria-activedescendant=\"focused ? focusedOptionId : undefined\"\n                    :aria-label=\"ariaLabel\"\n                    :aria-labelledby=\"ariaLabelledby\"\n                    @focus=\"onListFocus\"\n                    @blur=\"onListBlur\"\n                    @keydown=\"onListKeyDown\"\n                    v-bind=\"ptm('list')\"\n                >\n                    <template v-for=\"(item, i) of model\" :key=\"label(item) + i.toString()\">\n                        <template v-if=\"item.items && visible(item) && !item.separator\">\n                            <li v-if=\"item.items\" :id=\"id + '_' + i\" :class=\"[cx('submenuLabel'), item.class]\" role=\"none\" v-bind=\"ptm('submenuLabel')\">\n                                <!--TODO: submenuheader deprecated since v4.0. Use submenulabel-->\n                                <slot :name=\"$slots.submenulabel ? 'submenulabel' : 'submenuheader'\" :item=\"item\">{{ label(item) }}</slot>\n                            </li>\n                            <template v-for=\"(child, j) of item.items\" :key=\"child.label + i + '_' + j\">\n                                <PVMenuitem\n                                    v-if=\"visible(child) && !child.separator\"\n                                    :id=\"id + '_' + i + '_' + j\"\n                                    :item=\"child\"\n                                    :templates=\"$slots\"\n                                    :focusedOptionId=\"focusedOptionId\"\n                                    :unstyled=\"unstyled\"\n                                    @item-click=\"itemClick\"\n                                    @item-mousemove=\"itemMouseMove\"\n                                    :pt=\"pt\"\n                                />\n                                <li v-else-if=\"visible(child) && child.separator\" :key=\"'separator' + i + j\" :class=\"[cx('separator'), item.class]\" :style=\"child.style\" role=\"separator\" v-bind=\"ptm('separator')\"></li>\n                            </template>\n                        </template>\n                        <li v-else-if=\"visible(item) && item.separator\" :key=\"'separator' + i.toString()\" :class=\"[cx('separator'), item.class]\" :style=\"item.style\" role=\"separator\" v-bind=\"ptm('separator')\"></li>\n                        <PVMenuitem\n                            v-else\n                            :key=\"label(item) + i.toString()\"\n                            :id=\"id + '_' + i\"\n                            :item=\"item\"\n                            :index=\"i\"\n                            :templates=\"$slots\"\n                            :focusedOptionId=\"focusedOptionId\"\n                            :unstyled=\"unstyled\"\n                            @item-click=\"itemClick\"\n                            @item-mousemove=\"itemMouseMove\"\n                            :pt=\"pt\"\n                        />\n                    </template>\n                </ul>\n                <div v-if=\"$slots.end\" :class=\"cx('end')\" v-bind=\"ptm('end')\">\n                    <slot name=\"end\"></slot>\n                </div>\n            </div>\n        </transition>\n    </Portal>\n</template>\n\n<script>\nimport { ConnectedOverlayScrollHandler, UniqueComponentId } from '@primevue/core/utils';\nimport { focus, find, findSingle, addStyle, absolutePosition, getOuterWidth, isTouchDevice } from '@primeuix/utils/dom';\nimport { ZIndex } from '@primeuix/utils/zindex';\nimport OverlayEventBus from 'primevue/overlayeventbus';\nimport Portal from 'primevue/portal';\nimport BaseMenu from './BaseMenu.vue';\nimport Menuitem from './Menuitem.vue';\n\nexport default {\n    name: 'Menu',\n    extends: BaseMenu,\n    inheritAttrs: false,\n    emits: ['show', 'hide', 'focus', 'blur'],\n    data() {\n        return {\n            id: this.$attrs.id,\n            overlayVisible: false,\n            focused: false,\n            focusedOptionIndex: -1,\n            selectedOptionIndex: -1\n        };\n    },\n    watch: {\n        '$attrs.id': function (newValue) {\n            this.id = newValue || UniqueComponentId();\n        }\n    },\n    target: null,\n    outsideClickListener: null,\n    scrollHandler: null,\n    resizeListener: null,\n    container: null,\n    list: null,\n    mounted() {\n        this.id = this.id || UniqueComponentId();\n\n        if (!this.popup) {\n            this.bindResizeListener();\n            this.bindOutsideClickListener();\n        }\n    },\n    beforeUnmount() {\n        this.unbindResizeListener();\n        this.unbindOutsideClickListener();\n\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n\n        this.target = null;\n\n        if (this.container && this.autoZIndex) {\n            ZIndex.clear(this.container);\n        }\n\n        this.container = null;\n    },\n    methods: {\n        itemClick(event) {\n            const item = event.item;\n\n            if (this.disabled(item)) {\n                return;\n            }\n\n            if (item.command) {\n                item.command(event);\n            }\n\n            if (this.overlayVisible) this.hide();\n\n            if (!this.popup && this.focusedOptionIndex !== event.id) {\n                this.focusedOptionIndex = event.id;\n            }\n        },\n        itemMouseMove(event) {\n            if (this.focused) {\n                this.focusedOptionIndex = event.id;\n            }\n        },\n        onListFocus(event) {\n            this.focused = true;\n            !this.popup && this.changeFocusedOptionIndex(0);\n\n            this.$emit('focus', event);\n        },\n        onListBlur(event) {\n            this.focused = false;\n            this.focusedOptionIndex = -1;\n            this.$emit('blur', event);\n        },\n        onListKeyDown(event) {\n            switch (event.code) {\n                case 'ArrowDown':\n                    this.onArrowDownKey(event);\n                    break;\n\n                case 'ArrowUp':\n                    this.onArrowUpKey(event);\n                    break;\n\n                case 'Home':\n                    this.onHomeKey(event);\n                    break;\n\n                case 'End':\n                    this.onEndKey(event);\n                    break;\n\n                case 'Enter':\n                case 'NumpadEnter':\n                    this.onEnterKey(event);\n                    break;\n\n                case 'Space':\n                    this.onSpaceKey(event);\n                    break;\n\n                case 'Escape':\n                    if (this.popup) {\n                        focus(this.target);\n                        this.hide();\n                    }\n\n                case 'Tab':\n                    this.overlayVisible && this.hide();\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onArrowDownKey(event) {\n            const optionIndex = this.findNextOptionIndex(this.focusedOptionIndex);\n\n            this.changeFocusedOptionIndex(optionIndex);\n            event.preventDefault();\n        },\n        onArrowUpKey(event) {\n            if (event.altKey && this.popup) {\n                focus(this.target);\n                this.hide();\n                event.preventDefault();\n            } else {\n                const optionIndex = this.findPrevOptionIndex(this.focusedOptionIndex);\n\n                this.changeFocusedOptionIndex(optionIndex);\n                event.preventDefault();\n            }\n        },\n        onHomeKey(event) {\n            this.changeFocusedOptionIndex(0);\n            event.preventDefault();\n        },\n        onEndKey(event) {\n            this.changeFocusedOptionIndex(find(this.container, 'li[data-pc-section=\"item\"][data-p-disabled=\"false\"]').length - 1);\n            event.preventDefault();\n        },\n        onEnterKey(event) {\n            const element = findSingle(this.list, `li[id=\"${`${this.focusedOptionIndex}`}\"]`);\n            const anchorElement = element && findSingle(element, 'a[data-pc-section=\"itemlink\"]');\n\n            this.popup && focus(this.target);\n            anchorElement ? anchorElement.click() : element && element.click();\n\n            event.preventDefault();\n        },\n        onSpaceKey(event) {\n            this.onEnterKey(event);\n        },\n        findNextOptionIndex(index) {\n            const links = find(this.container, 'li[data-pc-section=\"item\"][data-p-disabled=\"false\"]');\n            const matchedOptionIndex = [...links].findIndex((link) => link.id === index);\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex + 1 : 0;\n        },\n        findPrevOptionIndex(index) {\n            const links = find(this.container, 'li[data-pc-section=\"item\"][data-p-disabled=\"false\"]');\n            const matchedOptionIndex = [...links].findIndex((link) => link.id === index);\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex - 1 : 0;\n        },\n        changeFocusedOptionIndex(index) {\n            const links = find(this.container, 'li[data-pc-section=\"item\"][data-p-disabled=\"false\"]');\n            let order = index >= links.length ? links.length - 1 : index < 0 ? 0 : index;\n\n            order > -1 && (this.focusedOptionIndex = links[order].getAttribute('id'));\n        },\n        toggle(event) {\n            if (this.overlayVisible) this.hide();\n            else this.show(event);\n        },\n        show(event) {\n            this.overlayVisible = true;\n            this.target = event.currentTarget;\n        },\n        hide() {\n            this.overlayVisible = false;\n            this.target = null;\n        },\n        onEnter(el) {\n            addStyle(el, { position: 'absolute', top: '0', left: '0' });\n            this.alignOverlay();\n            this.bindOutsideClickListener();\n            this.bindResizeListener();\n            this.bindScrollListener();\n\n            if (this.autoZIndex) {\n                ZIndex.set('menu', el, this.baseZIndex + this.$primevue.config.zIndex.menu);\n            }\n\n            if (this.popup) {\n                focus(this.list);\n            }\n\n            this.$emit('show');\n        },\n        onLeave() {\n            this.unbindOutsideClickListener();\n            this.unbindResizeListener();\n            this.unbindScrollListener();\n            this.$emit('hide');\n        },\n        onAfterLeave(el) {\n            if (this.autoZIndex) {\n                ZIndex.clear(el);\n            }\n        },\n        alignOverlay() {\n            absolutePosition(this.container, this.target);\n            const targetWidth = getOuterWidth(this.target);\n\n            if (targetWidth > getOuterWidth(this.container)) {\n                this.container.style.minWidth = getOuterWidth(this.target) + 'px';\n            }\n        },\n        bindOutsideClickListener() {\n            if (!this.outsideClickListener) {\n                this.outsideClickListener = (event) => {\n                    const isOutsideContainer = this.container && !this.container.contains(event.target);\n                    const isOutsideTarget = !(this.target && (this.target === event.target || this.target.contains(event.target)));\n\n                    if (this.overlayVisible && isOutsideContainer && isOutsideTarget) {\n                        this.hide();\n                    } else if (!this.popup && isOutsideContainer && isOutsideTarget) {\n                        this.focusedOptionIndex = -1;\n                    }\n                };\n\n                document.addEventListener('click', this.outsideClickListener);\n            }\n        },\n        unbindOutsideClickListener() {\n            if (this.outsideClickListener) {\n                document.removeEventListener('click', this.outsideClickListener);\n                this.outsideClickListener = null;\n            }\n        },\n        bindScrollListener() {\n            if (!this.scrollHandler) {\n                this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, () => {\n                    if (this.overlayVisible) {\n                        this.hide();\n                    }\n                });\n            }\n\n            this.scrollHandler.bindScrollListener();\n        },\n        unbindScrollListener() {\n            if (this.scrollHandler) {\n                this.scrollHandler.unbindScrollListener();\n            }\n        },\n        bindResizeListener() {\n            if (!this.resizeListener) {\n                this.resizeListener = () => {\n                    if (this.overlayVisible && !isTouchDevice()) {\n                        this.hide();\n                    }\n                };\n\n                window.addEventListener('resize', this.resizeListener);\n            }\n        },\n        unbindResizeListener() {\n            if (this.resizeListener) {\n                window.removeEventListener('resize', this.resizeListener);\n                this.resizeListener = null;\n            }\n        },\n        visible(item) {\n            return typeof item.visible === 'function' ? item.visible() : item.visible !== false;\n        },\n        disabled(item) {\n            return typeof item.disabled === 'function' ? item.disabled() : item.disabled;\n        },\n        label(item) {\n            return typeof item.label === 'function' ? item.label() : item.label;\n        },\n        onOverlayClick(event) {\n            OverlayEventBus.emit('overlay-click', {\n                originalEvent: event,\n                target: this.target\n            });\n        },\n        containerRef(el) {\n            this.container = el;\n        },\n        listRef(el) {\n            this.list = el;\n        }\n    },\n    computed: {\n        focusedOptionId() {\n            return this.focusedOptionIndex !== -1 ? this.focusedOptionIndex : null;\n        }\n    },\n    components: {\n        PVMenuitem: Menuitem,\n        Portal: Portal\n    }\n};\n</script>\n", "<template>\n    <Portal :appendTo=\"appendTo\" :disabled=\"!popup\">\n        <transition name=\"p-connected-overlay\" @enter=\"onEnter\" @leave=\"onLeave\" @after-leave=\"onAfterLeave\" v-bind=\"ptm('transition')\">\n            <div v-if=\"popup ? overlayVisible : true\" :ref=\"containerRef\" :id=\"id\" :class=\"cx('root')\" @click=\"onOverlayClick\" v-bind=\"ptmi('root')\">\n                <div v-if=\"$slots.start\" :class=\"cx('start')\" v-bind=\"ptm('start')\">\n                    <slot name=\"start\"></slot>\n                </div>\n                <ul\n                    :ref=\"listRef\"\n                    :id=\"id + '_list'\"\n                    :class=\"cx('list')\"\n                    role=\"menu\"\n                    :tabindex=\"tabindex\"\n                    :aria-activedescendant=\"focused ? focusedOptionId : undefined\"\n                    :aria-label=\"ariaLabel\"\n                    :aria-labelledby=\"ariaLabelledby\"\n                    @focus=\"onListFocus\"\n                    @blur=\"onListBlur\"\n                    @keydown=\"onListKeyDown\"\n                    v-bind=\"ptm('list')\"\n                >\n                    <template v-for=\"(item, i) of model\" :key=\"label(item) + i.toString()\">\n                        <template v-if=\"item.items && visible(item) && !item.separator\">\n                            <li v-if=\"item.items\" :id=\"id + '_' + i\" :class=\"[cx('submenuLabel'), item.class]\" role=\"none\" v-bind=\"ptm('submenuLabel')\">\n                                <!--TODO: submenuheader deprecated since v4.0. Use submenulabel-->\n                                <slot :name=\"$slots.submenulabel ? 'submenulabel' : 'submenuheader'\" :item=\"item\">{{ label(item) }}</slot>\n                            </li>\n                            <template v-for=\"(child, j) of item.items\" :key=\"child.label + i + '_' + j\">\n                                <PVMenuitem\n                                    v-if=\"visible(child) && !child.separator\"\n                                    :id=\"id + '_' + i + '_' + j\"\n                                    :item=\"child\"\n                                    :templates=\"$slots\"\n                                    :focusedOptionId=\"focusedOptionId\"\n                                    :unstyled=\"unstyled\"\n                                    @item-click=\"itemClick\"\n                                    @item-mousemove=\"itemMouseMove\"\n                                    :pt=\"pt\"\n                                />\n                                <li v-else-if=\"visible(child) && child.separator\" :key=\"'separator' + i + j\" :class=\"[cx('separator'), item.class]\" :style=\"child.style\" role=\"separator\" v-bind=\"ptm('separator')\"></li>\n                            </template>\n                        </template>\n                        <li v-else-if=\"visible(item) && item.separator\" :key=\"'separator' + i.toString()\" :class=\"[cx('separator'), item.class]\" :style=\"item.style\" role=\"separator\" v-bind=\"ptm('separator')\"></li>\n                        <PVMenuitem\n                            v-else\n                            :key=\"label(item) + i.toString()\"\n                            :id=\"id + '_' + i\"\n                            :item=\"item\"\n                            :index=\"i\"\n                            :templates=\"$slots\"\n                            :focusedOptionId=\"focusedOptionId\"\n                            :unstyled=\"unstyled\"\n                            @item-click=\"itemClick\"\n                            @item-mousemove=\"itemMouseMove\"\n                            :pt=\"pt\"\n                        />\n                    </template>\n                </ul>\n                <div v-if=\"$slots.end\" :class=\"cx('end')\" v-bind=\"ptm('end')\">\n                    <slot name=\"end\"></slot>\n                </div>\n            </div>\n        </transition>\n    </Portal>\n</template>\n\n<script>\nimport { ConnectedOverlayScrollHandler, UniqueComponentId } from '@primevue/core/utils';\nimport { focus, find, findSingle, addStyle, absolutePosition, getOuterWidth, isTouchDevice } from '@primeuix/utils/dom';\nimport { ZIndex } from '@primeuix/utils/zindex';\nimport OverlayEventBus from 'primevue/overlayeventbus';\nimport Portal from 'primevue/portal';\nimport BaseMenu from './BaseMenu.vue';\nimport Menuitem from './Menuitem.vue';\n\nexport default {\n    name: 'Menu',\n    extends: BaseMenu,\n    inheritAttrs: false,\n    emits: ['show', 'hide', 'focus', 'blur'],\n    data() {\n        return {\n            id: this.$attrs.id,\n            overlayVisible: false,\n            focused: false,\n            focusedOptionIndex: -1,\n            selectedOptionIndex: -1\n        };\n    },\n    watch: {\n        '$attrs.id': function (newValue) {\n            this.id = newValue || UniqueComponentId();\n        }\n    },\n    target: null,\n    outsideClickListener: null,\n    scrollHandler: null,\n    resizeListener: null,\n    container: null,\n    list: null,\n    mounted() {\n        this.id = this.id || UniqueComponentId();\n\n        if (!this.popup) {\n            this.bindResizeListener();\n            this.bindOutsideClickListener();\n        }\n    },\n    beforeUnmount() {\n        this.unbindResizeListener();\n        this.unbindOutsideClickListener();\n\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n\n        this.target = null;\n\n        if (this.container && this.autoZIndex) {\n            ZIndex.clear(this.container);\n        }\n\n        this.container = null;\n    },\n    methods: {\n        itemClick(event) {\n            const item = event.item;\n\n            if (this.disabled(item)) {\n                return;\n            }\n\n            if (item.command) {\n                item.command(event);\n            }\n\n            if (this.overlayVisible) this.hide();\n\n            if (!this.popup && this.focusedOptionIndex !== event.id) {\n                this.focusedOptionIndex = event.id;\n            }\n        },\n        itemMouseMove(event) {\n            if (this.focused) {\n                this.focusedOptionIndex = event.id;\n            }\n        },\n        onListFocus(event) {\n            this.focused = true;\n            !this.popup && this.changeFocusedOptionIndex(0);\n\n            this.$emit('focus', event);\n        },\n        onListBlur(event) {\n            this.focused = false;\n            this.focusedOptionIndex = -1;\n            this.$emit('blur', event);\n        },\n        onListKeyDown(event) {\n            switch (event.code) {\n                case 'ArrowDown':\n                    this.onArrowDownKey(event);\n                    break;\n\n                case 'ArrowUp':\n                    this.onArrowUpKey(event);\n                    break;\n\n                case 'Home':\n                    this.onHomeKey(event);\n                    break;\n\n                case 'End':\n                    this.onEndKey(event);\n                    break;\n\n                case 'Enter':\n                case 'NumpadEnter':\n                    this.onEnterKey(event);\n                    break;\n\n                case 'Space':\n                    this.onSpaceKey(event);\n                    break;\n\n                case 'Escape':\n                    if (this.popup) {\n                        focus(this.target);\n                        this.hide();\n                    }\n\n                case 'Tab':\n                    this.overlayVisible && this.hide();\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onArrowDownKey(event) {\n            const optionIndex = this.findNextOptionIndex(this.focusedOptionIndex);\n\n            this.changeFocusedOptionIndex(optionIndex);\n            event.preventDefault();\n        },\n        onArrowUpKey(event) {\n            if (event.altKey && this.popup) {\n                focus(this.target);\n                this.hide();\n                event.preventDefault();\n            } else {\n                const optionIndex = this.findPrevOptionIndex(this.focusedOptionIndex);\n\n                this.changeFocusedOptionIndex(optionIndex);\n                event.preventDefault();\n            }\n        },\n        onHomeKey(event) {\n            this.changeFocusedOptionIndex(0);\n            event.preventDefault();\n        },\n        onEndKey(event) {\n            this.changeFocusedOptionIndex(find(this.container, 'li[data-pc-section=\"item\"][data-p-disabled=\"false\"]').length - 1);\n            event.preventDefault();\n        },\n        onEnterKey(event) {\n            const element = findSingle(this.list, `li[id=\"${`${this.focusedOptionIndex}`}\"]`);\n            const anchorElement = element && findSingle(element, 'a[data-pc-section=\"itemlink\"]');\n\n            this.popup && focus(this.target);\n            anchorElement ? anchorElement.click() : element && element.click();\n\n            event.preventDefault();\n        },\n        onSpaceKey(event) {\n            this.onEnterKey(event);\n        },\n        findNextOptionIndex(index) {\n            const links = find(this.container, 'li[data-pc-section=\"item\"][data-p-disabled=\"false\"]');\n            const matchedOptionIndex = [...links].findIndex((link) => link.id === index);\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex + 1 : 0;\n        },\n        findPrevOptionIndex(index) {\n            const links = find(this.container, 'li[data-pc-section=\"item\"][data-p-disabled=\"false\"]');\n            const matchedOptionIndex = [...links].findIndex((link) => link.id === index);\n\n            return matchedOptionIndex > -1 ? matchedOptionIndex - 1 : 0;\n        },\n        changeFocusedOptionIndex(index) {\n            const links = find(this.container, 'li[data-pc-section=\"item\"][data-p-disabled=\"false\"]');\n            let order = index >= links.length ? links.length - 1 : index < 0 ? 0 : index;\n\n            order > -1 && (this.focusedOptionIndex = links[order].getAttribute('id'));\n        },\n        toggle(event) {\n            if (this.overlayVisible) this.hide();\n            else this.show(event);\n        },\n        show(event) {\n            this.overlayVisible = true;\n            this.target = event.currentTarget;\n        },\n        hide() {\n            this.overlayVisible = false;\n            this.target = null;\n        },\n        onEnter(el) {\n            addStyle(el, { position: 'absolute', top: '0', left: '0' });\n            this.alignOverlay();\n            this.bindOutsideClickListener();\n            this.bindResizeListener();\n            this.bindScrollListener();\n\n            if (this.autoZIndex) {\n                ZIndex.set('menu', el, this.baseZIndex + this.$primevue.config.zIndex.menu);\n            }\n\n            if (this.popup) {\n                focus(this.list);\n            }\n\n            this.$emit('show');\n        },\n        onLeave() {\n            this.unbindOutsideClickListener();\n            this.unbindResizeListener();\n            this.unbindScrollListener();\n            this.$emit('hide');\n        },\n        onAfterLeave(el) {\n            if (this.autoZIndex) {\n                ZIndex.clear(el);\n            }\n        },\n        alignOverlay() {\n            absolutePosition(this.container, this.target);\n            const targetWidth = getOuterWidth(this.target);\n\n            if (targetWidth > getOuterWidth(this.container)) {\n                this.container.style.minWidth = getOuterWidth(this.target) + 'px';\n            }\n        },\n        bindOutsideClickListener() {\n            if (!this.outsideClickListener) {\n                this.outsideClickListener = (event) => {\n                    const isOutsideContainer = this.container && !this.container.contains(event.target);\n                    const isOutsideTarget = !(this.target && (this.target === event.target || this.target.contains(event.target)));\n\n                    if (this.overlayVisible && isOutsideContainer && isOutsideTarget) {\n                        this.hide();\n                    } else if (!this.popup && isOutsideContainer && isOutsideTarget) {\n                        this.focusedOptionIndex = -1;\n                    }\n                };\n\n                document.addEventListener('click', this.outsideClickListener);\n            }\n        },\n        unbindOutsideClickListener() {\n            if (this.outsideClickListener) {\n                document.removeEventListener('click', this.outsideClickListener);\n                this.outsideClickListener = null;\n            }\n        },\n        bindScrollListener() {\n            if (!this.scrollHandler) {\n                this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, () => {\n                    if (this.overlayVisible) {\n                        this.hide();\n                    }\n                });\n            }\n\n            this.scrollHandler.bindScrollListener();\n        },\n        unbindScrollListener() {\n            if (this.scrollHandler) {\n                this.scrollHandler.unbindScrollListener();\n            }\n        },\n        bindResizeListener() {\n            if (!this.resizeListener) {\n                this.resizeListener = () => {\n                    if (this.overlayVisible && !isTouchDevice()) {\n                        this.hide();\n                    }\n                };\n\n                window.addEventListener('resize', this.resizeListener);\n            }\n        },\n        unbindResizeListener() {\n            if (this.resizeListener) {\n                window.removeEventListener('resize', this.resizeListener);\n                this.resizeListener = null;\n            }\n        },\n        visible(item) {\n            return typeof item.visible === 'function' ? item.visible() : item.visible !== false;\n        },\n        disabled(item) {\n            return typeof item.disabled === 'function' ? item.disabled() : item.disabled;\n        },\n        label(item) {\n            return typeof item.label === 'function' ? item.label() : item.label;\n        },\n        onOverlayClick(event) {\n            OverlayEventBus.emit('overlay-click', {\n                originalEvent: event,\n                target: this.target\n            });\n        },\n        containerRef(el) {\n            this.container = el;\n        },\n        listRef(el) {\n            this.list = el;\n        }\n    },\n    computed: {\n        focusedOptionId() {\n            return this.focusedOptionIndex !== -1 ? this.focusedOptionIndex : null;\n        }\n    },\n    components: {\n        PVMenuitem: Menuitem,\n        Portal: Portal\n    }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAMA,QAAQ,SAARA,OAAKC,MAAA;AAAA,MAAMC,KAAED,KAAFC;AAAE,SAAA,gCAAAC,OAEDD,GAAG,iBAAiB,GAAC,gBAAA,EAAAC,OAC1BD,GAAG,YAAY,GAAC,2BAAA,EAAAC,OACLD,GAAG,mBAAmB,GAACC,wBAAAA,EAAAA,OAC1BD,GAAG,oBAAoB,GAAC,gFAAA,EAAAC,OAM9BD,GAAG,mBAAmB,GAACC,4GAAAA,EAAAA,OAK3BD,GAAG,eAAe,GAAC,6DAAA,EAAAC,OAIDD,GAAG,0BAA0B,GAACC,UAAAA,EAAAA,OAAWD,GAAG,0BAA0B,GAAC,wBAAA,EAAAC,OAC/ED,GAAG,yBAAyB,GAACC,gBAAAA,EAAAA,OACrCD,GAAG,iBAAiB,GAAC,iNAAA,EAAAC,OAWnBD,GAAG,mBAAmB,GAAC,cAAA,EAAAC,OAC3BD,GAAG,eAAe,GAAC,wIAAA,EAAAC,OAUjBD,GAAG,sBAAsB,GAAC,kEAAA,EAAAC,OAI1BD,GAAG,uBAAuB,GAACC,qBAAAA,EAAAA,OACtBD,GAAG,4BAA4B,GAAC,+DAAA,EAAAC,OAIrCD,GAAG,4BAA4B,GAACC,iFAAAA,EAAAA,OAIhCD,GAAG,uBAAuB,GAAC,qBAAA,EAAAC,OACtBD,GAAG,4BAA4B,GAACC,mGAAAA,EAAAA,OAIrCD,GAAG,4BAA4B,GAAC,6CAAA,EAAAC,OAI3BD,GAAG,aAAa,GAACC,mDAAAA,EAAAA,OAIjBD,GAAG,+BAA+B,GAAC,kBAAA,EAAAC,OACtCD,GAAG,4BAA4B,GAAC,gBAAA,EAAAC,OAClCD,GAAG,0BAA0B,GAAC,sBAAA,EAAAC,OACxBD,GAAG,gCAAgC,GAACC,iEAAAA,EAAAA,OAInBD,GAAG,6BAA6B,GAAC,QAAA;AAAA;AAIrE,IAAME,UAAU;EACZC,MAAM,SAANA,KAAIC,OAAA;AAAA,QAAKC,QAAKD,MAALC;AAAK,WAAO,CACjB,sBACA;MACI,kBAAkBA,MAAMC;IAC5B,CAAC;EACJ;EACDC,OAAO;EACPC,MAAM;EACNC,cAAc;EACdC,WAAW;EACXC,KAAK;EACLC,MAAM,SAANA,KAAIC,OAAA;AAAA,QAAKC,WAAQD,MAARC;AAAQ,WAAO,CACpB,eACA;MACI,WAAWA,SAASC,OAAOD,SAASE;MACpC,cAAcF,SAASG,SAAQ;IACnC,CAAC;EACJ;EACDC,aAAa;EACbC,UAAU;EACVC,UAAU;EACVC,WAAW;AACf;AAEA,IAAA,YAAeC,UAAUC,OAAO;EAC5BC,MAAM;EACN1B;EACAI;AACJ,CAAC;;;AC5GD,IAAA,WAAe;EACXuB,MAAM;EACN,WAASC;EACTC,OAAO;IACHC,OAAO;MACHC,MAAMC;MACN,WAAS;;IAEbC,OAAO;MACHF,MAAMG;MACN,WAAS;;IAEbC,UAAU;MACNJ,MAAM,CAACK,QAAQC,MAAM;MACrB,WAAS;;IAEbC,YAAY;MACRP,MAAMC;MACN,WAAS;;IAEbO,YAAY;MACRR,MAAMS;MACN,WAAS;;IAEbC,UAAU;MACNV,MAAMS;MACN,WAAS;;IAEbE,WAAW;MACPX,MAAMK;MACN,WAAS;;IAEbO,gBAAgB;MACZZ,MAAMK;MACN,WAAS;IACb;;EAEJQ,OAAOC;EACPC,SAAO,SAAPA,UAAU;AACN,WAAO;MACHC,SAAS;MACTC,iBAAiB;;EAEzB;AACJ;AChBA,IAAA,WAAe;EACXrB,MAAM;EACNsB,UAAU;EACV,WAASrB;EACTsB,cAAc;EACdC,OAAO,CAAC,cAAc,gBAAgB;EACtCtB,OAAO;IACHuB,MAAM;IACNC,WAAW;IACXC,IAAI;IACJC,iBAAiB;IACjBC,OAAO;;EAEXC,SAAS;IACLC,aAAW,SAAXA,YAAYC,eAAehC,MAAM;AAC7B,aAAOgC,iBAAiBA,cAAcP,OAAOQ,QAAQD,cAAcP,KAAKzB,IAAI,CAAC,IAAIkC;;IAErFC,cAAAA,SAAAA,aAAaC,KAAK;AACd,aAAO,KAAKC,IAAID,KAAK;QACjBE,SAAS;UACLb,MAAM,KAAKA;UACXI,OAAO,KAAKA;UACZU,SAAS,KAAKC,cAAa;UAC3BC,UAAU,KAAKA,SAAQ;QAC3B;MACJ,CAAC;;IAELD,eAAa,SAAbA,gBAAgB;AACZ,aAAO,KAAKZ,oBAAoB,KAAKD;;IAEzCe,aAAAA,SAAAA,YAAYC,OAAO;AACf,UAAMC,UAAU,KAAKb,YAAY,KAAKN,MAAM,SAAS;AAErDmB,iBAAWA,QAAQ;QAAEC,eAAeF;QAAOlB,MAAM,KAAKA,KAAKA;MAAK,CAAC;AACjE,WAAKqB,MAAM,cAAc;QAAED,eAAeF;QAAOlB,MAAM,KAAKA;QAAME,IAAI,KAAKA;MAAG,CAAC;;IAEnFoB,iBAAAA,SAAAA,gBAAgBJ,OAAO;AACnB,WAAKG,MAAM,kBAAkB;QAAED,eAAeF;QAAOlB,MAAM,KAAKA;QAAME,IAAI,KAAKA;MAAG,CAAC;;IAEvFqB,SAAO,SAAPA,UAAU;AACN,aAAO,OAAO,KAAKvB,KAAKuB,YAAY,aAAa,KAAKvB,KAAKuB,QAAO,IAAK,KAAKvB,KAAKuB,YAAY;;IAEjGP,UAAQ,SAARA,WAAW;AACP,aAAO,OAAO,KAAKhB,KAAKgB,aAAa,aAAa,KAAKhB,KAAKgB,SAAQ,IAAK,KAAKhB,KAAKgB;;IAEvFQ,OAAK,SAALA,QAAQ;AACJ,aAAO,OAAO,KAAKxB,KAAKwB,UAAU,aAAa,KAAKxB,KAAKwB,MAAK,IAAK,KAAKxB,KAAKwB;;IAEjFC,kBAAAA,SAAAA,iBAAiBzB,OAAM;AACnB,aAAO;QACH0B,QAAQC,WACJ;UACI,SAAO,KAAKC,GAAG,UAAU;UACzBvC,UAAU;QACd,GACA,KAAKqB,aAAa,UAAU,CAChC;QACAmB,MAAMF,WACF;UACI,SAAO,CAAC,KAAKC,GAAG,UAAU,GAAG5B,MAAK6B,IAAI;QAC1C,GACA,KAAKnB,aAAa,UAAU,CAChC;QACAc,OAAOG,WACH;UACI,SAAO,KAAKC,GAAG,WAAW;QAC9B,GACA,KAAKlB,aAAa,WAAW,CACjC;;IAER;;EAEJoB,YAAY;IACRC,QAAQC;EACZ;AACJ;;;;;SCzGcC,SAAOV,QAAA,KADjBW,UAAA,GAAAC,mBAsBI,MAtBJC,WAsBI;;IApBClC,IAAImC,OAAEnC;IACN,SAAQ,CAAAoC,KAAAV,GAAY,MAAA,GAAAS,OAAArC,KAAI,OAAA,CAAM;IAC/BuC,MAAK;IACJ/C,OAAO6C,OAAIrC,KAACR;IACZ,cAAYyC,SAAKT,MAAA;IACjB,iBAAeS,SAAQjB,SAAA;KAChBiB,SAAYvB,aAAA,MAAA,GAAA;IACnB,kBAAgBuB,SAAalB,cAAA;IAC7B,mBAAiBkB,SAAQjB,SAAA,KAAA;OAE1BwB,gBASK,OATLJ,WASK;IATC,SAAOE,KAAEV,GAAA,aAAA;IAAkBa,SAAKC,OAAA,CAAA,MAAAA,OAAA,CAAA,IAAA,SAAAC,QAAA;AAAA,aAAEV,SAAWhB,YAAC0B,MAAM;IAAA;IAAIC,aAASF,OAAA,CAAA,MAAAA,OAAA,CAAA,IAAA,SAAAC,QAAA;AAAA,aAAEV,SAAeX,gBAACqB,MAAM;;KAAWV,SAAYvB,aAAA,aAAA,CAAA,GAAA,CACjG,CAAA2B,OAAApC,UAAUD,OACvB6C,gBAAAX,UAAA,GAAAC,mBAIG,KAJHC,WAIG;;IAJUU,MAAMT,OAAIrC,KAAC+C;IAAM,SAAOT,KAAEV,GAAA,UAAA;IAAeoB,QAAQX,OAAIrC,KAACgD;IAAQ3D,UAAS;KAAa4C,SAAYvB,aAAA,UAAA,CAAA,GAAA,CACxF2B,OAAApC,UAAUgD,YAAQ,UAAA,GAAnCC,YAAoGC,wBAA1Dd,OAASpC,UAACgD,QAAQ,GAAA;;IAAGjD,MAAMqC,OAAIrC;IAAG,SAAA,eAAOsC,KAAEV,GAAA,UAAA,CAAA;oCACpES,OAAArC,KAAK6B,QAAtBK,UAAA,GAAAC,mBAAoG,QAApGC,WAAoG;;IAAvE,SAAQ,CAAAE,KAAAV,GAAgB,UAAA,GAAAS,OAAArC,KAAK6B,IAAI;KAAWI,SAAYvB,aAAA,UAAA,CAAA,GAAA,MAAA,EAAA,KAAA,mBAAA,IAAA,IAAA,GACrF8B,gBAAqF,QAArFJ,WAAqF;IAA9E,SAAOE,KAAEV,GAAA,WAAA;KAAuBK,SAAAvB,aAAY,WAAA,CAAA,GAAA,gBAAkBuB,SAAKT,MAAA,CAAA,GAAA,EAAA,CAAA,GAAA,IAAA,YAAA,IAAA,CAAA,CAAA,iBAAA,CAAA,CAAA,IAG5Da,OAAApC,UAAUD,QAAI,UAAA,GAApCkD,YAAoIC,wBAAzFd,OAASpC,UAACD,IAAI,GAAA;;IAAGA,MAAMqC,OAAIrC;IAAGwB,OAAOS,SAAKT,MAAA;IAAK/C,OAAOwD,SAAgBR,iBAACY,OAAIrC,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsDlI,IAAAoD,UAAe;EACX7E,MAAM;EACN,WAAS8E;EACTvD,cAAc;EACdC,OAAO,CAAC,QAAQ,QAAQ,SAAS,MAAM;EACvCuD,MAAI,SAAJA,OAAO;AACH,WAAO;MACHpD,IAAI,KAAKqD,OAAOrD;MAChBsD,gBAAgB;MAChB1C,SAAS;MACT2C,oBAAoB;MACpBC,qBAAqB;;;EAG7BC,OAAO;IACH,aAAa,SAAbC,SAAuBC,UAAU;AAC7B,WAAK3D,KAAK2D,YAAYC,kBAAiB;IAC3C;;EAEJd,QAAQ;EACRe,sBAAsB;EACtBC,eAAe;EACfC,gBAAgB;EAChBC,WAAW;EACXC,MAAM;EACNC,SAAO,SAAPA,UAAU;AACN,SAAKlE,KAAK,KAAKA,MAAM4D,kBAAiB;AAEtC,QAAI,CAAC,KAAKpF,OAAO;AACb,WAAK2F,mBAAkB;AACvB,WAAKC,yBAAwB;IACjC;;EAEJC,eAAa,SAAbA,gBAAgB;AACZ,SAAKC,qBAAoB;AACzB,SAAKC,2BAA0B;AAE/B,QAAI,KAAKT,eAAe;AACpB,WAAKA,cAAcU,QAAO;AAC1B,WAAKV,gBAAgB;IACzB;AAEA,SAAKhB,SAAS;AAEd,QAAI,KAAKkB,aAAa,KAAKhF,YAAY;AACnCyF,aAAOC,MAAM,KAAKV,SAAS;IAC/B;AAEA,SAAKA,YAAY;;EAErB7D,SAAS;IACLwE,WAAAA,SAAAA,UAAU3D,OAAO;AACb,UAAMlB,QAAOkB,MAAMlB;AAEnB,UAAI,KAAKgB,SAAShB,KAAI,GAAG;AACrB;MACJ;AAEA,UAAIA,MAAKmB,SAAS;AACdnB,QAAAA,MAAKmB,QAAQD,KAAK;MACtB;AAEA,UAAI,KAAKsC,eAAgB,MAAKsB,KAAI;AAElC,UAAI,CAAC,KAAKpG,SAAS,KAAK+E,uBAAuBvC,MAAMhB,IAAI;AACrD,aAAKuD,qBAAqBvC,MAAMhB;MACpC;;IAEJ6E,eAAAA,SAAAA,cAAc7D,OAAO;AACjB,UAAI,KAAKJ,SAAS;AACd,aAAK2C,qBAAqBvC,MAAMhB;MACpC;;IAEJ8E,aAAAA,SAAAA,YAAY9D,OAAO;AACf,WAAKJ,UAAU;AACf,OAAC,KAAKpC,SAAS,KAAKuG,yBAAyB,CAAC;AAE9C,WAAK5D,MAAM,SAASH,KAAK;;IAE7BgE,YAAAA,SAAAA,WAAWhE,OAAO;AACd,WAAKJ,UAAU;AACf,WAAK2C,qBAAqB;AAC1B,WAAKpC,MAAM,QAAQH,KAAK;;IAE5BiE,eAAAA,SAAAA,cAAcjE,OAAO;AACjB,cAAQA,MAAMkE,MAAI;QACd,KAAK;AACD,eAAKC,eAAenE,KAAK;AACzB;QAEJ,KAAK;AACD,eAAKoE,aAAapE,KAAK;AACvB;QAEJ,KAAK;AACD,eAAKqE,UAAUrE,KAAK;AACpB;QAEJ,KAAK;AACD,eAAKsE,SAAStE,KAAK;AACnB;QAEJ,KAAK;QACL,KAAK;AACD,eAAKuE,WAAWvE,KAAK;AACrB;QAEJ,KAAK;AACD,eAAKwE,WAAWxE,KAAK;AACrB;QAEJ,KAAK;AACD,cAAI,KAAKxC,OAAO;AACZiH,kBAAM,KAAK3C,MAAM;AACjB,iBAAK8B,KAAI;UACb;QAEJ,KAAK;AACD,eAAKtB,kBAAkB,KAAKsB,KAAI;AAChC;MAIR;;IAEJO,gBAAAA,SAAAA,eAAenE,OAAO;AAClB,UAAM0E,cAAc,KAAKC,oBAAoB,KAAKpC,kBAAkB;AAEpE,WAAKwB,yBAAyBW,WAAW;AACzC1E,YAAM4E,eAAc;;IAExBR,cAAAA,SAAAA,aAAapE,OAAO;AAChB,UAAIA,MAAM6E,UAAU,KAAKrH,OAAO;AAC5BiH,cAAM,KAAK3C,MAAM;AACjB,aAAK8B,KAAI;AACT5D,cAAM4E,eAAc;MACxB,OAAO;AACH,YAAMF,cAAc,KAAKI,oBAAoB,KAAKvC,kBAAkB;AAEpE,aAAKwB,yBAAyBW,WAAW;AACzC1E,cAAM4E,eAAc;MACxB;;IAEJP,WAAAA,SAAAA,UAAUrE,OAAO;AACb,WAAK+D,yBAAyB,CAAC;AAC/B/D,YAAM4E,eAAc;;IAExBN,UAAAA,SAAAA,SAAStE,OAAO;AACZ,WAAK+D,yBAAyBgB,KAAK,KAAK/B,WAAW,qDAAqD,EAAEgC,SAAS,CAAC;AACpHhF,YAAM4E,eAAc;;IAExBL,YAAAA,SAAAA,WAAWvE,OAAO;AACd,UAAMiF,UAAUC,WAAW,KAAKjC,MAAI,UAAAkC,OAAA,GAAAA,OAAe,KAAK5C,kBAAkB,GAAA,IAAA,CAAM;AAChF,UAAM6C,gBAAgBH,WAAWC,WAAWD,SAAS,+BAA+B;AAEpF,WAAKzH,SAASiH,MAAM,KAAK3C,MAAM;AAC/BsD,sBAAgBA,cAAcC,MAAK,IAAKJ,WAAWA,QAAQI,MAAK;AAEhErF,YAAM4E,eAAc;;IAExBJ,YAAAA,SAAAA,WAAWxE,OAAO;AACd,WAAKuE,WAAWvE,KAAK;;IAEzB2E,qBAAAA,SAAAA,oBAAoBzF,OAAO;AACvB,UAAMoG,QAAQP,KAAK,KAAK/B,WAAW,qDAAqD;AACxF,UAAMuC,qBAAqBC,mBAAIF,KAAK,EAAEG,UAAU,SAACC,MAAI;AAAA,eAAKA,KAAK1G,OAAOE;OAAM;AAE5E,aAAOqG,qBAAqB,KAAKA,qBAAqB,IAAI;;IAE9DT,qBAAAA,SAAAA,oBAAoB5F,OAAO;AACvB,UAAMoG,QAAQP,KAAK,KAAK/B,WAAW,qDAAqD;AACxF,UAAMuC,qBAAqBC,mBAAIF,KAAK,EAAEG,UAAU,SAACC,MAAI;AAAA,eAAKA,KAAK1G,OAAOE;OAAM;AAE5E,aAAOqG,qBAAqB,KAAKA,qBAAqB,IAAI;;IAE9DxB,0BAAAA,SAAAA,yBAAyB7E,OAAO;AAC5B,UAAMoG,QAAQP,KAAK,KAAK/B,WAAW,qDAAqD;AACxF,UAAI2C,QAAQzG,SAASoG,MAAMN,SAASM,MAAMN,SAAS,IAAI9F,QAAQ,IAAI,IAAIA;AAEvEyG,cAAQ,OAAO,KAAKpD,qBAAqB+C,MAAMK,KAAK,EAAEC,aAAa,IAAI;;IAE3EC,QAAAA,SAAAA,OAAO7F,OAAO;AACV,UAAI,KAAKsC,eAAgB,MAAKsB,KAAI;UAC7B,MAAKkC,KAAK9F,KAAK;;IAExB8F,MAAAA,SAAAA,KAAK9F,OAAO;AACR,WAAKsC,iBAAiB;AACtB,WAAKR,SAAS9B,MAAM+F;;IAExBnC,MAAI,SAAJA,OAAO;AACH,WAAKtB,iBAAiB;AACtB,WAAKR,SAAS;;IAElBkE,SAAAA,SAAAA,QAAQC,IAAI;AACRC,eAASD,IAAI;QAAEE,UAAU;QAAYC,KAAK;QAAKC,MAAM;MAAI,CAAC;AAC1D,WAAKC,aAAY;AACjB,WAAKlD,yBAAwB;AAC7B,WAAKD,mBAAkB;AACvB,WAAKoD,mBAAkB;AAEvB,UAAI,KAAKvI,YAAY;AACjByF,eAAO+C,IAAI,QAAQP,IAAI,KAAKhI,aAAa,KAAKwI,UAAUC,OAAOC,OAAOC,IAAI;MAC9E;AAEA,UAAI,KAAKpJ,OAAO;AACZiH,cAAM,KAAKxB,IAAI;MACnB;AAEA,WAAK9C,MAAM,MAAM;;IAErB0G,SAAO,SAAPA,UAAU;AACN,WAAKtD,2BAA0B;AAC/B,WAAKD,qBAAoB;AACzB,WAAKwD,qBAAoB;AACzB,WAAK3G,MAAM,MAAM;;IAErB4G,cAAAA,SAAAA,aAAad,IAAI;AACb,UAAI,KAAKjI,YAAY;AACjByF,eAAOC,MAAMuC,EAAE;MACnB;;IAEJK,cAAY,SAAZA,eAAe;AACXU,uBAAiB,KAAKhE,WAAW,KAAKlB,MAAM;AAC5C,UAAMmF,cAAcC,cAAc,KAAKpF,MAAM;AAE7C,UAAImF,cAAcC,cAAc,KAAKlE,SAAS,GAAG;AAC7C,aAAKA,UAAU1E,MAAM6I,WAAWD,cAAc,KAAKpF,MAAM,IAAI;MACjE;;IAEJsB,0BAAwB,SAAxBA,2BAA2B;AAAA,UAAAgE,QAAA;AACvB,UAAI,CAAC,KAAKvE,sBAAsB;AAC5B,aAAKA,uBAAuB,SAAC7C,OAAU;AACnC,cAAMqH,qBAAqBD,MAAKpE,aAAa,CAACoE,MAAKpE,UAAUsE,SAAStH,MAAM8B,MAAM;AAClF,cAAMyF,kBAAkB,EAAEH,MAAKtF,WAAWsF,MAAKtF,WAAW9B,MAAM8B,UAAUsF,MAAKtF,OAAOwF,SAAStH,MAAM8B,MAAM;AAE3G,cAAIsF,MAAK9E,kBAAkB+E,sBAAsBE,iBAAiB;AAC9DH,kBAAKxD,KAAI;qBACF,CAACwD,MAAK5J,SAAS6J,sBAAsBE,iBAAiB;AAC7DH,kBAAK7E,qBAAqB;UAC9B;;AAGJiF,iBAASC,iBAAiB,SAAS,KAAK5E,oBAAoB;MAChE;;IAEJU,4BAA0B,SAA1BA,6BAA6B;AACzB,UAAI,KAAKV,sBAAsB;AAC3B2E,iBAASE,oBAAoB,SAAS,KAAK7E,oBAAoB;AAC/D,aAAKA,uBAAuB;MAChC;;IAEJ0D,oBAAkB,SAAlBA,qBAAqB;AAAA,UAAAoB,SAAA;AACjB,UAAI,CAAC,KAAK7E,eAAe;AACrB,aAAKA,gBAAgB,IAAI8E,8BAA8B,KAAK9F,QAAQ,WAAM;AACtE,cAAI6F,OAAKrF,gBAAgB;AACrBqF,mBAAK/D,KAAI;UACb;QACJ,CAAC;MACL;AAEA,WAAKd,cAAcyD,mBAAkB;;IAEzCO,sBAAoB,SAApBA,uBAAuB;AACnB,UAAI,KAAKhE,eAAe;AACpB,aAAKA,cAAcgE,qBAAoB;MAC3C;;IAEJ3D,oBAAkB,SAAlBA,qBAAqB;AAAA,UAAA0E,SAAA;AACjB,UAAI,CAAC,KAAK9E,gBAAgB;AACtB,aAAKA,iBAAiB,WAAM;AACxB,cAAI8E,OAAKvF,kBAAkB,CAACwF,cAAa,GAAI;AACzCD,mBAAKjE,KAAI;UACb;;AAGJmE,eAAON,iBAAiB,UAAU,KAAK1E,cAAc;MACzD;;IAEJO,sBAAoB,SAApBA,uBAAuB;AACnB,UAAI,KAAKP,gBAAgB;AACrBgF,eAAOL,oBAAoB,UAAU,KAAK3E,cAAc;AACxD,aAAKA,iBAAiB;MAC1B;;IAEJ1C,SAAAA,SAAAA,SAAQvB,OAAM;AACV,aAAO,OAAOA,MAAKuB,YAAY,aAAavB,MAAKuB,QAAO,IAAKvB,MAAKuB,YAAY;;IAElFP,UAAAA,SAAAA,UAAShB,OAAM;AACX,aAAO,OAAOA,MAAKgB,aAAa,aAAahB,MAAKgB,SAAQ,IAAKhB,MAAKgB;;IAExEQ,OAAAA,SAAAA,OAAMxB,OAAM;AACR,aAAO,OAAOA,MAAKwB,UAAU,aAAaxB,MAAKwB,MAAK,IAAKxB,MAAKwB;;IAElE0H,gBAAAA,SAAAA,eAAehI,OAAO;AAClBiI,sBAAgBC,KAAK,iBAAiB;QAClChI,eAAeF;QACf8B,QAAQ,KAAKA;MACjB,CAAC;;IAELqG,cAAAA,SAAAA,aAAalC,IAAI;AACb,WAAKjD,YAAYiD;;IAErBmC,SAAAA,SAAAA,QAAQnC,IAAI;AACR,WAAKhD,OAAOgD;IAChB;;EAEJoC,UAAU;IACNpJ,iBAAe,SAAfA,kBAAkB;AACd,aAAO,KAAKsD,uBAAuB,KAAK,KAAKA,qBAAqB;IACtE;;EAEJ+F,YAAY;IACRC,YAAYC;IACZC,QAAQA;EACZ;AACJ;;;;;;;sBCrYIzG,YA8DQ0G,mBAAA;IA9DC7K,UAAUuD,KAAQvD;IAAGiC,UAAQ,CAAGsB,KAAK5D;;uBAC1C,WAAA;AAAA,aA4DY,CA5DZmL,YA4DYC,YA5DZ1H,WA4DY;QA5DA7D,MAAK;QAAuB2I,SAAOjF,SAAOiF;QAAGa,SAAO9F,SAAO8F;QAAGE,cAAahG,SAAYgG;SAAU3F,KAAG1B,IAAA,YAAA,CAAA,GAAA;2BAC5G,WAAA;AAAA,iBA0DK,EA1DM0B,KAAA5D,QAAQqL,MAAevG,iBAAA,SAAlCtB,UAAA,GAAAC,mBA0DK,OA1DLC,WA0DK;;YA1DsC4H,KAAK/H,SAAYoH;YAAGnJ,IAAI6J,MAAE7J;YAAG,SAAOoC,KAAEV,GAAA,MAAA;YAAWa,SAAK,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;qBAAER,SAAciH,kBAAAjH,SAAAiH,eAAAe,MAAAhI,UAAAiI,SAAA;;aAAU5H,KAAI6H,KAAA,MAAA,CAAA,GAAA,CAChH7H,KAAA8H,OAAOC,SAAlBnI,UAAA,GAAAC,mBAEK,OAFLC,WAEK;;YAFqB,SAAOE,KAAEV,GAAA,OAAA;aAAmBU,KAAG1B,IAAA,OAAA,CAAA,GAAA,CACrD0J,WAAyBhI,KAAA8H,QAAA,OAAA,CAAA,GAAA,EAAA,KAAA,mBAAA,IAAA,IAAA,GAE7B5H,gBAkDI,MAlDJJ,WAkDI;YAjDC4H,KAAK/H,SAAOqH;YACZpJ,IAAI6J,MAAC7J,KAAA;YACL,SAAOoC,KAAEV,GAAA,MAAA;YACVW,MAAK;YACJlD,UAAUiD,KAAQjD;YAClB,yBAAuB0K,MAAAjJ,UAAUmB,SAAA9B,kBAAkBM;YACnD,cAAY6B,KAAShD;YACrB,mBAAiBgD,KAAc/C;YAC/BgL,SAAK,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;qBAAEtI,SAAW+C,eAAA/C,SAAA+C,YAAAiF,MAAAhI,UAAAiI,SAAA;YAAA;YAClBM,QAAI,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;qBAAEvI,SAAUiD,cAAAjD,SAAAiD,WAAA+E,MAAAhI,UAAAiI,SAAA;YAAA;YAChBO,WAAO,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;qBAAExI,SAAakD,iBAAAlD,SAAAkD,cAAA8E,MAAAhI,UAAAiI,SAAA;;aACf5H,KAAG1B,IAAA,MAAA,CAAA,GAAA,EAEXsB,UAAA,IAAA,GAAAC,mBAmCUuI,UAnCoB,MAAAC,WAAArI,KAAAzD,OAAZ,SAAAmB,OAAM4K,GAAC;;cAAkBjK,KAAAsB,SAAAT,MAAMxB,KAAI,IAAI4K,EAAEC,SAAQ;gBAC/C7K,MAAK8K,SAAS7I,SAAAV,QAAQvB,KAAI,KAAA,CAAMA,MAAK+K,aAAS,UAAA,GAA9D5I,mBAmBUuI,UAAA;cAAA/J,KAAA;YAAA,GAAA,CAlBIX,MAAK8K,SAAf5I,UAAA,GAAAC,mBAGI,MAHJC,WAGI;;cAHmBlC,IAAI6J,MAAC7J,KAAA,MAAU0K;cAAI,SAAQ,CAAAtI,KAAAV,GAAoB,cAAA,GAAA5B,MAAI,OAAA,CAAM;cAAGuC,MAAK;;eAAeD,KAAG1B,IAAA,cAAA,CAAA,GAAA,CAEtG0J,WAAyGhI,KAAA8H,QAA5F9H,KAAAA,OAAO0I,eAAa,iBAAA,iBAAA;cAAqChL,MAAMA;YAAI,GAAhF,WAAA;AAAA,qBAAyG,CAApBiL,gBAAAC,gBAAAjJ,SAAAT,MAAMxB,KAAI,CAAA,GAAA,CAAA,CAAA;mEAEnGkC,UAAA,IAAA,GAAAC,mBAaUuI,UAAAA,MAAAA,WAbqB1K,MAAK8K,OAAlB,SAAAK,OAAOC,GAAC;;gBAAuBzK,KAAAwK,MAAM3J,QAAQoJ,IAAE,MAAQQ;kBAE3DnJ,SAAAV,QAAQ4J,KAAK,KAAM,CAAAA,MAAMJ,aAAS,UAAA,GAD5C7H,YAUCmI,uBAAA;;gBARInL,IAAI6J,MAAA7J,KAAW,MAAA0K,IAAE,MAAQQ;gBACzBpL,MAAMmL;gBACNlL,WAAWqC,KAAM8H;gBACjBjK,iBAAiB8B,SAAe9B;gBAChCmL,UAAUhJ,KAAQgJ;gBAClBrK,aAAYgB,SAAS4C;gBACrB0G,iBAAgBtJ,SAAa8C;gBAC7ByG,IAAIlJ,KAAEkJ;iIAEIvJ,SAAAV,QAAQ4J,KAAK,KAAKA,MAAMJ,aAAvC7I,UAAA,GAAAC,mBAAwL,MAAxLC,WAAwL;gBAArIzB,KAAG,cAAgBiK,IAAIQ;gBAAI,SAAQ,CAAA9I,KAAAV,GAAiB,WAAA,GAAA5B,MAAI,OAAA,CAAM;gBAAIR,OAAO2L,MAAM3L;gBAAO+C,MAAK;;iBAAoBD,KAAG1B,IAAA,WAAA,CAAA,GAAA,MAAA,EAAA,KAAA,mBAAA,IAAA,IAAA,CAAA,GAAA,EAAA;+BAG9JqB,SAAAV,QAAQvB,KAAI,KAAKA,MAAK+K,aAArC7I,UAAA,GAAAC,mBAA4L,MAA5LC,WAA4L;cAA3IzB,KAAG,cAAgBiK,EAAEC,SAAQ;cAAK,SAAQ,CAAAvI,KAAAV,GAAiB,WAAA,GAAA5B,MAAI,OAAA,CAAM;cAAIR,OAAOQ,MAAKR;cAAO+C,MAAK;;eAAoBD,KAAG1B,IAAA,WAAA,CAAA,GAAA,MAAA,EAAA,MAAA,UAAA,GACzKsC,YAYCmI,uBAAA;cAVI1K,KAAKsB,SAAKT,MAACxB,KAAI,IAAI4K,EAAEC,SAAQ;cAC7B3K,IAAI6J,MAAG7J,KAAA,MAAQ0K;cACf5K,MAAMA;cACNI,OAAOwK;cACP3K,WAAWqC,KAAM8H;cACjBjK,iBAAiB8B,SAAe9B;cAChCmL,UAAUhJ,KAAQgJ;cAClBrK,aAAYgB,SAAS4C;cACrB0G,iBAAgBtJ,SAAa8C;cAC7ByG,IAAIlJ,KAAEkJ;;uCAIRlJ,KAAA8H,OAAOqB,OAAlBvJ,UAAA,GAAAC,mBAEK,OAFLC,WAEK;;YAFmB,SAAOE,KAAEV,GAAA,KAAA;aAAiBU,KAAG1B,IAAA,KAAA,CAAA,GAAA,CACjD0J,WAAuBhI,KAAA8H,QAAA,KAAA,CAAA,GAAA,EAAA,KAAA,mBAAA,IAAA,IAAA,CAAA,GAAA,IAAA,UAAA,KAAA,mBAAA,IAAA,IAAA,CAAA;;;;;;;;;", "names": ["theme", "_ref", "dt", "concat", "classes", "root", "_ref2", "props", "popup", "start", "list", "submenuLabel", "separator", "end", "item", "_ref3", "instance", "id", "focusedOptionId", "disabled", "itemContent", "itemLink", "itemIcon", "itemLabel", "BaseStyle", "extend", "name", "name", "BaseComponent", "props", "popup", "type", "Boolean", "model", "Array", "appendTo", "String", "Object", "autoZIndex", "baseZIndex", "Number", "tabindex", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "style", "MenuStyle", "provide", "$pcMenu", "$parentInstance", "hostName", "inheritAttrs", "emits", "item", "templates", "id", "focusedOptionId", "index", "methods", "getItemProp", "processedItem", "resolve", "undefined", "getPTOptions", "key", "ptm", "context", "focused", "isItemFocused", "disabled", "onItemClick", "event", "command", "originalEvent", "$emit", "onItemMouseMove", "visible", "label", "getMenuItemProps", "action", "mergeProps", "cx", "icon", "directives", "ripple", "<PERSON><PERSON><PERSON>", "$options", "_openBlock", "_createElementBlock", "_mergeProps", "$props", "_ctx", "role", "_createElementVNode", "onClick", "_cache", "$event", "onMousemove", "_withDirectives", "href", "url", "target", "itemicon", "_createBlock", "_resolveDynamicComponent", "script", "BaseMenu", "data", "$attrs", "overlayVisible", "focusedOptionIndex", "selectedOptionIndex", "watch", "$attrsId", "newValue", "UniqueComponentId", "outsideClickListener", "<PERSON><PERSON><PERSON><PERSON>", "resizeListener", "container", "list", "mounted", "bindResizeListener", "bindOutsideClickListener", "beforeUnmount", "unbindResizeListener", "unbindOutsideClickListener", "destroy", "ZIndex", "clear", "itemClick", "hide", "itemMouseMove", "onListFocus", "changeFocusedOptionIndex", "onListBlur", "onListKeyDown", "code", "onArrowDownKey", "onArrowUpKey", "onHomeKey", "onEndKey", "onEnterKey", "onSpaceKey", "focus", "optionIndex", "findNextOptionIndex", "preventDefault", "altKey", "findPrevOptionIndex", "find", "length", "element", "findSingle", "concat", "anchorElement", "click", "links", "matchedOptionIndex", "_toConsumableArray", "findIndex", "link", "order", "getAttribute", "toggle", "show", "currentTarget", "onEnter", "el", "addStyle", "position", "top", "left", "alignOverlay", "bindScrollListener", "set", "$primevue", "config", "zIndex", "menu", "onLeave", "unbindScrollListener", "onAfterLeave", "absolutePosition", "targetWidth", "getOuterWidth", "min<PERSON><PERSON><PERSON>", "_this", "isOutsideContainer", "contains", "isOutsideTarget", "document", "addEventListener", "removeEventListener", "_this2", "ConnectedOverlayScrollHandler", "_this3", "isTouchDevice", "window", "onOverlayClick", "OverlayEventBus", "emit", "containerRef", "listRef", "computed", "components", "PVMenuitem", "<PERSON><PERSON><PERSON>", "Portal", "_component_Portal", "_createVNode", "_Transition", "$data", "ref", "apply", "arguments", "ptmi", "$slots", "start", "_renderSlot", "onFocus", "onBlur", "onKeydown", "_Fragment", "_renderList", "i", "toString", "items", "separator", "submenulabel", "_createTextVNode", "_toDisplayString", "child", "j", "_component_PVMenuitem", "unstyled", "onItemMousemove", "pt", "end"]}