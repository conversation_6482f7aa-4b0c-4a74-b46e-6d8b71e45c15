
# This file will contain authentication service logic
import logging
import os
import jwt
from typing import Dict, Any, Optional
import httpx
from api_docs_server.constants import TAIHU_PRODUCTION_CONFIG

logger = logging.getLogger(__name__)

class AuthManager:
    """Handles authentication with various providers including Taihu production environment."""
    
    @staticmethod
    def get_taihu_production_auth_url() -> str:
        """Get the authorization URL for Taihu production environment."""
        config = TAIHU_PRODUCTION_CONFIG
        # Construct auth URL with required parameters
        return (
            f"{config['authorization_url']}?"
            f"client_id={config['client_id']}&"
            f"redirect_uri={config['callback_url']}&"
            f"response_type=code&"
            f"scope={'+'.join(config['scope'])}&"
            f"state={{state}}"  # State will be replaced in the route
        )
    
    @staticmethod
    async def authenticate_with_taihu_production(code: str) -> Dict[str, Any]:
        """Authenticate with Taihu production environment using authorization code."""
        config = TAIHU_PRODUCTION_CONFIG
        
        # Exchange authorization code for tokens
        token_data = {
            "grant_type": "authorization_code",
            "code": code,
            "redirect_uri": config["callback_url"],
            "client_id": config["client_id"],
            "client_secret": config["client_secret"]
        }
        
        # Use httpx to make the request
        async with httpx.AsyncClient() as client:
            # Get token
            token_response = await client.post(
                config["token_url"],
                data=token_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if not token_response.is_success:
                raise Exception(f"Failed to get token: {token_response.text}")
            
            token_json = token_response.json()
            access_token = token_json.get("access_token")
            
            if not access_token:
                raise Exception("No access token in response")
            
            # Get user info
            userinfo_response = await client.get(
                config["userinfo_url"],
                headers={"Authorization": f"Bearer {access_token}"}
            )
            
            if not userinfo_response.is_success:
                raise Exception(f"Failed to get user info: {userinfo_response.text}")
            
            return userinfo_response.json()