/**
 * API文档类型定义
 */
export interface ApiDoc {
    id: number;
    url: string;
    title?: string;
    description?: string;
    icon_url?: string;
    icon_data?: string;
    auto_metadata: boolean;
    view_count: number;
    created_at: string;
    updated_at: string;
    metadata_updated_at?: string;
}

/**
 * API文档创建参数
 */
export interface ApiDocCreate {
    url: string;
    title?: string;
    description?: string;
    icon_url?: string;
    icon_data?: string;
    auto_metadata?: boolean;
}

/**
 * API文档更新参数
 */
export interface ApiDocUpdate {
    url?: string;
    title?: string;
    description?: string;
    icon_url?: string;
    icon_data?: string;
    auto_metadata?: boolean;
}

/**
 * API响应类型
 */
export interface ApiResponse<T> {
    data: T;
    message?: string;
}

/**
 * API错误响应
 */
export interface ApiError {
    message: string;
    detail?: string;
}
