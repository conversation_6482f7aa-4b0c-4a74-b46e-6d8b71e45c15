"""Authentication routes for Taihu account login."""
from fastapi import APIRouter, HTTPException, Request, Depends
import httpx
import logging
from pydantic import BaseModel
from api_docs_server.middleware.auth import verify_token

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(tags=["auth"])

class TaihuLoginRequest(BaseModel):
    """Request model for Taihu login."""
    login_name: str
    password: str
    code: str

@router.get("/callback")
async def taihu_callback(request: Request):
    """Handle Taihu OAuth2.0 callback."""
    try:
        code = request.query_params.get("code")
        if not code:
            raise HTTPException(status_code=400, detail="Missing authorization code")

        # 获取环境变量或使用默认值
        import os
        from dotenv import load_dotenv
        load_dotenv()

        client_id = os.getenv("TAIHU_CLIENT_ID", "test-app")
        client_secret = os.getenv("TAIHU_CLIENT_SECRET", "ilX3uqWyJRbK")
        auth_url = os.getenv("TAIHU_AUTH_URL", "https://test-odc.it.woa.com/api/auth-center/oauth2")
        redirect_uri = os.getenv("TAIHU_REDIRECT_URI", "http://localhost:8000/api/auth/callback")

        logger.info(f"Exchanging code for token with redirect_uri: {redirect_uri}")

        # 使用授权码换取访问令牌
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{auth_url}/token",
                data={
                    "grant_type": "authorization_code",
                    "code": code,
                    "client_id": client_id,
                    "client_secret": client_secret,
                    "redirect_uri": redirect_uri
                },
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            response.raise_for_status()
            data = response.json()

            logger.info(f"Token exchange response status: {response.status_code}")

            # 验证令牌并完成登录
            if data.get("access_token"):
                # 将令牌保存到响应的cookie中
                response_data = {"status": "success", "access_token": data["access_token"]}
                if data.get("refresh_token"):
                    response_data["refresh_token"] = data["refresh_token"]
                return response_data
            else:
                logger.error(f"Failed to exchange token: {data}")
                raise HTTPException(status_code=400, detail="Failed to exchange token")

    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error during token exchange: {str(e)}")
        raise HTTPException(status_code=e.response.status_code, detail="Failed to connect to Taihu service")
    except Exception as e:
        logger.error(f"Unexpected error during token exchange: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/login")
async def taihu_login(request: TaihuLoginRequest):
    """Handle Taihu account login."""
    try:
        # 调用太湖登录接口
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://test-odc.it.woa.com/ebus/openapi/odc-auth/v1/auth/password/login/verify",
                json={
                    "login_name": request.login_name,
                    "password": request.password,
                    "code": request.code
                },
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            data = response.json()

            # 检查登录结果
            if data.get("ErrCode") == 0:
                return {"status": "success", "redirect_url": data.get("Data", {}).get("redirect_url")}
            else:
                logger.error(f"Taihu login failed: {data.get('ErrMsg')}")
                raise HTTPException(status_code=400, detail=data.get("ErrMsg"))

    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error during Taihu login: {str(e)}")
        raise HTTPException(status_code=e.response.status_code, detail="Failed to connect to Taihu service")
    except Exception as e:
        logger.error(f"Unexpected error during Taihu login: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/user")
async def get_user_info(token_data=Depends(verify_token)):
    """获取当前登录用户信息."""
    try:
        # 从令牌数据中提取用户信息
        user_info = {
            "id": token_data.get("sub"),
            "name": token_data.get("name", "Unknown User"),
            "email": token_data.get("email"),
            "roles": token_data.get("roles", []),
            "permissions": token_data.get("permissions", [])
        }
        return user_info
    except Exception as e:
        logger.error(f"Error retrieving user info: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve user information")