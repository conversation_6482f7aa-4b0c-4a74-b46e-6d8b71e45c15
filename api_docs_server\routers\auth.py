
# This file will contain authentication routes including Taihu production environment login
import logging
from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import RedirectResponse
from api_docs_server.services.auth import AuthManager

logger = logging.getLogger(__name__)
router = APIRouter(tags=["auth"])

@router.get("/auth/taihu-production")
async def taihu_production_login(request: Request):
    """Initiate Taihu production environment login."""
    # Get auth URL from AuthManager
    auth_url = AuthManager.get_taihu_production_auth_url()
    
    # Store state in session if needed
    if "state" in request.query_params:
        request.session["taihu_production_state"] = request.query_params["state"]
    
    # Redirect to Taihu production auth URL
    return RedirectResponse(url=auth_url)

@router.get("/auth/taihu-production/callback")
async def taihu_production_callback(code: str, state: str, request: Request):
    """Handle callback from Taihu production environment login."""
    try:
        # Verify state if needed
        if "taihu_production_state" in request.session and request.session["taihu_production_state"] != state:
            raise HTTPException(status_code=400, detail="Invalid state parameter")
        
        # Get user info from AuthManager using the authorization code
        user_info = await AuthManager.authenticate_with_taihu_production(code)
        
        # Store user info in session
        request.session["user"] = user_info
        
        # Redirect to profile page or frontend
        return RedirectResponse(url="/profile")
    except Exception as e:
        logger.error(f"Taihu production callback failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Taihu production authentication failed")

@router.get("/profile")
async def get_profile(request: Request):
    """Get current user profile."""
    if "user" not in request.session:
        raise HTTPException(status_code=401, detail="Not authenticated")
    
    return request.session["user"]

@router.post("/logout")
async def logout(request: Request):
    """Logout current user."""
    # Clear user info from session
    request.session.pop("user", None)
    return {"message": "Logged out"}