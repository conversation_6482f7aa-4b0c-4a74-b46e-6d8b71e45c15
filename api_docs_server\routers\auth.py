"""Authentication routes for Taihu account login."""
from fastapi import APIRouter, HTTPException, Request, Depends
import httpx
import logging
from pydantic import BaseModel
from api_docs_server.middleware.auth import verify_token

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(tags=["auth"])

class TaihuLoginRequest(BaseModel):
    """Request model for Taihu login."""
    login_name: str
    password: str
    code: str

@router.get("/taihu_callback")
async def taihu_callback(request: Request):
    """Handle Taihu OAuth2.0 callback."""
    try:
        code = request.query_params.get("code")
        state = request.query_params.get("state")
        error = request.query_params.get("error")

        # 获取环境变量
        import os
        from dotenv import load_dotenv
        load_dotenv()

        frontend_url = os.getenv("FRONTEND_URL", "http://localhost:3000")

        # 如果有错误参数，重定向到前端错误页面
        if error:
            from fastapi.responses import RedirectResponse
            redirect_url = f"{frontend_url}/#/auth/callback?error={error}"
            return RedirectResponse(url=redirect_url)

        if not code:
            from fastapi.responses import RedirectResponse
            redirect_url = f"{frontend_url}/#/auth/callback?error=missing_code"
            return RedirectResponse(url=redirect_url)

        client_id = os.getenv("TAIHU_CLIENT_ID", "test-app")
        client_secret = os.getenv("TAIHU_CLIENT_SECRET", "ilX3uqWyJRbK")
        auth_url = os.getenv("TAIHU_AUTH_URL", "https://test-odc.it.woa.com/api/auth-center/oauth2")
        redirect_uri = os.getenv("TAIHU_REDIRECT_URI", "https://api-docs.woa.com/taihu_callback")

        logger.info(f"Exchanging code for token with redirect_uri: {redirect_uri}")

        # 使用授权码换取访问令牌
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{auth_url}/token",
                data={
                    "grant_type": "authorization_code",
                    "code": code,
                    "client_id": client_id,
                    "client_secret": client_secret,
                    "redirect_uri": redirect_uri
                },
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            response.raise_for_status()
            data = response.json()

            logger.info(f"Token exchange response status: {response.status_code}")

            # 验证令牌并完成登录
            if data.get("access_token"):
                # 重定向到前端回调页面，携带token信息
                from fastapi.responses import RedirectResponse
                from urllib.parse import urlencode

                params = {
                    "code": code,
                    "access_token": data["access_token"]
                }
                if state:
                    params["state"] = state
                if data.get("refresh_token"):
                    params["refresh_token"] = data["refresh_token"]

                callback_url = f"{frontend_url}/#/auth/callback?{urlencode(params)}"
                return RedirectResponse(url=callback_url)
            else:
                logger.error(f"Failed to exchange token: {data}")
                from fastapi.responses import RedirectResponse
                error_url = f"{frontend_url}/#/auth/callback?error=token_exchange_failed"
                return RedirectResponse(url=error_url)

    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error during token exchange: {str(e)}")
        raise HTTPException(status_code=e.response.status_code, detail="Failed to connect to Taihu service")
    except Exception as e:
        logger.error(f"Unexpected error during token exchange: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/login")
async def taihu_login(request: TaihuLoginRequest):
    """Handle Taihu account login."""
    try:
        # 调用太湖登录接口
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://test-odc.it.woa.com/ebus/openapi/odc-auth/v1/auth/password/login/verify",
                json={
                    "login_name": request.login_name,
                    "password": request.password,
                    "code": request.code
                },
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            data = response.json()

            # 检查登录结果
            if data.get("ErrCode") == 0:
                return {"status": "success", "redirect_url": data.get("Data", {}).get("redirect_url")}
            else:
                logger.error(f"Taihu login failed: {data.get('ErrMsg')}")
                raise HTTPException(status_code=400, detail=data.get("ErrMsg"))

    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error during Taihu login: {str(e)}")
        raise HTTPException(status_code=e.response.status_code, detail="Failed to connect to Taihu service")
    except Exception as e:
        logger.error(f"Unexpected error during Taihu login: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/user")
async def get_user_info(token_data=Depends(verify_token)):
    """获取当前登录用户信息."""
    try:
        # 从令牌数据中提取用户信息
        user_info = {
            "id": token_data.get("sub"),
            "name": token_data.get("name", "Unknown User"),
            "email": token_data.get("email"),
            "roles": token_data.get("roles", []),
            "permissions": token_data.get("permissions", [])
        }
        return user_info
    except Exception as e:
        logger.error(f"Error retrieving user info: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve user information")