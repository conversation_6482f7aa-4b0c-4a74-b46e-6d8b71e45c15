{"version": 3, "sources": ["../../@primeuix/src/eventbus/index.ts"], "sourcesContent": ["export interface EventBusOptions {\n    on(type: string, handler: Function): void;\n    off(type: string, handler: Function): void;\n    emit(type: string, evt?: any): void;\n    clear(): void;\n}\n\nexport function EventBus(): EventBusOptions {\n    const allHandlers = new Map<string, Function[]>();\n\n    return {\n        on(type: string, handler: Function) {\n            let handlers = allHandlers.get(type);\n\n            if (!handlers) handlers = [handler];\n            else handlers.push(handler);\n\n            allHandlers.set(type, handlers);\n\n            return this;\n        },\n        off(type: string, handler: Function) {\n            let handlers = allHandlers.get(type);\n\n            if (handlers) {\n                handlers.splice(handlers.indexOf(handler) >>> 0, 1);\n            }\n\n            return this;\n        },\n        emit(type: string, evt?: any) {\n            let handlers = allHandlers.get(type);\n\n            if (handlers) {\n                handlers.slice().map((handler) => {\n                    handler(evt);\n                });\n            }\n        },\n        clear() {\n            allHandlers.clear();\n        }\n    };\n}\n"], "mappings": ";AAOO,SAAS,WAA4B;AACxC,QAAM,cAAc,oBAAI,IAAwB;AAEhD,SAAO;IACH,GAAG,MAAc,SAAmB;AAChC,UAAI,WAAW,YAAY,IAAI,IAAI;AAEnC,UAAI,CAAC,SAAU,YAAW,CAAC,OAAO;UAC7B,UAAS,KAAK,OAAO;AAE1B,kBAAY,IAAI,MAAM,QAAQ;AAE9B,aAAO;IACX;IACA,IAAI,MAAc,SAAmB;AACjC,UAAI,WAAW,YAAY,IAAI,IAAI;AAEnC,UAAI,UAAU;AACV,iBAAS,OAAO,SAAS,QAAQ,OAAO,MAAM,GAAG,CAAC;MACtD;AAEA,aAAO;IACX;IACA,KAAK,MAAc,KAAW;AAC1B,UAAI,WAAW,YAAY,IAAI,IAAI;AAEnC,UAAI,UAAU;AACV,iBAAS,MAAM,EAAE,IAAI,CAAC,YAAY;AAC9B,kBAAQ,GAAG;QACf,CAAC;MACL;IACJ;IACA,QAAQ;AACJ,kBAAY,MAAM;IACtB;EACJ;AACJ;", "names": []}