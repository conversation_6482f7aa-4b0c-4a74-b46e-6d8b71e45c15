<template>
  <Dialog
    v-model:visible="visible"
    modal
    :style="{ width: '90vw', maxWidth: '1200px' }"
    :maximizable="true"
    :dismissableMask="true"
  >
    <template #header>
      <div class="flex align-items-center gap-2">
        <span class="text-xl font-bold">{{ doc?.title }}</span>
        <Button
          v-if="doc"
          icon="pi pi-external-link"
          link
          @click="openInNewTab"
        />
      </div>
    </template>

    <iframe
      v-if="doc"
      :src="doc.url"
      class="w-full"
      style="height: 80vh"
      :title="doc.title"
    ></iframe>

    <template #footer>
      <div class="flex justify-content-end gap-2">
        <Button
          label="关闭"
          icon="pi pi-times"
          @click="closeDialog"
          text
        />
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import Dialog from 'primevue/dialog'
import But<PERSON> from 'primevue/button'
import type { ApiDoc } from '@/types/api'

const props = defineProps<{
  modelValue: boolean
  doc: ApiDoc | null
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const openInNewTab = () => {
  if (!props.doc?.url) {
    return
  }

  try {
    // 尝试创建 URL 对象来验证 URL 格式
    const url = new URL(props.doc.url)
    // 检查是否是绝对路径
    if (url.protocol === 'http:' || url.protocol === 'https:') {
      window.open(props.doc.url, '_blank', 'noopener,noreferrer')
    } else {
      console.warn('Invalid URL protocol:', url.protocol)
    }
  } catch (error) {
    console.error('Invalid URL:', props.doc.url)
  }
}

const closeDialog = () => {
  visible.value = false
}
</script>
