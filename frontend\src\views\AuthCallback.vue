<template>
  <div class="auth-callback-container">
    <div class="flex flex-column align-items-center justify-content-center min-h-screen">
      <div class="card p-4 shadow-2 border-round w-full max-w-md">
        <div class="text-center mb-4">
          <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
          <h2 class="text-xl font-semibold text-900 mb-2">{{ $t('auth.processing') }}</h2>
          <p class="text-600 m-0">{{ $t('auth.processingDescription') }}</p>
        </div>

        <!-- 错误状态 -->
        <div v-if="error" class="text-center">
          <i class="pi pi-exclamation-triangle text-4xl text-red-500 mb-3"></i>
          <h3 class="text-lg font-semibold text-900 mb-2">{{ $t('auth.error') }}</h3>
          <p class="text-600 mb-4">{{ error }}</p>
          <Button
            :label="$t('auth.backToLogin')"
            @click="goToLogin"
            class="w-full"
            severity="secondary"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'
import { useToast } from 'primevue/usetoast'
import Button from 'primevue/button'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const toast = useToast()

const error = ref<string | null>(null)

onMounted(async () => {
  try {
    // 获取URL中的参数
    const code = route.query.code as string
    const state = route.query.state as string
    const accessToken = route.query.access_token as string
    const errorParam = route.query.error as string

    if (errorParam) {
      error.value = `认证失败: ${errorParam}`
      return
    }

    if (!code) {
      error.value = '缺少授权码，请重新登录'
      return
    }

    // 处理回调，如果有access_token则直接使用
    const success = await authStore.handleCallback(code, accessToken)

    if (success) {
      toast.add({
        severity: 'success',
        summary: '登录成功',
        detail: '欢迎回来！',
        life: 3000
      })

      // 获取重定向目标，优先使用state参数，然后是query参数，最后默认首页
      const redirect = state || (route.query.redirect as string) || '/'
      await router.push(redirect)
    } else {
      error.value = '登录处理失败，请重新尝试'
    }
  } catch (err) {
    console.error('Auth callback error:', err)
    error.value = '登录过程中发生错误，请重新尝试'
  }
})

const goToLogin = () => {
  router.push('/login')
}
</script>

<style scoped>
.auth-callback-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}
</style>
