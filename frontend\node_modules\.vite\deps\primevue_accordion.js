import {
  script as script3
} from "./chunk-LTIBOJKI.js";
import {
  UniqueComponentId
} from "./chunk-VMI3R23S.js";
import {
  Ripple
} from "./chunk-J6MP5JJN.js";
import "./chunk-7MNOBW2X.js";
import {
  script as script2
} from "./chunk-I7CE2OFZ.js";
import "./chunk-CXETA7UJ.js";
import "./chunk-47K6FRN7.js";
import "./chunk-JKGRDULH.js";
import {
  script
} from "./chunk-IDNK5ZN5.js";
import "./chunk-B73TEBOQ.js";
import {
  BaseStyle,
  findSingle,
  focus,
  getAttribute
} from "./chunk-TZR35JV3.js";
import "./chunk-LQERBOIJ.js";
import {
  Fragment,
  Transition,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createVNode,
  mergeProps,
  normalizeClass,
  openBlock,
  renderList,
  renderSlot,
  resolveComponent,
  resolveDirective,
  resolveDynamicComponent,
  toDisplayString,
  vShow,
  withCtx,
  withDirectives
} from "./chunk-U3LI7FBV.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/@primevue/icons/chevronright/index.mjs
var script4 = {
  name: "ChevronRightIcon",
  "extends": script2
};
function render(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("svg", mergeProps({
    width: "14",
    height: "14",
    viewBox: "0 0 14 14",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
  }, _ctx.pti()), _cache[0] || (_cache[0] = [createBaseVNode("path", {
    d: "M4.38708 13C4.28408 13.0005 4.18203 12.9804 4.08691 12.9409C3.99178 12.9014 3.9055 12.8433 3.83313 12.7701C3.68634 12.6231 3.60388 12.4238 3.60388 12.2161C3.60388 12.0084 3.68634 11.8091 3.83313 11.6622L8.50507 6.99022L3.83313 2.31827C3.69467 2.16968 3.61928 1.97313 3.62287 1.77005C3.62645 1.56698 3.70872 1.37322 3.85234 1.22959C3.99596 1.08597 4.18972 1.00371 4.3928 1.00012C4.59588 0.996539 4.79242 1.07192 4.94102 1.21039L10.1669 6.43628C10.3137 6.58325 10.3962 6.78249 10.3962 6.99022C10.3962 7.19795 10.3137 7.39718 10.1669 7.54416L4.94102 12.7701C4.86865 12.8433 4.78237 12.9014 4.68724 12.9409C4.59212 12.9804 4.49007 13.0005 4.38708 13Z",
    fill: "currentColor"
  }, null, -1)]), 16);
}
script4.render = render;

// node_modules/@primevue/icons/chevronup/index.mjs
var script5 = {
  name: "ChevronUpIcon",
  "extends": script2
};
function render2(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("svg", mergeProps({
    width: "14",
    height: "14",
    viewBox: "0 0 14 14",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
  }, _ctx.pti()), _cache[0] || (_cache[0] = [createBaseVNode("path", {
    d: "M12.2097 10.4113C12.1057 10.4118 12.0027 10.3915 11.9067 10.3516C11.8107 10.3118 11.7237 10.2532 11.6506 10.1792L6.93602 5.46461L2.22139 10.1476C2.07272 10.244 1.89599 10.2877 1.71953 10.2717C1.54307 10.2556 1.3771 10.1808 1.24822 10.0593C1.11933 9.93766 1.035 9.77633 1.00874 9.6011C0.982477 9.42587 1.0158 9.2469 1.10338 9.09287L6.37701 3.81923C6.52533 3.6711 6.72639 3.58789 6.93602 3.58789C7.14565 3.58789 7.3467 3.6711 7.49502 3.81923L12.7687 9.09287C12.9168 9.24119 13 9.44225 13 9.65187C13 9.8615 12.9168 10.0626 12.7687 10.2109C12.616 10.3487 12.4151 10.4207 12.2097 10.4113Z",
    fill: "currentColor"
  }, null, -1)]), 16);
}
script5.render = render2;

// node_modules/primevue/accordioncontent/style/index.mjs
var classes = {
  root: "p-accordioncontent",
  content: "p-accordioncontent-content"
};
var AccordionContentStyle = BaseStyle.extend({
  name: "accordioncontent",
  classes
});

// node_modules/primevue/accordioncontent/index.mjs
var script$1 = {
  name: "BaseAccordionContent",
  "extends": script,
  props: {
    as: {
      type: [String, Object],
      "default": "DIV"
    },
    asChild: {
      type: Boolean,
      "default": false
    }
  },
  style: AccordionContentStyle,
  provide: function provide() {
    return {
      $pcAccordionContent: this,
      $parentInstance: this
    };
  }
};
var script6 = {
  name: "AccordionContent",
  "extends": script$1,
  inheritAttrs: false,
  inject: ["$pcAccordion", "$pcAccordionPanel"],
  computed: {
    id: function id() {
      return "".concat(this.$pcAccordion.id, "_accordioncontent_").concat(this.$pcAccordionPanel.value);
    },
    ariaLabelledby: function ariaLabelledby() {
      return "".concat(this.$pcAccordion.id, "_accordionheader_").concat(this.$pcAccordionPanel.value);
    },
    attrs: function attrs() {
      return mergeProps(this.a11yAttrs, this.ptmi("root", this.ptParams));
    },
    a11yAttrs: function a11yAttrs() {
      return {
        id: this.id,
        role: "region",
        "aria-labelledby": this.ariaLabelledby,
        "data-pc-name": "accordioncontent",
        "data-p-active": this.$pcAccordionPanel.active
      };
    },
    ptParams: function ptParams() {
      return {
        context: {
          active: this.$pcAccordionPanel.active
        }
      };
    }
  }
};
function render3(_ctx, _cache, $props, $setup, $data, $options) {
  return !_ctx.asChild ? (openBlock(), createBlock(Transition, mergeProps({
    key: 0,
    name: "p-toggleable-content"
  }, _ctx.ptm("transition", $options.ptParams)), {
    "default": withCtx(function() {
      return [($options.$pcAccordion.lazy ? $options.$pcAccordionPanel.active : true) ? withDirectives((openBlock(), createBlock(resolveDynamicComponent(_ctx.as), mergeProps({
        key: 0,
        "class": _ctx.cx("root")
      }, $options.attrs), {
        "default": withCtx(function() {
          return [createBaseVNode("div", mergeProps({
            "class": _ctx.cx("content")
          }, _ctx.ptm("content", $options.ptParams)), [renderSlot(_ctx.$slots, "default")], 16)];
        }),
        _: 3
      }, 16, ["class"])), [[vShow, $options.$pcAccordion.lazy ? true : $options.$pcAccordionPanel.active]]) : createCommentVNode("", true)];
    }),
    _: 3
  }, 16)) : renderSlot(_ctx.$slots, "default", {
    key: 1,
    "class": normalizeClass(_ctx.cx("root")),
    active: $options.$pcAccordionPanel.active,
    a11yAttrs: $options.a11yAttrs
  });
}
script6.render = render3;

// node_modules/primevue/accordionheader/style/index.mjs
var classes2 = {
  root: "p-accordionheader",
  toggleicon: "p-accordionheader-toggle-icon"
};
var AccordionHeaderStyle = BaseStyle.extend({
  name: "accordionheader",
  classes: classes2
});

// node_modules/primevue/accordionheader/index.mjs
var script$12 = {
  name: "BaseAccordionHeader",
  "extends": script,
  props: {
    as: {
      type: [String, Object],
      "default": "BUTTON"
    },
    asChild: {
      type: Boolean,
      "default": false
    }
  },
  style: AccordionHeaderStyle,
  provide: function provide2() {
    return {
      $pcAccordionHeader: this,
      $parentInstance: this
    };
  }
};
var script7 = {
  name: "AccordionHeader",
  "extends": script$12,
  inheritAttrs: false,
  inject: ["$pcAccordion", "$pcAccordionPanel"],
  methods: {
    onFocus: function onFocus() {
      this.$pcAccordion.selectOnFocus && this.changeActiveValue();
    },
    onClick: function onClick() {
      this.changeActiveValue();
    },
    onKeydown: function onKeydown(event) {
      switch (event.code) {
        case "ArrowDown":
          this.onArrowDownKey(event);
          break;
        case "ArrowUp":
          this.onArrowUpKey(event);
          break;
        case "Home":
          this.onHomeKey(event);
          break;
        case "End":
          this.onEndKey(event);
          break;
        case "Enter":
        case "NumpadEnter":
        case "Space":
          this.onEnterKey(event);
          break;
      }
    },
    onArrowDownKey: function onArrowDownKey(event) {
      var nextPanel = this.findNextPanel(this.findPanel(event.currentTarget));
      nextPanel ? this.changeFocusedPanel(event, nextPanel) : this.onHomeKey(event);
      event.preventDefault();
    },
    onArrowUpKey: function onArrowUpKey(event) {
      var prevPanel = this.findPrevPanel(this.findPanel(event.currentTarget));
      prevPanel ? this.changeFocusedPanel(event, prevPanel) : this.onEndKey(event);
      event.preventDefault();
    },
    onHomeKey: function onHomeKey(event) {
      var firstPanel = this.findFirstPanel();
      this.changeFocusedPanel(event, firstPanel);
      event.preventDefault();
    },
    onEndKey: function onEndKey(event) {
      var lastPanel = this.findLastPanel();
      this.changeFocusedPanel(event, lastPanel);
      event.preventDefault();
    },
    onEnterKey: function onEnterKey(event) {
      this.changeActiveValue();
      event.preventDefault();
    },
    findPanel: function findPanel(headerElement) {
      return headerElement === null || headerElement === void 0 ? void 0 : headerElement.closest('[data-pc-name="accordionpanel"]');
    },
    findHeader: function findHeader(panelElement) {
      return findSingle(panelElement, '[data-pc-name="accordionheader"]');
    },
    findNextPanel: function findNextPanel(panelElement) {
      var selfCheck = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;
      var element = selfCheck ? panelElement : panelElement.nextElementSibling;
      return element ? getAttribute(element, "data-p-disabled") ? this.findNextPanel(element) : this.findHeader(element) : null;
    },
    findPrevPanel: function findPrevPanel(panelElement) {
      var selfCheck = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;
      var element = selfCheck ? panelElement : panelElement.previousElementSibling;
      return element ? getAttribute(element, "data-p-disabled") ? this.findPrevPanel(element) : this.findHeader(element) : null;
    },
    findFirstPanel: function findFirstPanel() {
      return this.findNextPanel(this.$pcAccordion.$el.firstElementChild, true);
    },
    findLastPanel: function findLastPanel() {
      return this.findPrevPanel(this.$pcAccordion.$el.lastElementChild, true);
    },
    changeActiveValue: function changeActiveValue() {
      this.$pcAccordion.updateValue(this.$pcAccordionPanel.value);
    },
    changeFocusedPanel: function changeFocusedPanel(event, element) {
      focus(this.findHeader(element));
    }
  },
  computed: {
    id: function id2() {
      return "".concat(this.$pcAccordion.id, "_accordionheader_").concat(this.$pcAccordionPanel.value);
    },
    ariaControls: function ariaControls() {
      return "".concat(this.$pcAccordion.id, "_accordioncontent_").concat(this.$pcAccordionPanel.value);
    },
    attrs: function attrs2() {
      return mergeProps(this.asAttrs, this.a11yAttrs, this.ptmi("root", this.ptParams));
    },
    asAttrs: function asAttrs() {
      return this.as === "BUTTON" ? {
        type: "button",
        disabled: this.$pcAccordionPanel.disabled
      } : void 0;
    },
    a11yAttrs: function a11yAttrs2() {
      return {
        id: this.id,
        tabindex: this.$pcAccordion.tabindex,
        "aria-expanded": this.$pcAccordionPanel.active,
        "aria-controls": this.ariaControls,
        "data-pc-name": "accordionheader",
        "data-p-disabled": this.$pcAccordionPanel.disabled,
        "data-p-active": this.$pcAccordionPanel.active,
        onFocus: this.onFocus,
        onKeydown: this.onKeydown
      };
    },
    ptParams: function ptParams2() {
      return {
        context: {
          active: this.$pcAccordionPanel.active
        }
      };
    }
  },
  components: {
    ChevronUpIcon: script5,
    ChevronDownIcon: script3
  },
  directives: {
    ripple: Ripple
  }
};
function render4(_ctx, _cache, $props, $setup, $data, $options) {
  var _directive_ripple = resolveDirective("ripple");
  return !_ctx.asChild ? withDirectives((openBlock(), createBlock(resolveDynamicComponent(_ctx.as), mergeProps({
    key: 0,
    "class": _ctx.cx("root"),
    onClick: $options.onClick
  }, $options.attrs), {
    "default": withCtx(function() {
      return [renderSlot(_ctx.$slots, "default", {
        active: $options.$pcAccordionPanel.active
      }), renderSlot(_ctx.$slots, "toggleicon", {
        active: $options.$pcAccordionPanel.active,
        "class": normalizeClass(_ctx.cx("toggleicon"))
      }, function() {
        return [$options.$pcAccordionPanel.active ? (openBlock(), createBlock(resolveDynamicComponent($options.$pcAccordion.$slots.collapseicon ? $options.$pcAccordion.$slots.collapseicon : $options.$pcAccordion.collapseIcon ? "span" : "ChevronDownIcon"), mergeProps({
          key: 0,
          "class": [$options.$pcAccordion.collapseIcon, _ctx.cx("toggleicon")],
          "aria-hidden": "true"
        }, _ctx.ptm("toggleicon", $options.ptParams)), null, 16, ["class"])) : (openBlock(), createBlock(resolveDynamicComponent($options.$pcAccordion.$slots.expandicon ? $options.$pcAccordion.$slots.expandicon : $options.$pcAccordion.expandIcon ? "span" : "ChevronUpIcon"), mergeProps({
          key: 1,
          "class": [$options.$pcAccordion.expandIcon, _ctx.cx("toggleicon")],
          "aria-hidden": "true"
        }, _ctx.ptm("toggleicon", $options.ptParams)), null, 16, ["class"]))];
      })];
    }),
    _: 3
  }, 16, ["class", "onClick"])), [[_directive_ripple]]) : renderSlot(_ctx.$slots, "default", {
    key: 1,
    "class": normalizeClass(_ctx.cx("root")),
    active: $options.$pcAccordionPanel.active,
    a11yAttrs: $options.a11yAttrs,
    onClick: $options.onClick
  });
}
script7.render = render4;

// node_modules/primevue/accordionpanel/style/index.mjs
var classes3 = {
  root: function root(_ref) {
    var instance = _ref.instance, props = _ref.props;
    return ["p-accordionpanel", {
      "p-accordionpanel-active": instance.active,
      "p-disabled": props.disabled
    }];
  }
};
var AccordionPanelStyle = BaseStyle.extend({
  name: "accordionpanel",
  classes: classes3
});

// node_modules/primevue/accordionpanel/index.mjs
var script$13 = {
  name: "BaseAccordionPanel",
  "extends": script,
  props: {
    value: {
      type: [String, Number],
      "default": void 0
    },
    disabled: {
      type: Boolean,
      "default": false
    },
    as: {
      type: [String, Object],
      "default": "DIV"
    },
    asChild: {
      type: Boolean,
      "default": false
    }
  },
  style: AccordionPanelStyle,
  provide: function provide3() {
    return {
      $pcAccordionPanel: this,
      $parentInstance: this
    };
  }
};
var script8 = {
  name: "AccordionPanel",
  "extends": script$13,
  inheritAttrs: false,
  inject: ["$pcAccordion"],
  computed: {
    active: function active() {
      return this.$pcAccordion.isItemActive(this.value);
    },
    attrs: function attrs3() {
      return mergeProps(this.a11yAttrs, this.ptmi("root", this.ptParams));
    },
    a11yAttrs: function a11yAttrs3() {
      return {
        "data-pc-name": "accordionpanel",
        "data-p-disabled": this.disabled,
        "data-p-active": this.active
      };
    },
    ptParams: function ptParams3() {
      return {
        context: {
          active: this.active
        }
      };
    }
  }
};
function render5(_ctx, _cache, $props, $setup, $data, $options) {
  return !_ctx.asChild ? (openBlock(), createBlock(resolveDynamicComponent(_ctx.as), mergeProps({
    key: 0,
    "class": _ctx.cx("root")
  }, $options.attrs), {
    "default": withCtx(function() {
      return [renderSlot(_ctx.$slots, "default")];
    }),
    _: 3
  }, 16, ["class"])) : renderSlot(_ctx.$slots, "default", {
    key: 1,
    "class": normalizeClass(_ctx.cx("root")),
    active: $options.active,
    a11yAttrs: $options.a11yAttrs
  });
}
script8.render = render5;

// node_modules/primevue/accordion/style/index.mjs
var theme = function theme2(_ref) {
  var dt = _ref.dt;
  return "\n.p-accordionpanel {\n    display: flex;\n    flex-direction: column;\n    border-style: solid;\n    border-width: ".concat(dt("accordion.panel.border.width"), ";\n    border-color: ").concat(dt("accordion.panel.border.color"), ";\n}\n\n.p-accordionheader {\n    all: unset;\n    cursor: pointer;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: ").concat(dt("accordion.header.padding"), ";\n    color: ").concat(dt("accordion.header.color"), ";\n    background: ").concat(dt("accordion.header.background"), ";\n    border-style: solid;\n    border-width: ").concat(dt("accordion.header.border.width"), ";\n    border-color: ").concat(dt("accordion.header.border.color"), ";\n    font-weight: ").concat(dt("accordion.header.font.weight"), ";\n    border-radius: ").concat(dt("accordion.header.border.radius"), ";\n    transition: background ").concat(dt("accordion.transition.duration"), "; color ").concat(dt("accordion.transition.duration"), "color ").concat(dt("accordion.transition.duration"), ", outline-color ").concat(dt("accordion.transition.duration"), ", box-shadow ").concat(dt("accordion.transition.duration"), ";\n    outline-color: transparent;\n}\n\n.p-accordionpanel:first-child > .p-accordionheader {\n    border-width: ").concat(dt("accordion.header.first.border.width"), ";\n    border-start-start-radius: ").concat(dt("accordion.header.first.top.border.radius"), ";\n    border-start-end-radius: ").concat(dt("accordion.header.first.top.border.radius"), ";\n}\n\n.p-accordionpanel:last-child > .p-accordionheader {\n    border-end-start-radius: ").concat(dt("accordion.header.last.bottom.border.radius"), ";\n    border-end-end-radius: ").concat(dt("accordion.header.last.bottom.border.radius"), ";\n}\n\n.p-accordionpanel:last-child.p-accordionpanel-active > .p-accordionheader {\n    border-end-start-radius: ").concat(dt("accordion.header.last.active.bottom.border.radius"), ";\n    border-end-end-radius: ").concat(dt("accordion.header.last.active.bottom.border.radius"), ";\n}\n\n.p-accordionheader-toggle-icon {\n    color: ").concat(dt("accordion.header.toggle.icon.color"), ";\n}\n\n.p-accordionpanel:not(.p-disabled) .p-accordionheader:focus-visible {\n    box-shadow: ").concat(dt("accordion.header.focus.ring.shadow"), ";\n    outline: ").concat(dt("accordion.header.focus.ring.width"), " ").concat(dt("accordion.header.focus.ring.style"), " ").concat(dt("accordion.header.focus.ring.color"), ";\n    outline-offset: ").concat(dt("accordion.header.focus.ring.offset"), ";\n}\n\n.p-accordionpanel:not(.p-accordionpanel-active):not(.p-disabled) > .p-accordionheader:hover {\n    background: ").concat(dt("accordion.header.hover.background"), ";\n    color: ").concat(dt("accordion.header.hover.color"), ";\n}\n\n.p-accordionpanel:not(.p-accordionpanel-active):not(.p-disabled) .p-accordionheader:hover .p-accordionheader-toggle-icon {\n    color: ").concat(dt("accordion.header.toggle.icon.hover.color"), ";\n}\n\n.p-accordionpanel:not(.p-disabled).p-accordionpanel-active > .p-accordionheader {\n    background: ").concat(dt("accordion.header.active.background"), ";\n    color: ").concat(dt("accordion.header.active.color"), ";\n}\n\n.p-accordionpanel:not(.p-disabled).p-accordionpanel-active > .p-accordionheader .p-accordionheader-toggle-icon {\n    color: ").concat(dt("accordion.header.toggle.icon.active.color"), ";\n}\n\n.p-accordionpanel:not(.p-disabled).p-accordionpanel-active > .p-accordionheader:hover {\n    background: ").concat(dt("accordion.header.active.hover.background"), ";\n    color: ").concat(dt("accordion.header.active.hover.color"), ";\n}\n\n.p-accordionpanel:not(.p-disabled).p-accordionpanel-active > .p-accordionheader:hover .p-accordionheader-toggle-icon {\n    color: ").concat(dt("accordion.header.toggle.icon.active.hover.color"), ";\n}\n\n.p-accordioncontent-content {\n    border-style: solid;\n    border-width: ").concat(dt("accordion.content.border.width"), ";\n    border-color: ").concat(dt("accordion.content.border.color"), ";\n    background-color: ").concat(dt("accordion.content.background"), ";\n    color: ").concat(dt("accordion.content.color"), ";\n    padding: ").concat(dt("accordion.content.padding"), ";\n}\n");
};
var classes4 = {
  root: "p-accordion p-component"
};
var AccordionStyle = BaseStyle.extend({
  name: "accordion",
  theme,
  classes: classes4
});

// node_modules/primevue/accordion/index.mjs
var script$14 = {
  name: "BaseAccordion",
  "extends": script,
  props: {
    value: {
      type: [String, Number, Array],
      "default": void 0
    },
    multiple: {
      type: Boolean,
      "default": false
    },
    lazy: {
      type: Boolean,
      "default": false
    },
    tabindex: {
      type: Number,
      "default": 0
    },
    selectOnFocus: {
      type: Boolean,
      "default": false
    },
    expandIcon: {
      type: String,
      "default": void 0
    },
    collapseIcon: {
      type: String,
      "default": void 0
    },
    // @deprecated since v4.
    activeIndex: {
      type: [Number, Array],
      "default": null
    }
  },
  style: AccordionStyle,
  provide: function provide4() {
    return {
      $pcAccordion: this,
      $parentInstance: this
    };
  }
};
var script9 = {
  name: "Accordion",
  "extends": script$14,
  inheritAttrs: false,
  emits: ["update:value", "update:activeIndex", "tab-open", "tab-close", "tab-click"],
  data: function data() {
    return {
      id: this.$attrs.id,
      d_value: this.value
    };
  },
  watch: {
    "$attrs.id": function $attrsId(newValue) {
      this.id = newValue || UniqueComponentId();
    },
    value: function value(newValue) {
      this.d_value = newValue;
    },
    activeIndex: {
      immediate: true,
      handler: function handler(newValue) {
        if (this.hasAccordionTab) {
          this.d_value = this.multiple ? newValue === null || newValue === void 0 ? void 0 : newValue.map(String) : newValue === null || newValue === void 0 ? void 0 : newValue.toString();
        }
      }
    }
  },
  mounted: function mounted() {
    this.id = this.id || UniqueComponentId();
  },
  methods: {
    isItemActive: function isItemActive(value2) {
      var _this$d_value;
      return this.multiple ? (_this$d_value = this.d_value) === null || _this$d_value === void 0 ? void 0 : _this$d_value.includes(value2) : this.d_value === value2;
    },
    updateValue: function updateValue(newValue) {
      var _this$d_value2;
      var active2 = this.isItemActive(newValue);
      if (this.multiple) {
        if (active2) {
          this.d_value = this.d_value.filter(function(v) {
            return v !== newValue;
          });
        } else {
          if (this.d_value) this.d_value.push(newValue);
          else this.d_value = [newValue];
        }
      } else {
        this.d_value = active2 ? null : newValue;
      }
      this.$emit("update:value", this.d_value);
      this.$emit("update:activeIndex", this.multiple ? (_this$d_value2 = this.d_value) === null || _this$d_value2 === void 0 ? void 0 : _this$d_value2.map(Number) : Number(this.d_value));
      this.$emit(active2 ? "tab-close" : "tab-open", {
        originalEvent: void 0,
        index: Number(newValue)
      });
    },
    // @deprecated since v4. Use new structure instead.
    isAccordionTab: function isAccordionTab(child) {
      return child.type.name === "AccordionTab";
    },
    getTabProp: function getTabProp(tab, name) {
      return tab.props ? tab.props[name] : void 0;
    },
    getKey: function getKey(tab, index) {
      return this.getTabProp(tab, "header") || index;
    },
    getHeaderPT: function getHeaderPT(tab, index) {
      var _this = this;
      return {
        root: mergeProps({
          onClick: function onClick2(event) {
            return _this.onTabClick(event, index);
          }
        }, this.getTabProp(tab, "headerProps"), this.getTabPT(tab, "header", index)),
        toggleicon: mergeProps(this.getTabProp(tab, "headeractionprops"), this.getTabPT(tab, "headeraction", index))
      };
    },
    getContentPT: function getContentPT(tab, index) {
      return {
        root: mergeProps(this.getTabProp(tab, "contentProps"), this.getTabPT(tab, "toggleablecontent", index)),
        transition: this.getTabPT(tab, "transition", index),
        content: this.getTabPT(tab, "content", index)
      };
    },
    getTabPT: function getTabPT(tab, key, index) {
      var count = this.tabs.length;
      var tabMetaData = {
        props: tab.props || {},
        parent: {
          instance: this,
          props: this.$props,
          state: this.$data
        },
        context: {
          index,
          count,
          first: index === 0,
          last: index === count - 1,
          active: this.isItemActive("".concat(index))
        }
      };
      return mergeProps(this.ptm("accordiontab.".concat(key), tabMetaData), this.ptmo(this.getTabProp(tab, "pt"), key, tabMetaData));
    },
    onTabClick: function onTabClick(event, index) {
      this.$emit("tab-click", {
        originalEvent: event,
        index
      });
    }
  },
  computed: {
    // @deprecated since v4.
    tabs: function tabs() {
      var _this2 = this;
      return this.$slots["default"]().reduce(function(tabs2, child) {
        if (_this2.isAccordionTab(child)) {
          tabs2.push(child);
        } else if (child.children && child.children instanceof Array) {
          child.children.forEach(function(nestedChild) {
            if (_this2.isAccordionTab(nestedChild)) {
              tabs2.push(nestedChild);
            }
          });
        }
        return tabs2;
      }, []);
    },
    hasAccordionTab: function hasAccordionTab() {
      return this.tabs.length;
    }
  },
  components: {
    AccordionPanel: script8,
    AccordionHeader: script7,
    AccordionContent: script6,
    ChevronUpIcon: script5,
    ChevronRightIcon: script4
  }
};
function render6(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_AccordionHeader = resolveComponent("AccordionHeader");
  var _component_AccordionContent = resolveComponent("AccordionContent");
  var _component_AccordionPanel = resolveComponent("AccordionPanel");
  return openBlock(), createElementBlock("div", mergeProps({
    "class": _ctx.cx("root")
  }, _ctx.ptmi("root")), [$options.hasAccordionTab ? (openBlock(true), createElementBlock(Fragment, {
    key: 0
  }, renderList($options.tabs, function(tab, i) {
    return openBlock(), createBlock(_component_AccordionPanel, {
      key: $options.getKey(tab, i),
      value: "".concat(i),
      pt: {
        root: $options.getTabPT(tab, "root", i)
      },
      disabled: $options.getTabProp(tab, "disabled")
    }, {
      "default": withCtx(function() {
        return [createVNode(_component_AccordionHeader, {
          "class": normalizeClass($options.getTabProp(tab, "headerClass")),
          pt: $options.getHeaderPT(tab, i)
        }, {
          toggleicon: withCtx(function(slotProps) {
            return [slotProps.active ? (openBlock(), createBlock(resolveDynamicComponent(_ctx.$slots.collapseicon ? _ctx.$slots.collapseicon : _ctx.collapseIcon ? "span" : "ChevronDownIcon"), mergeProps({
              key: 0,
              "class": [_ctx.collapseIcon, slotProps["class"]],
              "aria-hidden": "true",
              ref_for: true
            }, $options.getTabPT(tab, "headericon", i)), null, 16, ["class"])) : (openBlock(), createBlock(resolveDynamicComponent(_ctx.$slots.expandicon ? _ctx.$slots.expandicon : _ctx.expandIcon ? "span" : "ChevronUpIcon"), mergeProps({
              key: 1,
              "class": [_ctx.expandIcon, slotProps["class"]],
              "aria-hidden": "true",
              ref_for: true
            }, $options.getTabPT(tab, "headericon", i)), null, 16, ["class"]))];
          }),
          "default": withCtx(function() {
            return [tab.children && tab.children.headericon ? (openBlock(), createBlock(resolveDynamicComponent(tab.children.headericon), {
              key: 0,
              isTabActive: $options.isItemActive("".concat(i)),
              active: $options.isItemActive("".concat(i)),
              index: i
            }, null, 8, ["isTabActive", "active", "index"])) : createCommentVNode("", true), tab.props && tab.props.header ? (openBlock(), createElementBlock("span", mergeProps({
              key: 1,
              ref_for: true
            }, $options.getTabPT(tab, "headertitle", i)), toDisplayString(tab.props.header), 17)) : createCommentVNode("", true), tab.children && tab.children.header ? (openBlock(), createBlock(resolveDynamicComponent(tab.children.header), {
              key: 2
            })) : createCommentVNode("", true)];
          }),
          _: 2
        }, 1032, ["class", "pt"]), createVNode(_component_AccordionContent, {
          pt: $options.getContentPT(tab, i)
        }, {
          "default": withCtx(function() {
            return [(openBlock(), createBlock(resolveDynamicComponent(tab)))];
          }),
          _: 2
        }, 1032, ["pt"])];
      }),
      _: 2
    }, 1032, ["value", "pt", "disabled"]);
  }), 128)) : renderSlot(_ctx.$slots, "default", {
    key: 1
  })], 16);
}
script9.render = render6;
export {
  script9 as default
};
//# sourceMappingURL=primevue_accordion.js.map
