import { ref, onUnmounted } from 'vue'
import { usePolling } from './usePolling'
import { HealthService } from '../api/apiService'
import type { HealthCheckResponse } from '../types/api'

export type HealthcheckState = HealthCheckResponse & {
  status: 'unknown' | 'ok' | 'error'
}

export function useHealthcheck() {
  const state = ref<HealthcheckState>({
    status: 'unknown',
    start_time: '',
    uptime_seconds: 0
  })
  
  let controller: AbortController | null = null
  const isChecking = ref(false)

  async function checkHealth() {
    if (isChecking.value) return

    // 取消之前的请求
    if (controller) {
      controller.abort()
    }
    controller = new AbortController()
    isChecking.value = true

    try {
      const data = await HealthService.checkHealth(controller.signal)
      state.value = {
        ...data,
        status: 'ok'
      }
      return data
    } catch (err) {
      const error = err as Error
      if (error.name === 'AbortError') {
        return
      }
      state.value.status = 'error'
      throw error
    } finally {
      isChecking.value = false
      controller = null
    }
  }

  const { isPolling, error, retryCount, start, stop } = usePolling(checkHealth, {
    interval: 10000,  // 每10秒检查一次
    maxRetries: 3,    // 最多重试3次
    immediate: true   // 立即开始检查
  })

  onUnmounted(() => {
    if (controller) {
      controller.abort()
    }
    stop()
  })

  return {
    state,
    isChecking,
    isPolling,
    error,
    retryCount,
    checkHealth,
    start,
    stop
  }
}
