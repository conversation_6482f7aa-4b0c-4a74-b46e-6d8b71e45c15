<template>
  <div class="min-h-screen flex flex-column surface-ground" style="font-family: var(--font-family-custom);">
    <header class="surface-card border-bottom-1 surface-border sticky top-0 z-5">
      <div class="mx-auto px-2 py-1 lg:px-3 lg:py-1 flex justify-content-between align-items-center">
        <div class="flex align-items-center gap-1">
          <img src="@/assets/logo.svg" alt="Lightbox Logo" class="h-1rem" />
          <h1 class="text-base lg:text-lg font-medium text-900 m-0 line-height-2">{{ $t('app.title') }}</h1>
        </div>
        <div class="flex align-items-center">
          <RouterLink to="/login" class="p-button p-component p-button-text">
            <span class="p-button-label">登录</span>
          </RouterLink>
          <LanguageSelector />
        </div>
      </div>
    </header>

    <div class="flex-1 mx-auto w-full max-w-7xl p-3 lg:p-4">
      <RouterView />
    </div>

    <footer class="surface-card border-top-1 surface-border text-center py-1">
      <span class="text-xs text-color-secondary">{{ $t('app.footer.powered') }} <i class="pi pi-heart-fill text-pink-500"></i></span>
    </footer>

    <Toast position="top-right" />
    <ConfirmDialog />
  </div>
</template>

<script setup lang="ts">
import { RouterView, RouterLink } from 'vue-router'
import { watch } from 'vue'
import { useToast } from 'primevue/usetoast'
import { useCurrentInstance } from '@/composables/useCurrentInstance'
import { useHealthcheck } from '@/composables/useHealthcheck'
import Toast from 'primevue/toast'
import ConfirmDialog from 'primevue/confirmdialog'
import LanguageSelector from '@/components/LanguageSelector.vue'

const { proxy } = useCurrentInstance()
const toast = useToast()

const { state, error } = useHealthcheck()

// 监听健康检查错误
watch(error, (newError) => {
  if (newError) {
    toast.add({
      severity: 'error',
      summary: '服务器连接失败',
      detail: '无法连接到服务器，请检查网络连接',
      life: 5000
    })
  }
})
</script>

<style lang="scss">
@import '@/assets/styles/fonts.css';

/* 全局应用自定义字体 */
:root {
  font-family: var(--font-family-custom);
}

:root {
  --font-size: 1rem;
  --font-weight: 400;
  --transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

html, body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: rgb(var(--surface-900));
  color: rgb(var(--surface-50));
  font-family: var(--font-family);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--surface-ground);
}

.app-header {
  padding: 0.75rem 1.5rem;
  background: var(--surface-card);
  border-bottom: 1px solid var(--surface-border);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1280px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-right {
  display: flex;
  align-items: center;
}

.app-logo {
  height: 2rem;
  width: auto;
}

.app-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.app-content {
  flex: 1;
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.app-footer {
  padding: 1rem;
  text-align: center;
  background: var(--surface-card);
  border-top: 1px solid var(--surface-border);
  color: var(--text-color-secondary);
}

/* 桌面端优化 */
@media screen and (min-width: 1024px) {
  .app-header {
    padding: 1rem 2rem;
  }

  .app-content {
    padding: 2.5rem 2rem;
  }

  .app-title {
    font-size: 1.5rem;
  }

  .app-logo {
    height: 2.25rem;
  }
}

/* 移动端适配 */
@media screen and (max-width: 640px) {
  .app-header {
    padding: 0.5rem 1rem;
  }

  .app-content {
    padding: 1.5rem 1rem;
    gap: 1.5rem;
  }

  .app-logo {
    height: 1.75rem;
  }

  .app-title {
    font-size: 1rem;
  }

  .header-left {
    gap: 0.5rem;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 380px) {
  .app-title {
    display: none;
  }
}

/* Modern Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(var(--surface-800));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgb(var(--surface-600));
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--surface-500));
}

/* Smooth Transitions */
.p-component {
  transition: all 0.3s var(--transition-timing-function);
}

/* Toast Customization */
:deep(.p-toast) {
  font-family: var(--font-family);
}

:deep(.p-toast-message) {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgb(var(--surface-800) / 0.8);
}

:deep(.p-toast-message-success) {
  border-left: 4px solid rgb(var(--success-color));
}

:deep(.p-toast-message-info) {
  border-left: 4px solid rgb(var(--info-color));
}

:deep(.p-toast-message-warn) {
  border-left: 4px solid rgb(var(--warning-color));
}

:deep(.p-toast-message-error) {
  border-left: 4px solid rgb(var(--danger-color));
}

/* ConfirmDialog Customization */
:deep(.p-dialog) {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

:deep(.p-confirm-dialog-message) {
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0;
  padding: 1rem 0;
}

:deep(.p-confirm-dialog-icon) {
  font-size: 1.5rem;
  margin-right: 0.75rem;
}

/* Selection Color */
::selection {
  background: rgb(var(--primary-500) / 0.3);
  color: rgb(var(--surface-50));
}
</style>
