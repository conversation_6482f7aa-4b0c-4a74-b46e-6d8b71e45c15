"""简化的测试服务器，用于测试太湖登录功能"""
import os
import logging
from fastapi import FastAPI, Request, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import RedirectResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import httpx
from dotenv import load_dotenv
from urllib.parse import urlencode

# 加载环境变量
load_dotenv("api_docs_server/.env.development")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="API Documentation Service - Test Auth",
    description="测试太湖登录功能",
    version="1.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 安全依赖
security = HTTPBearer(auto_error=False)

@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {"status": "ok", "message": "Service is running"}

@app.get("/api/auth/callback")
async def taihu_callback(request: Request):
    """处理太湖OAuth2.0回调"""
    try:
        code = request.query_params.get("code")
        state = request.query_params.get("state")
        error = request.query_params.get("error")

        frontend_url = os.getenv("FRONTEND_URL", "http://localhost:3000")

        # 如果有错误参数，重定向到前端错误页面
        if error:
            redirect_url = f"{frontend_url}/#/auth/callback?error={error}"
            return RedirectResponse(url=redirect_url)

        if not code:
            redirect_url = f"{frontend_url}/#/auth/callback?error=missing_code"
            return RedirectResponse(url=redirect_url)

        client_id = os.getenv("TAIHU_CLIENT_ID", "test-app")
        client_secret = os.getenv("TAIHU_CLIENT_SECRET", "ilX3uqWyJRbK")
        auth_url = os.getenv("TAIHU_AUTH_URL", "https://test-odc.it.woa.com/api/auth-center/oauth2")
        redirect_uri = os.getenv("TAIHU_REDIRECT_URI", "http://localhost:8001/api/auth/callback")

        logger.info(f"Exchanging code for token with redirect_uri: {redirect_uri}")

        # 使用授权码换取访问令牌
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{auth_url}/token",
                data={
                    "grant_type": "authorization_code",
                    "code": code,
                    "client_id": client_id,
                    "client_secret": client_secret,
                    "redirect_uri": redirect_uri
                },
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            response.raise_for_status()
            data = response.json()

            logger.info(f"Token exchange response status: {response.status_code}")

            # 验证令牌并完成登录
            if data.get("access_token"):
                # 重定向到前端回调页面，携带token信息
                params = {
                    "code": code,
                    "access_token": data["access_token"]
                }
                if state:
                    params["state"] = state
                if data.get("refresh_token"):
                    params["refresh_token"] = data["refresh_token"]

                callback_url = f"{frontend_url}/#/auth/callback?{urlencode(params)}"
                return RedirectResponse(url=callback_url)
            else:
                logger.error(f"Failed to exchange token: {data}")
                error_url = f"{frontend_url}/#/auth/callback?error=token_exchange_failed"
                return RedirectResponse(url=error_url)

    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error during token exchange: {str(e)}")
        frontend_url = os.getenv("FRONTEND_URL", "http://localhost:3000")
        error_url = f"{frontend_url}/#/auth/callback?error=http_error"
        return RedirectResponse(url=error_url)
    except Exception as e:
        logger.error(f"Unexpected error during callback: {str(e)}")
        frontend_url = os.getenv("FRONTEND_URL", "http://localhost:3000")
        error_url = f"{frontend_url}/#/auth/callback?error=unexpected_error"
        return RedirectResponse(url=error_url)

async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """验证访问令牌"""
    if not credentials:
        logger.warning("No authentication credentials provided")
        raise HTTPException(status_code=401, detail="Authentication required")

    token = credentials.credentials

    try:
        # 获取环境变量或使用默认值
        auth_url = os.getenv("TAIHU_AUTH_URL", "https://test-odc.it.woa.com/api/auth-center/oauth2")

        # 调用太湖验证接口验证令牌
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{auth_url}/verify",
                json={"token": token},
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            data = response.json()

            # 检查验证结果
            if data.get("active", False):
                return data
            else:
                logger.error(f"Invalid token: {data}")
                raise HTTPException(status_code=401, detail="Invalid or expired token")

    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error during token verification: {str(e)}")
        raise HTTPException(status_code=401, detail="Token verification failed")
    except Exception as e:
        logger.error(f"Error during token verification: {str(e)}")
        raise HTTPException(status_code=401, detail="Authentication failed")

@app.get("/api/auth/user")
async def get_user_info(token_data=Depends(verify_token)):
    """获取当前登录用户信息"""
    try:
        # 从令牌数据中提取用户信息
        user_info = {
            "id": token_data.get("sub"),
            "name": token_data.get("name", "Unknown User"),
            "email": token_data.get("email"),
            "roles": token_data.get("roles", []),
            "permissions": token_data.get("permissions", [])
        }
        return user_info
    except Exception as e:
        logger.error(f"Error retrieving user info: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve user information")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
