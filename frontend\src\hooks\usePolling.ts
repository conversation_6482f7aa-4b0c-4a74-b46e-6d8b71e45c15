import { ref, onMounted, onUnmounted } from 'vue';

interface PollingOptions {
  interval?: number;
  maxRetries?: number;
  retryDelay?: number;
  onError?: (error: any) => void;
  immediate?: boolean;
  pauseWhenHidden?: boolean;  // 页面不可见时是否暂停
  pauseWhenOffline?: boolean; // 离线时是否暂停
}

export function usePolling<T>(
  fetchFn: () => Promise<T>,
  options: PollingOptions = {}
) {
  const {
    interval = 30000,
    maxRetries = 3,
    retryDelay = 5000,
    onError,
    immediate = true,
    pauseWhenHidden = true,
    pauseWhenOffline = true
  } = options;

  const data = ref<T | null>(null);
  const loading = ref(false);
  const error = ref<Error | null>(null);
  const isPaused = ref(false);
  let timer: number | null = null;
  let retryCount = 0;
  let retryTimer: number | null = null;
  let lastFetchTime = 0;

  const clearTimers = () => {
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
    if (retryTimer) {
      clearTimeout(retryTimer);
      retryTimer = null;
    }
  };

  const shouldPoll = () => {
    if (isPaused.value) return false;
    if (pauseWhenHidden && document.hidden) return false;
    if (pauseWhenOffline && !navigator.onLine) return false;
    return true;
  };

  const refresh = async (force = true) => {
    console.log('[usePolling] Refreshing data...');
    
    // 检查是否应该继续轮询
    if (!shouldPoll()) {
      console.log('[usePolling] Polling paused');
      return;
    }
    
    // 检查距离上次成功获取数据的时间
    const now = Date.now();
    if (!force && now - lastFetchTime < interval) {
      console.log('[usePolling] Skip refresh - too soon');
      return;
    }

    if (loading.value && !force) {
      console.log('[usePolling] Skip refresh - already loading');
      return;
    }

    loading.value = true;
    error.value = null;

    try {
      console.log('[usePolling] Fetching data...');
      const result = await fetchFn();
      console.log('[usePolling] Data fetched successfully');
      data.value = result;
      lastFetchTime = now;
      retryCount = 0;
    } catch (err: any) {
      console.error('[usePolling] Error fetching data:', err);
      error.value = err;
      retryCount++;

      if (retryCount <= maxRetries) {
        console.log(`[usePolling] Retrying in ${retryDelay}ms (${retryCount}/${maxRetries})`);
        retryTimer = window.setTimeout(() => {
          refresh(true);
        }, retryDelay);
      }

      if (onError) {
        onError(err);
      }
    } finally {
      loading.value = false;
    }
  };

  const startPolling = () => {
    console.log('[usePolling] Starting polling...');
    isPaused.value = false;
    clearTimers();
    
    // 立即执行一次
    if (immediate) {
      refresh(true);
    }
    
    // 设置定时器
    timer = window.setInterval(() => {
      refresh(false);
    }, interval);
  };

  const stopPolling = () => {
    console.log('[usePolling] Stopping polling...');
    isPaused.value = true;
    clearTimers();
  };

  const pausePolling = () => {
    console.log('[usePolling] Pausing polling...');
    isPaused.value = true;
  };

  const resumePolling = () => {
    console.log('[usePolling] Resuming polling...');
    isPaused.value = false;
    refresh(true);
  };

  // 监听页面可见性变化
  const handleVisibilityChange = () => {
    if (pauseWhenHidden) {
      if (document.hidden) {
        console.log('[usePolling] Page hidden, pausing polling...');
        pausePolling();
      } else {
        console.log('[usePolling] Page visible, resuming polling...');
        resumePolling();
      }
    }
  };

  // 监听网络状态变化
  const handleNetworkChange = () => {
    if (pauseWhenOffline) {
      if (navigator.onLine) {
        console.log('[usePolling] Network online, resuming polling...');
        resumePolling();
      } else {
        console.log('[usePolling] Network offline, pausing polling...');
        pausePolling();
      }
    }
  };

  onMounted(() => {
    console.log('[usePolling] Component mounted, starting polling...');
    
    // 添加事件监听器
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('online', handleNetworkChange);
    window.addEventListener('offline', handleNetworkChange);
    
    // 开始轮询
    startPolling();
  });

  onUnmounted(() => {
    console.log('[usePolling] Component unmounted, cleaning up...');
    
    // 移除事件监听器
    document.removeEventListener('visibilitychange', handleVisibilityChange);
    window.removeEventListener('online', handleNetworkChange);
    window.removeEventListener('offline', handleNetworkChange);
    
    // 清理定时器
    clearTimers();
  });

  return {
    data,
    loading,
    error,
    refresh,
    startPolling,
    stopPolling,
    pausePolling,
    resumePolling,
    isPaused
  };
}
