{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "Node", "strict": true, "jsx": "preserve", "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "noEmit": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "allowJs": true, "types": ["node"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["src/**/__tests__/*", "src/**/*.spec.ts", "src/**/*.test.ts"], "references": [{"path": "./tsconfig.node.json"}]}