"""简化的服务器启动脚本"""
import os
import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.sessions import SessionMiddleware
from dotenv import load_dotenv

# 加载环境变量
load_dotenv("api_docs_server/.env.development")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="API Documentation Service",
    description="A service for managing API documentation with Taihu authentication.",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3005", "http://localhost:3000", "https://api-docs.woa.com"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加Session中间件
app.add_middleware(
    SessionMiddleware,
    secret_key=os.getenv("SESSION_SECRET_KEY", "keyboard cat"),
    max_age=3600
)

# 导入并注册太湖认证路由
from api_docs_server.routers.taihu_auth import router as taihu_router
app.include_router(taihu_router, prefix="/auth")

# 根路径重定向到登录页面
@app.get("/")
async def root():
    from fastapi.responses import RedirectResponse
    return RedirectResponse(url="/auth/login")

# 健康检查端点
@app.get("/api/health")
async def health_check():
    return {"status": "ok", "message": "Service is running"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
