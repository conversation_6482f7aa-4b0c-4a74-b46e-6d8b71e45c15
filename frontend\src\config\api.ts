// API configuration
import { API_BASE_URL } from './env'

// API端点配置
export const API_ENDPOINTS = {
    docs: '/backend/api/docs',
    health: '/backend/api/health'
} as const

// Axios配置
export const AXIOS_CONFIG = {
    baseURL: API_BASE_URL,
    timeout: 30000,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    },
    withCredentials: true  // 允许跨域请求携带凭证
} as const
