<template>
  <div class="api-docs-container">
    <RouterView />
  </div>
</template>

<script setup>
import { RouterView } from 'vue-router'
</script>

<style scoped>
.api-docs-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: var(--surface-ground);
}

.content-wrapper {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Document Cards */
.doc-card {
  background: var(--surface-card);
  border-radius: 1.5rem;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
  position: relative;
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.doc-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.3);
}

.doc-card::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.03), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.doc-card:hover::before {
  opacity: 1;
}

.doc-card-header {
  background: linear-gradient(135deg, var(--primary-900), var(--primary-800));
  padding: 2rem;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.doc-icon {
  font-size: 3rem;
  color: rgba(255,255,255,0.9);
}

.card-menu-button {
  position: absolute !important;
  top: 0.5rem;
  right: 0.5rem;
  width: 2.5rem !important;
  height: 2.5rem !important;
}

.doc-card-content {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.doc-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.doc-type {
  font-size: 0.875rem;
  color: var(--primary-400);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.doc-date {
  font-size: 0.875rem;
  color: var(--text-color-secondary);
}

.doc-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
  line-height: 1.4;
}

.doc-description {
  color: var(--text-color-secondary);
  line-height: 1.6;
  margin: 0;
  flex: 1;
}

.doc-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 1rem;
  border-top: 1px solid rgba(255,255,255,0.1);
}

.doc-url {
  font-size: 0.875rem;
  color: var(--primary-400);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 1rem;
}

.view-button {
  width: 2.5rem !important;
  height: 2.5rem !important;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  background: var(--surface-card);
  border-radius: 2rem;
  border: 1px solid rgba(255,255,255,0.1);
}

.empty-state-icon {
  width: 5rem;
  height: 5rem;
  margin: 0 auto 2rem;
  background: linear-gradient(135deg, var(--primary-900), var(--primary-800));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-state-icon i {
  font-size: 2.5rem;
  color: white;
}

.empty-state-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.empty-state-text {
  color: var(--text-color-secondary);
  font-size: 1.1rem;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto 2rem;
}

.empty-state-button {
  font-size: 1.1rem !important;
  padding: 1rem 2rem !important;
}

.preview-dialog :deep(.p-dialog-content) {
  padding: 0;
  overflow: hidden;
  border-bottom-left-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
  background: var(--surface-section);
}

.preview-container {
  position: relative;
  width: 100%;
  height: 80vh;
  background: var(--surface-section);
  overflow: hidden;
}

.preview-frame {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
  box-shadow: inset 0 0 0 1px var(--surface-border);
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-color-secondary);
  background: var(--surface-ground);
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .preview-container {
    height: 70vh;
  }
}

@media screen and (max-width: 480px) {
  .preview-container {
    height: 60vh;
  }
}

@keyframes rotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* Responsive Design */
@media screen and (max-width: 960px) {
  .hero-section {
    padding: 3rem 1.5rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }
}

@media screen and (max-width: 640px) {
  .api-docs-container {
    padding: 1rem;
  }

  .hero-section {
    padding: 2rem 1rem;
    margin-bottom: 2rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .doc-card-header {
    padding: 1.5rem;
  }

  .doc-icon {
    font-size: 2.5rem;
  }
}

.preview-dialog {
  :deep(.p-dialog-header) {
    padding: 1rem 1.5rem;
    background: var(--surface-section);
    border-bottom: 1px solid var(--surface-border);
  }

  :deep(.p-dialog-content) {
    padding: 0;
    background: var(--surface-ground);
  }

  :deep(.p-dialog-mask) {
    backdrop-filter: blur(8px);
  }

  :deep(.p-dialog-maximized) {
    .preview-container {
      height: calc(100vh - 4rem);
    }
  }
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 1rem;
}

.preview-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--surface-900);
  margin: 0;
}

.preview-toolbar {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.preview-container {
  position: relative;
  width: 100%;
  height: 80vh;
  background: var(--surface-card);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  overflow: hidden;
  transition: all 0.3s ease;
}

.preview-frame {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
  box-shadow: inset 0 0 0 1px var(--surface-border);
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-color-secondary);
  background: var(--surface-ground);
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .preview-container {
    height: 70vh;
  }

  .preview-title {
    font-size: 1.1rem;
  }
}

@media screen and (max-width: 480px) {
  .preview-container {
    height: 60vh;
  }

  .preview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .preview-toolbar {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
