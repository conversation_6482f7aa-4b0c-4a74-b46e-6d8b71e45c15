API 参考文档
===========

基础信息
-------
* 基础URL: ``http://localhost:8000`` (开发环境)
* 内容类型: ``application/json``
* 认证方式: Bearer <PERSON>ken (如果需要)

API 端点
-------

文档管理
~~~~~~~

创建文档
^^^^^^^
.. code-block:: http

   POST /api/docs
   Content-Type: application/json
   Authorization: Bearer <token> (可选)

   {
       "title": "文档标题",
       "url": "文档URL",
       "description": "文档描述",
       "tags": ["标签1", "标签2"]
   }

响应示例:

.. code-block:: json

   {
       "id": "文档ID",
       "title": "文档标题",
       "url": "文档URL",
       "description": "文档描述",
       "tags": ["标签1", "标签2"],
       "created_at": "创建时间",
       "updated_at": "更新时间"
   }

获取文档列表
^^^^^^^^^^
.. code-block:: http

   GET /api/docs

响应示例:

.. code-block:: json

   [
       {
           "id": "文档ID",
           "title": "文档标题",
           "url": "文档URL",
           "description": "文档描述",
           "tags": ["标签1", "标签2"],
           "created_at": "创建时间",
           "updated_at": "更新时间"
       }
   ]

搜索文档
^^^^^^^
.. code-block:: http

   GET /api/docs/search?q=关键词

响应示例:

.. code-block:: json

   [
       {
           "id": "文档ID",
           "title": "文档标题",
           "url": "文档URL",
           "description": "文档描述",
           "tags": ["标签1", "标签2"],
           "created_at": "创建时间",
           "updated_at": "更新时间"
       }
   ]

系统功能
-------

刷新通知
^^^^^^^
.. code-block:: http

   POST /api/notify/refresh

响应示例:

.. code-block:: json

   {
       "status": "success",
       "message": "refresh notification sent"
   }
