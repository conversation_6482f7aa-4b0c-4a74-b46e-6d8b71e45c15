import axios, { AxiosError, AxiosInstance } from 'axios';
import { handleApiError } from './errorHandler';

// API endpoints
export const API_ENDPOINTS = {
  DOCS: '/api/docs',
  VALIDATE_URL: '/api/docs/url/validate',
  HEALTH: '/api/health'
} as const;

// 创建axios实例
const axiosInstance: AxiosInstance = axios.create({
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
  },
  // 允许跨域请求携带凭证
  withCredentials: true,
  baseURL: '/backend'
});

// 请求拦截器
axiosInstance.interceptors.request.use(
  (config) => {
    // 从localStorage获取token
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error: AxiosError) => {
    if (error.response) {
      // 服务器返回错误响应
      const status = error.response.status;
      const data = error.response.data as any;
      const url = error.config?.url || '';
      // 移除 URL 中可能的重复斜杠
      const fullUrl = (axiosInstance.defaults.baseURL + url).replace(/([^:]\/)\/+/g, "$1");
      const method = error.config?.method?.toUpperCase() || 'UNKNOWN';

      let errorMessage = `请求失败 [${method} ${fullUrl}]: `;

      switch (status) {
        case 400:
          errorMessage += data.detail || 'Bad Request';
          break;
        case 401:
          // 清除本地存储的token
          localStorage.removeItem('auth_token');
          // 重定向到登录页
          window.location.href = '/#/login';
          errorMessage += data.detail || 'Unauthorized';
          break;
        case 403:
          errorMessage += data.detail || 'Forbidden';
          break;
        case 404:
          errorMessage += data.detail || 'Not Found';
          break;
        case 500:
          errorMessage += data.detail || 'Internal Server Error';
          break;
        default:
          errorMessage += data.detail || `HTTP ${status}`;
      }

      throw new Error(errorMessage);
    } else if (error.request) {
      // 请求已发出但未收到响应
      const url = error.config?.url || '';
      // 移除 URL 中可能的重复斜杠
      const fullUrl = (axiosInstance.defaults.baseURL + url).replace(/([^:]\/)\/+/g, "$1");
      const method = error.config?.method?.toUpperCase() || 'UNKNOWN';
      throw new Error(`请求超时或无响应 [${method} ${fullUrl}]`);
    } else {
      // 请求配置出错
      throw new Error(`请求配置错误: ${error.message}`);
    }
  }
);

export default axiosInstance;
