# Name of current package.
PACKAGE_NAME = "api_docs_server"

# Taihu production environment configuration
TAIHU_PRODUCTION_CONFIG = {
    "issuer": "https://tai.it.tencent.com/api/auth-center/oauth2/",
    "authorization_url": "https://tai.it.tencent.com/api/auth-center/oauth2/authorize",
    "token_url": "https://tai.it.tencent.com/api/auth-center/oauth2/token",
    "userinfo_url": "https://tai.it.tencent.com/api/auth-center/oauth2/userinfo",
    "client_id": os.getenv("TAIHU_PRODUCTION_CLIENT_ID", "api-docs"),
    "client_secret": os.getenv("TAIHU_PRODUCTION_CLIENT_SECRET", "WNEK28UBJHHMN0D81GMPBHAWWZ4GEL2U"),
    "callback_url": os.getenv("TAIHU_PRODUCTION_CALLBACK_URL", "https://api-docs.woa.com/auth/tencent/callback"),
    "scope": ["openid", "profile"]
}

# Add any other necessary constants for authentication