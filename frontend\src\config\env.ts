// 环境变量配置
interface ImportMetaEnv {
  VITE_API_HOST: string
  VITE_API_PORT: string
  VITE_FRONTEND_HOST: string
  VITE_FRONTEND_PORT: string
  VITE_ENV: string
  VITE_API_BASE_URL: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

interface EnvConfig {
  apiBaseUrl: string
  environment: string
  apiHost: string
  apiPort: number
  frontendHost: string
  frontendPort: number
}

const envConfig: EnvConfig = {
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',  // 使用相对路径，让 Vite 代理处理
  environment: import.meta.env.VITE_ENV || 'development',
  apiHost: import.meta.env.VITE_API_HOST || 'localhost',
  apiPort: parseInt(import.meta.env.VITE_API_PORT || '8000'),
  frontendHost: import.meta.env.VITE_FRONTEND_HOST || 'localhost',
  frontendPort: parseInt(import.meta.env.VITE_FRONTEND_PORT || '3000')
}

// 环境配置
export const API_BASE_URL = envConfig.apiBaseUrl

// 其他环境变量
export const isDevelopment = import.meta.env.DEV
export const isProduction = import.meta.env.PROD

export default envConfig
