#!/usr/bin/env python3
"""
测试太湖登录URL生成的脚本
"""

from urllib.parse import urlencode, urlparse, parse_qs

def generate_taihu_url(state="test_redirect"):
    """生成太湖登录URL"""
    base_url = "https://tai.it.tencent.com/api/auth-center/oauth2/authorize"

    params = {
        'client_id': 'api-docs',
        'response_type': 'code',
        'scope': 'openid offline',
        'state': state,
        'redirect_uri': 'https://api-docs.woa.com/taihu_callback'
    }

    return f"{base_url}?{urlencode(params)}"

def compare_urls():
    """对比我们生成的URL和正确的URL"""

    # 您提供的正确URL
    correct_url = "https://tai.it.tencent.com/api/auth-center/oauth2/authorize?client_id=api-docs&response_type=code&scope=openid+offline&state=abcdefg123456&redirect_uri=https://api-docs.woa.com/taihu_callback"

    # 我们生成的URL
    generated_url = generate_taihu_url("abcdefg123456")

    print("=== 太湖登录URL对比 ===\n")

    print("正确的URL:")
    print(correct_url)
    print()

    print("我们生成的URL:")
    print(generated_url)
    print()

    # 解析URL参数
    correct_parsed = urlparse(correct_url)
    generated_parsed = urlparse(generated_url)

    correct_params = parse_qs(correct_parsed.query)
    generated_params = parse_qs(generated_parsed.query)

    print("=== 参数对比 ===")
    print(f"{'参数':<15} {'正确值':<25} {'生成值':<25} {'状态'}")
    print("-" * 80)

    all_params = set(correct_params.keys()) | set(generated_params.keys())

    for param in sorted(all_params):
        correct_val = correct_params.get(param, ['缺失'])[0]
        generated_val = generated_params.get(param, ['缺失'])[0]

        # 处理scope参数的空格/加号差异
        if param == 'scope':
            correct_val = correct_val.replace('+', ' ')
            generated_val = generated_val.replace('+', ' ')

        status = "✅ 匹配" if correct_val == generated_val else "❌ 不匹配"

        print(f"{param:<15} {correct_val:<25} {generated_val:<25} {status}")

    print()

    # 检查URL是否完全匹配（忽略参数顺序）
    correct_flat = {k: v[0] for k, v in correct_params.items()}
    generated_flat = {k: v[0] for k, v in generated_params.items()}

    # 处理scope参数的空格/加号差异
    if 'scope' in correct_flat:
        correct_flat['scope'] = correct_flat['scope'].replace('+', ' ')
    if 'scope' in generated_flat:
        generated_flat['scope'] = generated_flat['scope'].replace('+', ' ')

    if correct_flat == generated_flat:
        print("🎉 URL参数完全匹配！")
    else:
        print("⚠️  URL参数存在差异")

    return generated_url

def test_different_states():
    """测试不同的state参数"""
    print("\n=== 测试不同的state参数 ===")

    test_states = [
        "/",
        "/docs/123",
        "abcdefg123456",
        "redirect_after_login"
    ]

    for state in test_states:
        url = generate_taihu_url(state)
        print(f"State: {state}")
        print(f"URL: {url}")
        print()

if __name__ == "__main__":
    # 运行对比测试
    generated_url = compare_urls()

    # 测试不同的state参数
    test_different_states()

    print("=== 测试建议 ===")
    print("1. 复制上面生成的URL到浏览器中测试")
    print("2. 确认是否能看到太湖登录页面")
    print("3. 如果看不到登录页面，请检查参数是否完全匹配")
    print()
    print("测试URL:")
    print(generated_url)
