#!/usr/bin/env python3
"""
测试太湖登录URL生成的脚本
"""

from urllib.parse import urlencode, urlparse, parse_qs

def generate_taihu_url(state="test_redirect"):
    """生成太湖登录URL - 新的两步流程"""
    # 第一步：太湖登录页面
    login_url = "https://tai.it.tencent.com/odc-login/login"

    # 第二步：OAuth授权URL
    auth_base_url = "https://tai.it.tencent.com/api/auth-center/oauth2/authorize"
    auth_params = {
        'client_id': 'api-docs',
        'response_type': 'code',
        'scope': 'openid offline',
        'state': state,
        'redirect_uri': 'https://api-docs.woa.com/taihu_callback'
    }

    auth_url = f"{auth_base_url}?{urlencode(auth_params)}"

    # 登录页面URL，包含OAuth URL作为redirect_uri
    login_params = {
        'redirect_uri': auth_url
    }

    return f"{login_url}?{urlencode(login_params)}"

def compare_urls():
    """对比我们生成的URL和正确的URL"""

    # 您提供的OAuth授权URL（这是第二步）
    oauth_url = "https://tai.it.tencent.com/api/auth-center/oauth2/authorize?client_id=api-docs&response_type=code&scope=openid+offline&state=abcdefg123456&redirect_uri=https://api-docs.woa.com/taihu_callback"

    # 我们生成的完整登录URL（包含两步流程）
    generated_url = generate_taihu_url("abcdefg123456")

    print("=== 太湖登录流程对比 ===\n")

    print("第一步 - 太湖登录页面:")
    print("https://tai.it.tencent.com/odc-login/login")
    print()

    print("第二步 - OAuth授权URL (您提供的):")
    print(oauth_url)
    print()

    print("我们生成的完整URL (包含两步流程):")
    print(generated_url)
    print()

    # 解析生成的URL中的OAuth部分
    generated_parsed = urlparse(generated_url)
    generated_params = parse_qs(generated_parsed.query)

    # 从redirect_uri参数中提取OAuth URL
    oauth_redirect_uri = generated_params.get('redirect_uri', [''])[0]
    oauth_parsed = urlparse(oauth_redirect_uri)
    oauth_generated_params = parse_qs(oauth_parsed.query)

    # 解析您提供的OAuth URL
    oauth_correct_parsed = urlparse(oauth_url)
    oauth_correct_params = parse_qs(oauth_correct_parsed.query)

    print("=== OAuth参数对比 ===")
    print(f"{'参数':<15} {'正确值':<25} {'生成值':<25} {'状态'}")
    print("-" * 80)

    all_params = set(oauth_correct_params.keys()) | set(oauth_generated_params.keys())

    for param in sorted(all_params):
        correct_val = oauth_correct_params.get(param, ['缺失'])[0]
        generated_val = oauth_generated_params.get(param, ['缺失'])[0]

        # 处理scope参数的空格/加号差异
        if param == 'scope':
            correct_val = correct_val.replace('+', ' ')
            generated_val = generated_val.replace('+', ' ')

        status = "✅ 匹配" if correct_val == generated_val else "❌ 不匹配"

        print(f"{param:<15} {correct_val:<25} {generated_val:<25} {status}")

    print()

    # 检查OAuth参数是否完全匹配
    oauth_correct_flat = {k: v[0] for k, v in oauth_correct_params.items()}
    oauth_generated_flat = {k: v[0] for k, v in oauth_generated_params.items()}

    # 处理scope参数的空格/加号差异
    if 'scope' in oauth_correct_flat:
        oauth_correct_flat['scope'] = oauth_correct_flat['scope'].replace('+', ' ')
    if 'scope' in oauth_generated_flat:
        oauth_generated_flat['scope'] = oauth_generated_flat['scope'].replace('+', ' ')

    if oauth_correct_flat == oauth_generated_flat:
        print("🎉 OAuth参数完全匹配！")
    else:
        print("⚠️  OAuth参数存在差异")

    return generated_url

def test_different_states():
    """测试不同的state参数"""
    print("\n=== 测试不同的state参数 ===")

    test_states = [
        "/",
        "/docs/123",
        "abcdefg123456",
        "redirect_after_login"
    ]

    for state in test_states:
        url = generate_taihu_url(state)
        print(f"State: {state}")
        print(f"URL: {url}")
        print()

if __name__ == "__main__":
    # 运行对比测试
    generated_url = compare_urls()

    # 测试不同的state参数
    test_different_states()

    print("=== 测试建议 ===")
    print("1. 复制上面生成的URL到浏览器中测试")
    print("2. 确认是否能看到太湖登录页面")
    print("3. 如果看不到登录页面，请检查参数是否完全匹配")
    print()
    print("测试URL:")
    print(generated_url)
