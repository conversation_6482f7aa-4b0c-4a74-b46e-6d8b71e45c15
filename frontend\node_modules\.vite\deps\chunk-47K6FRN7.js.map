{"version": 3, "sources": ["../../@primeuix/src/zindex/index.ts"], "sourcesContent": ["export interface ZIndexOptions {\n    get(element?: HTMLElement): number;\n    set(key: string, element: HTMLElement, baseZIndex?: number): void;\n    clear(element: HTMLElement): void;\n    getCurrent(key: string): number;\n}\n\nfunction handler(): ZIndexOptions {\n    let zIndexes: { key: string; value: number }[] = [];\n\n    const generateZIndex = (key: string, autoZIndex: boolean, baseZIndex: number = 999) => {\n        const lastZIndex = getLastZIndex(key, autoZIndex, baseZIndex);\n        const newZIndex = lastZIndex.value + (lastZIndex.key === key ? 0 : baseZIndex) + 1;\n\n        zIndexes.push({ key, value: newZIndex });\n\n        return newZIndex;\n    };\n\n    const revertZIndex = (zIndex: number) => {\n        zIndexes = zIndexes.filter((obj) => obj.value !== zIndex);\n    };\n\n    const getCurrentZIndex = (key: string, autoZIndex: boolean) => {\n        return getLastZIndex(key, autoZIndex).value;\n    };\n\n    const getLastZIndex = (key: string, autoZIndex: boolean, baseZIndex: number = 0) => {\n        return [...zIndexes].reverse().find((obj) => (autoZIndex ? true : obj.key === key)) || { key, value: baseZIndex };\n    };\n\n    const getZIndex = (element?: HTMLElement): number => {\n        return element ? parseInt(element.style.zIndex, 10) || 0 : 0;\n    };\n\n    return {\n        get: getZIndex,\n        set: (key: string, element?: HTMLElement, baseZIndex?: number) => {\n            if (element) {\n                element.style.zIndex = String(generateZIndex(key, true, baseZIndex));\n            }\n        },\n        clear: (element?: HTMLElement) => {\n            if (element) {\n                revertZIndex(getZIndex(element));\n                element.style.zIndex = '';\n            }\n        },\n        getCurrent: (key: string) => getCurrentZIndex(key, true)\n    };\n}\n\nexport const ZIndex: ZIndexOptions = handler();\n"], "mappings": ";AAOA,SAAS,UAAyB;AAC9B,MAAI,WAA6C,CAAC;AAElD,QAAM,iBAAiB,CAAC,KAAa,YAAqB,aAAqB,QAAQ;AACnF,UAAM,aAAa,cAAc,KAAK,YAAY,UAAU;AAC5D,UAAM,YAAY,WAAW,SAAS,WAAW,QAAQ,MAAM,IAAI,cAAc;AAEjF,aAAS,KAAK,EAAE,KAAK,OAAO,UAAU,CAAC;AAEvC,WAAO;EACX;AAEA,QAAM,eAAe,CAAC,WAAmB;AACrC,eAAW,SAAS,OAAO,CAAC,QAAQ,IAAI,UAAU,MAAM;EAC5D;AAEA,QAAM,mBAAmB,CAAC,KAAa,eAAwB;AAC3D,WAAO,cAAc,KAAK,UAAU,EAAE;EAC1C;AAEA,QAAM,gBAAgB,CAAC,KAAa,YAAqB,aAAqB,MAAM;AAChF,WAAO,CAAC,GAAG,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,QAAS,aAAa,OAAO,IAAI,QAAQ,GAAI,KAAK,EAAE,KAAK,OAAO,WAAW;EACpH;AAEA,QAAM,YAAY,CAAC,YAAkC;AACjD,WAAO,UAAU,SAAS,QAAQ,MAAM,QAAQ,EAAE,KAAK,IAAI;EAC/D;AAEA,SAAO;IACH,KAAK;IACL,KAAK,CAAC,KAAa,SAAuB,eAAwB;AAC9D,UAAI,SAAS;AACT,gBAAQ,MAAM,SAAS,OAAO,eAAe,KAAK,MAAM,UAAU,CAAC;MACvE;IACJ;IACA,OAAO,CAAC,YAA0B;AAC9B,UAAI,SAAS;AACT,qBAAa,UAAU,OAAO,CAAC;AAC/B,gBAAQ,MAAM,SAAS;MAC3B;IACJ;IACA,YAAY,CAAC,QAAgB,iBAAiB,KAAK,IAAI;EAC3D;AACJ;AAEO,IAAM,SAAwB,QAAQ;", "names": []}