import { createApp } from 'vue'
import { createPinia } from 'pinia'
import i18n from './i18n'

import App from './App.vue'
import router from './router'

// 导入字体样式
import '@/assets/styles/fonts.css'

// PrimeVue Core
import PrimeVue from 'primevue/config'
import Button from 'primevue/button'
import Card from 'primevue/card'
import Dialog from 'primevue/dialog'
import InputText from 'primevue/inputtext'
import Toolbar from 'primevue/toolbar'
import Chip from 'primevue/chip'
import Toast from 'primevue/toast'
import ToastService from 'primevue/toastservice'
import Tooltip from 'primevue/tooltip'
import ConfirmDialog from 'primevue/confirmdialog'
import ConfirmationService from 'primevue/confirmationservice'
import Textarea from 'primevue/textarea'
import Accordion from 'primevue/accordion'
import AccordionTab from 'primevue/accordiontab'
import ContextMenu from 'primevue/contextmenu'
import Dropdown from 'primevue/dropdown'

// Theme and Styles
import Aura from '@primevue/themes/aura'
import 'primeicons'
import 'primeflex'

// Heartbeat
import { heartbeatService } from './services/heartbeat'

const app = createApp(App)

// Configure PrimeVue
app.use(PrimeVue, {
  theme: {
    preset: Aura,
    options: {
        prefix: 'p',
        darkModeSelector: 'system',
        cssLayer: false
    }
},
zIndex: {
  modal: 1100,        //dialog, drawer
  overlay: 1000,      //select, popover
  menu: 1000,         //overlay menus
  tooltip: 1100       //tooltip
},
toast: {
  position: 'top-right',
  timeout: 3000
},
ripple: true,
})

// Register components
app.component('Button', Button)
app.component('Card', Card)
app.component('Dialog', Dialog)
app.component('InputText', InputText)
app.component('Toolbar', Toolbar)
app.component('Chip', Chip)
app.component('Toast', Toast)
app.component('Textarea', Textarea)
app.component('Accordion', Accordion)
app.component('AccordionTab', AccordionTab)
app.component('ContextMenu', ContextMenu)
app.component('Dropdown', Dropdown)
app.component('ConfirmDialog', ConfirmDialog)

// Use Services
app.use(createPinia())
app.use(router)
app.use(i18n)
app.use(ToastService)
app.use(ConfirmationService)

// Directives
app.directive('tooltip', Tooltip)

// Global error handlers
app.config.errorHandler = (err: unknown, vm: any, info: string) => {
  console.error('Vue Error:', { err, info })
}

window.addEventListener('error', (event: ErrorEvent) => {
  console.error('Global Error:', event.error)
})

window.addEventListener('unhandledrejection', (event: PromiseRejectionEvent) => {
  console.error('Unhandled Promise Rejection:', event.reason)
})

document.addEventListener('DOMContentLoaded', () => {
  // 启动心跳服务
  heartbeatService.start();

  // 在应用关闭时停止心跳服务
  window.addEventListener('beforeunload', () => {
    heartbeatService.stop();
  });

  app.mount('#app')
})

// 添加全局 CSS 变量
const style = document.createElement('style')
style.textContent = `
:root {
  --primary-color-rgb: 56, 114, 224;
  --surface-card: #ffffff;
  --surface-border: rgba(0, 0, 0, 0.12);
  --surface-hover: rgba(0, 0, 0, 0.04);
  --text-color: rgba(0, 0, 0, 0.87);
  --text-color-secondary: rgba(0, 0, 0, 0.6);
  --surface-200: #f5f5f5;
}

@media (prefers-color-scheme: dark) {
  :root {
    --surface-card: #1e1e1e;
    --surface-border: rgba(255, 255, 255, 0.12);
    --surface-hover: rgba(255, 255, 255, 0.08);
    --text-color: rgba(255, 255, 255, 0.87);
    --text-color-secondary: rgba(255, 255, 255, 0.6);
    --surface-200: #2d2d2d;
  }
}
`
document.head.appendChild(style)

import './assets/main.css'
