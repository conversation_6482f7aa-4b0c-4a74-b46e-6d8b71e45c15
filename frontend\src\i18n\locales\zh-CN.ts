export default {
  app: {
    title: 'API 文档管理',
    footer: {
      powered: '由 Lightbox 强力驱动'
    }
  },
  common: {
    actions: {
      create: '创建',
      edit: '编辑',
      delete: '删除',
      refresh: '刷新',
      save: '保存',
      cancel: '取消',
      confirm: '确认',
      search: '搜索'
    },
    messages: {
      confirmDelete: '确定要删除吗？',
      saveSuccess: '保存成功',
      saveFailed: '保存失败',
      deleteSuccess: '删除成功',
      deleteFailed: '删除失败'
    }
  },
  apiDoc: {
    title: '标题',
    url: 'URL',
    description: '描述',
    createdAt: '创建时间',
    viewCount: '访问次数',
    actions: {
      openInNewTab: '在新标签页打开',
      refreshMetadata: '刷新元数据'
    },
    empty: {
      title: '暂无文档',
      description: '点击右上角的创建按钮添加新文档'
    }
  },
  hero: {
    description: '管理和探索您的 API 文档，创建、组织和分享您的 API。',
    newDoc: '新建文档'
  },
  language: {
    switch: '切换语言'
  },
  search: {
    placeholder: '搜索文档... (共 {total} 个文档)',
    clear: '清除搜索',
    noResults: {
      title: '未找到匹配的文档',
      description: '没有找到与"{query}"相关的文档'
    }
  }
}
