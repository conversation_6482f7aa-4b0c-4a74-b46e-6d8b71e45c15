<template>
  <div class="surface-card border-round-sm shadow-1 p-3 lg:px-4 lg:py-3">
    <div class="mx-auto max-w-4xl flex flex-column gap-3">
      <div class="text-center">
        <p class="text-base lg:text-lg text-900 font-bold line-height-3 mx-auto max-w-30rem m-0" style="color: #000000;">
          {{ $t('hero.description') }}
        </p>
      </div>

      <div class="flex flex-column md:flex-row gap-2 mx-auto w-full max-w-35rem">
        <span class="p-input-icon-left flex-1 w-full">
          <i class="pi pi-search"></i>
          <InputText
            v-model="localSearchQuery"
            :placeholder="$t('search.placeholder', { total: props.totalDocs })"
            class="w-full text-sm lg:text-base"
            @input="handleSearch"
          />
        </span>
        <!-- 隐藏创建按钮
        <Button
          v-if="!localSearchQuery"
          icon="pi pi-plus"
          :label="$t('hero.newDoc')"
          severity="primary"
          size="small"
          class="align-self-center"
          @click="$emit('new')"
        />
        -->
        <Button
          v-if="localSearchQuery"
          icon="pi pi-times"
          size="small"
          class="p-button-text p-button-rounded align-self-center"
          @click="clearSearch"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onBeforeUnmount } from 'vue'
import debounce from 'lodash/debounce'
import { useI18n } from '@/i18n'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'

const { t } = useI18n()

const props = defineProps<{
  totalDocs?: number
  isError?: boolean
  searchQuery?: string
}>()

const emit = defineEmits<{
  (e: 'search', query: string): void
  (e: 'new'): void
}>()

const localSearchQuery = ref(props.searchQuery || '')

// 计算搜索框占位符
const searchPlaceholder = computed(() => {
  const total = props.totalDocs ?? 0
  return t('search.placeholder', { total })
})

// 监听外部 searchQuery 变化
watch(() => props.searchQuery, (newQuery) => {
  if (newQuery !== localSearchQuery.value) {
    localSearchQuery.value = newQuery || ''
  }
})

// 使用 debounce 处理搜索
const debouncedSearch = debounce((query: string) => {
  emit('search', query)
}, 300)

const handleSearch = () => {
  debouncedSearch(localSearchQuery.value)
}

// 清除搜索，暴露给父组件使用
const clearSearch = () => {
  localSearchQuery.value = ''
  emit('search', '')
}

// 组件销毁时取消未执行的 debounce
onBeforeUnmount(() => {
  debouncedSearch.cancel()
})

// 暴露方法给父组件
defineExpose({
  clearSearch
})
</script>

<style scoped>
.surface-card {
  background: var(--surface-card);
  padding: 2rem 1.5rem;
  margin-bottom: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.max-w-5xl {
  max-width: 64rem;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.gap-4 {
  gap: 1rem;
}

.lg\:gap-5 {
  gap: 1.25rem;
}

.text-center {
  text-align: center;
}

.text-lg {
  font-size: 1.125rem;
}

.lg\:text-xl {
  font-size: 1.25rem;
}

.text-color-secondary {
  color: var(--text-color-secondary);
}

.line-height-3 {
  line-height: 1.6;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.max-w-30rem {
  max-width: 30rem;
}

.p-input-icon-left {
  position: relative;
}

.pi.pi-search {
  position: absolute;
  left: 1rem;
  color: var(--text-color-secondary);
}

.w-full {
  width: 100%;
}

.p-3 {
  padding: 0.75rem;
}

.lg\:p-4 {
  padding: 1rem;
}

.text-base {
  font-size: 1rem;
}

.lg\:text-lg {
  font-size: 1.125rem;
}

.p-button-lg {
  padding: 1rem 1.5rem;
  font-size: 1.125rem;
}

.p-button-text {
  background-color: transparent;
  border: none;
  padding: 0.5rem 1rem;
}

.p-button-rounded {
  border-radius: 50%;
}

.p-input-icon-left .p-inputtext {
  padding-left: 2.5rem;
}

/* 桌面端优化 */
@media screen and (min-width: 1024px) {
  .surface-card {
    padding: 3rem 2rem;
    margin-bottom: 2.5rem;
  }

  .flex-column.md\:flex-row {
    flex-direction: row;
  }

  .gap-3 {
    gap: 1rem;
  }

  .mx-auto.max-w-35rem {
    max-width: 35rem;
  }
}



/* 移动端适配 */
@media screen and (max-width: 640px) {
  .surface-card {
    padding: 1.5rem 1rem;
    margin-bottom: 1.5rem;
  }

  .text-lg {
    font-size: 1rem;
  }

  .flex-column.md\:flex-row {
    flex-direction: column;
  }

  .w-full.max-w-35rem {
    max-width: 100%;
  }
}
</style>
