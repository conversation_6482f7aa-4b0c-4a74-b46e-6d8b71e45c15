# 开发环境配置
API_HOST=localhost
API_PORT=8000

# 数据库配置
DB_HOST=************
DB_PORT=5432
DB_NAME=api_docs
DB_USER=api_docs
DB_PASSWORD=your_password

# CORS 配置
CORS_ORIGINS=["http://localhost:3000", "http://api-docs.woa.com:8000", "http://localhost:3005"]

# 太湖认证配置 - 测试环境
TAIHU_CLIENT_ID=test-app
TAIHU_CLIENT_SECRET=ilX3uqWyJRbK
TAIHU_AUTH_URL=https://test-odc.it.woa.com/api/auth-center/oauth2
TAIHU_REDIRECT_URI=http://127.0.0.1:8001/auth/tencent/callback
TAIHU_USERINFO_URL=https://test-odc.it.woa.com/api/auth-center/oauth2/userinfo
TAIHU_LOGOUT_URL=https://test-odc.it.woa.com/logout
TAIHU_ISSUER=https://test-odc.it.woa.com/api/auth-center/oauth2/
TAIHU_TOKEN_URL=https://test-odc.it.woa.com/api/auth-center/oauth2/token

# 前端配置
FRONTEND_URL=http://localhost:3005

# 其他配置
DEBUG=true
LOG_LEVEL=debug
