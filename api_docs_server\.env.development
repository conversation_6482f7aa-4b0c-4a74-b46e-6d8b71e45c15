# 开发环境配置
API_HOST=localhost
API_PORT=8000

# 数据库配置
DB_HOST=************
DB_PORT=5432
DB_NAME=api_docs
DB_USER=api_docs
DB_PASSWORD=your_password

# CORS 配置
CORS_ORIGINS=["http://localhost:3000", "http://api-docs.woa.com:8000"]

# 太湖认证配置
TAIHU_CLIENT_ID=api-docs
TAIHU_CLIENT_SECRET=your_client_secret_here
TAIHU_AUTH_URL=https://tai.it.tencent.com/api/auth-center/oauth2
TAIHU_REDIRECT_URI=https://api-docs.woa.com/taihu_callback

# 前端配置
FRONTEND_URL=http://localhost:3002

# 其他配置
DEBUG=true
LOG_LEVEL=debug
