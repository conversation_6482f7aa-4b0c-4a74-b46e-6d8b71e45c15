import { defineComponent, h } from 'vue';

// 模拟基础组件
export const Dialog = defineComponent({
  name: 'Dialog',
  render() {
    return h('div', { class: 'p-dialog' }, this.$slots.default?.());
  }
});

export const Button = defineComponent({
  name: 'Button',
  render() {
    return h('button', { class: 'p-button' }, this.$slots.default?.());
  }
});

export const EmptyState = defineComponent({
  name: 'EmptyState',
  render() {
    return h('div', { class: 'empty-state' }, this.$slots.default?.());
  }
});

export const ApiDocCard = defineComponent({
  name: 'ApiDocCard',
  props: {
    doc: {
      type: Object,
      required: true
    }
  },
  render() {
    return h('div', { class: 'api-doc-card' }, [
      h('h3', this.doc.title),
      h('p', this.doc.description)
    ]);
  }
});

export const ApiDocHero = defineComponent({
  name: 'ApiDocHero',
  render() {
    return h('div', { class: 'api-doc-hero' }, this.$slots.default?.());
  }
});
