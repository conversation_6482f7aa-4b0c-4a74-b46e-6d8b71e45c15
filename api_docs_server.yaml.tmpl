apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ facility }}-{{ project_configuration.components.backend.label }}
  namespace: lightbox-srv-2
  labels:
    app: {{ facility }}-{{ project_configuration.components.backend.label }}
spec:
  replicas: 2
  selector:
    matchLabels:
      app: {{ facility }}-{{ project_configuration.components.backend.label }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: {{ facility }}-{{ project_configuration.components.backend.label }}
      annotations:
        tke.cloud.tencent.com/networks: tke-route-eni
    spec:
      imagePullSecrets:
      - name: docker-ci
      containers:
      - name: backend
        image: {{ main_configuration.docker_registry }}/lightbox/{{ project_configuration.docker_image.backend.name }}:{{ project_configuration.version }}
        imagePullPolicy: Always
        ports:
        - name: http
          containerPort: 8000
          protocol: TCP
        livenessProbe:
          httpGet:
            path: /api/health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: http
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
            tke.cloud.tencent.com/eni-ip: "1"
        env:
{%- for item in facility_project_configuration.env %}
        - name: {{ item.env_key }}
          value: "{{ item.env_value }}"
{%- endfor %}
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              key: POSTGRES_PASSWORD
              name: {{ facility }}-{{ project_configuration.label_name }}-secret
        - name: ALLOWED_ORIGINS
          value: "https://api-docs.woa.com"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ facility }}-{{ project_configuration.components.frontend.label }}
  namespace: lightbox-srv-2
  labels:
    app: {{ facility }}-{{ project_configuration.components.frontend.label }}
spec:
  replicas: 2
  selector:
    matchLabels:
      app: {{ facility }}-{{ project_configuration.components.frontend.label }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: {{ facility }}-{{ project_configuration.components.frontend.label }}
      annotations:
        tke.cloud.tencent.com/networks: tke-route-eni
    spec:
      imagePullSecrets:
      - name: docker-ci
      containers:
      - name: frontend
        image: {{ main_configuration.docker_registry }}/lightbox/{{ project_configuration.docker_image.frontend.name }}:{{ project_configuration.version }}
        imagePullPolicy: Always
        ports:
        - name: http
          containerPort: 80
          protocol: TCP
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
            tke.cloud.tencent.com/eni-ip: "1"
        env:
{%- for item in facility_project_configuration.env %}
        - name: {{ item.env_key }}
          value: "{{ item.env_value }}"
{%- endfor %}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ facility }}-{{ project_configuration.components.backend.label }}-svc
  labels:
    app: {{ facility }}-{{ project_configuration.components.backend.label }}
  annotations:
    tke.cloud.tencent.com/networks: tke-route-eni
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - name: http
    port: 8000
    targetPort: 8000
    protocol: TCP
  selector:
    app: {{ facility }}-{{ project_configuration.components.backend.label }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ facility }}-{{ project_configuration.components.frontend.label }}-svc
  labels:
    app: {{ facility }}-{{ project_configuration.components.frontend.label }}
  annotations:
    tke.cloud.tencent.com/networks: tke-route-eni
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  selector:
    app: {{ facility }}-{{ project_configuration.components.frontend.label }}
---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: {{ facility }}-{{ project_configuration.label_name }}-ingress
  annotations:
    kubernetes.io/ingress.class: ingress-controller-37898
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "PUT, GET, POST, OPTIONS, DELETE"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/cors-allow-headers: "*"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  rules:
  - host: {{ project_configuration.domain }}
    http:
      paths:
      - path: /backend(/|$)(.*)
        pathType: Prefix
        backend:
          serviceName: {{ facility }}-{{ project_configuration.components.backend.label }}-svc
          servicePort: 8000
      - path: /($|)(.*)
        pathType: Prefix
        backend:
          serviceName: {{ facility }}-{{ project_configuration.components.frontend.label }}-svc
          servicePort: 80
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ facility }}-{{ project_configuration.label_name }}-secret
data:
  POSTGRES_PASSWORD: QXoxMjMhc3NkMTI0eno=
