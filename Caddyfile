{
    # 生产环境配置
    {$APP_ENV}
}

:80 {
    # 启用 gzip 压缩
    encode gzip

    # 前端健康检查路径
    handle /health {
        header Content-Type "text/plain"
        respond 200 "OK"
    }

    # 静态文件服务
    handle {
        root * /usr/share/caddy
        try_files {path} /index.html
        file_server
    }

    # 后端 API 路由
    handle /backend/* {
        # 移除 /backend 前缀
        uri strip_prefix /backend

        # CORS 配置
        header {
            Access-Control-Allow-Origin *
            Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
            Access-Control-Allow-Headers "*"
            Access-Control-Allow-Credentials "true"
            Access-Control-Max-Age "3600"
            defer
        }

        # 反向代理到后端服务
        reverse_proxy sxz-api-docs-server-backend-svc:8000 {
            # 请求头
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-Proto {http.request.scheme}
        }
    }

    # 日志配置
    log {
        output stdout
        format json
        level INFO
    }
}
