import { ref } from 'vue'
import api from '@/api'

export type StatusCallback = (isOnline: boolean) => void | Promise<void>

class HeartbeatService {
  private timer: number | null = null
  private lastStartTime: string | null = null
  private retryCount = 0
  private maxRetries = 5
  private retryDelay = 1000
  private checkInProgress = false
  private reconnectAttempts = 0
  private maxReconnectAttempts = 10
  private baseReconnectDelay = 1000
  private callbacks = new Set<StatusCallback>()
  public isServerOnline = ref(false)

  private async checkServerHealth() {
    if (this.checkInProgress) return

    this.checkInProgress = true
    try {
      await api.get('/health')
      this.isServerOnline.value = true
      this.retryCount = 0
      this.reconnectAttempts = 0
      await this.notifyCallbacks()
    } catch (error) {
      this.isServerOnline.value = false
      if (this.retryCount < this.maxRetries) {
        this.retryCount++
        await this.handleReconnection()
      }
    } finally {
      this.checkInProgress = false
    }
  }

  private async handleReconnection() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached')
      return
    }

    const delay = this.calculateReconnectDelay()
    console.log(`Attempting reconnection in ${delay}ms...`)
    await new Promise(resolve => setTimeout(resolve, delay))
    this.reconnectAttempts++
    await this.checkServerHealth()
  }

  private calculateReconnectDelay(): number {
    // Exponential backoff with jitter
    const jitter = Math.random() * 1000
    return Math.min(
      this.baseReconnectDelay * Math.pow(2, this.reconnectAttempts) + jitter,
      30000
    )
  }

  private async notifyCallbacks() {
    const promises = Array.from(this.callbacks).map(callback =>
      Promise.resolve(callback(this.isServerOnline.value))
    )
    await Promise.all(promises)
  }

  public start() {
    if (this.timer !== null) return

    const now = new Date().toISOString()
    this.lastStartTime = now
    const currentStartTime = now

    const runCheck = async () => {
      if (this.lastStartTime !== currentStartTime) return
      await this.checkServerHealth()
      if (this.lastStartTime === currentStartTime) {
        this.timer = window.setTimeout(runCheck, 30000)
      }
    }

    runCheck()
  }

  public stop() {
    if (this.timer !== null) {
      clearTimeout(this.timer)
      this.timer = null
    }
    this.lastStartTime = null
  }

  public addStatusCallback(callback: StatusCallback) {
    this.callbacks.add(callback)
  }

  public removeStatusCallback(callback: StatusCallback) {
    this.callbacks.delete(callback)
  }

  public isRunning(): boolean {
    return this.timer !== null
  }
}

export const heartbeatService = new HeartbeatService()
