import { describe, it, expect } from 'vitest';
import { handleApiError, formatErrorMessage } from '../errorHandler';
import axios from 'axios';

describe('Error Handler', () => {
  describe('handleApiError', () => {
    it('should handle axios error with response data', () => {
      const error = {
        isAxiosError: true,
        response: {
          status: 400,
          data: {
            detail: 'Bad request'
          }
        },
        message: 'Request failed'
      };

      expect(() => handleApiError(error)).toThrow('Bad request');
    });

    it('should handle network error', () => {
      const error = {
        isAxiosError: true,
        message: 'Network Error',
        code: 'ECONNABORTED',
        response: undefined
      };

      expect(() => handleApiError(error)).toThrow('Network Error');
    });

    it('should handle timeout error', () => {
      const error = {
        isAxiosError: true,
        message: 'Timeout',
        code: 'ECONNABORTED',
        response: undefined
      };

      expect(() => handleApiError(error)).toThrow('Request timeout');
    });
  });

  describe('formatErrorMessage', () => {
    it('should format string error', () => {
      const error = 'Test error';
      expect(formatErrorMessage(error)).toBe('Test error');
    });

    it('should format Error object', () => {
      const error = new Error('Test error');
      expect(formatErrorMessage(error)).toBe('Test error');
    });

    it('should format unknown error', () => {
      const error = { custom: 'error' };
      expect(formatErrorMessage(error)).toBe('An unknown error occurred');
    });
  });
});
