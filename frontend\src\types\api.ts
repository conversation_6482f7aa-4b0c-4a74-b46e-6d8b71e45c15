// API 响应类型
export interface ApiResponse<T> {
  data: T;
  status: number;
  message?: string;
}

// API 文档类型
export interface ApiDoc {
  id: string  // UUID 字符串
  title: string
  url: string
  description?: string
  icon_url?: string
  icon_data?: string
  auto_metadata?: boolean
  created_at: string
  updated_at: string
  metadata_updated_at?: string
  view_count: number
}

// 创建文档请求类型
export interface CreateDocRequest {
  url: string
  title: string
  description?: string
  icon_url?: string
  icon_data?: string
  auto_metadata?: boolean
}

// 更新文档请求类型
export interface UpdateDocRequest {
  title?: string
  url?: string
  description?: string
  icon_url?: string
  icon_data?: string
  auto_metadata?: boolean
}

// URL验证请求类型
export interface ValidateUrlRequest {
  url: string
}

// URL验证响应类型
export interface ValidateUrlResponse {
  is_valid: boolean
  message?: string
}

// 分页查询参数
export interface PaginationParams {
  page?: number
  per_page?: number
}

// 健康检查响应类型
export interface HealthCheckResponse {
  status: string
  uptime_seconds: number
  start_time: string
}
