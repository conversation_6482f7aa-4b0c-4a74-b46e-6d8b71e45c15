<template>
  <div class="surface-card border-round shadow-2 flex flex-column align-items-center justify-content-center p-4 lg:p-6 my-4 lg:my-6 text-center">
    <i :class="[icon, 'text-4xl lg:text-5xl text-color-secondary mb-3 lg:mb-4']"></i>
    <h3 class="text-xl lg:text-2xl font-semibold text-900 mb-2 lg:mb-3">{{ $t('search.noResults.title') }}</h3>
    <p class="text-base lg:text-lg text-color-secondary line-height-3 max-w-30rem">{{ $t('search.noResults.description', { query: searchQuery }) }}
      <br>
      <Button
        v-if="showClearButton"
        :label="$t('search.clear')"
        icon="pi pi-times"
        severity="secondary"
        class="p-button-outlined mt-4"
        @click="$emit('clear')"
      />
    </p>
  </div>
</template>

<script setup lang="ts">
import Button from 'primevue/button'

interface EmptyStateProps {
  icon?: string
  searchQuery: string
  showClearButton?: boolean
}

defineProps<EmptyStateProps>()

defineEmits<{
  (e: 'clear'): void
}>()
</script>

<style scoped>
/* 桌面端优化 */
@media screen and (min-width: 1024px) {
  :deep(.p-button) {
    margin-top: 2rem;
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
  }
}

/* 移动端适配 */
@media screen and (max-width: 640px) {
  :deep(.p-button) {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}
</style>
