# 前端 API 接口文档

## 目录结构

```
frontend/src/api/
├── apiService.ts  # API服务类
├── axios.ts       # Axios配置
└── errorHandler.ts # 错误处理

frontend/src/types/
└── api.ts         # API类型定义
```

## 类型定义

### 基础类型

```typescript
// API 响应类型
interface ApiResponse<T> {
  data: T;
  status: number;
  message?: string;
}

// API 文档类型
interface ApiDoc {
  id: number;
  url: string;
  title?: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

// 创建文档请求类型
interface CreateDocRequest {
  url: string;
  title?: string;
  description?: string;
}

// 更新文档请求类型
interface UpdateDocRequest {
  title?: string;
  description?: string;
}

// URL验证请求类型
interface ValidateUrlRequest {
  url: string;
}

// URL验证响应类型
interface ValidateUrlResponse {
  valid: boolean;
}

// 分页查询参数
interface PaginationParams {
  skip?: number;
  limit?: number;
}

// 健康检查响应类型
interface HealthCheckResponse {
  status: string;
  start_time: string;
  uptime_seconds: number;
}
```

## API 服务

### ApiDocService

文档管理相关的API服务类。

#### 获取文档列表
```typescript
static async getDocs(params?: PaginationParams): Promise<ApiDoc[]>
```
- **参数**：
  - `params`: 可选的分页参数
    - `skip`: 跳过的记录数
    - `limit`: 返回的最大记录数
- **返回值**：文档列表

#### 获取单个文档
```typescript
static async getDoc(id: number): Promise<ApiDoc>
```
- **参数**：
  - `id`: 文档ID
- **返回值**：文档详情

#### 创建文档
```typescript
static async createDoc(doc: CreateDocRequest): Promise<ApiDoc>
```
- **参数**：
  - `doc`: 文档信息
    - `url`: 文档URL（必填）
    - `title`: 文档标题（可选）
    - `description`: 文档描述（可选）
- **返回值**：创建的文档

#### 更新文档
```typescript
static async updateDoc(id: number, doc: UpdateDocRequest): Promise<ApiDoc>
```
- **参数**：
  - `id`: 文档ID
  - `doc`: 更新的文档信息
    - `title`: 文档标题（可选）
    - `description`: 文档描述（可选）
- **返回值**：更新后的文档

#### 删除文档
```typescript
static async deleteDoc(id: number): Promise<void>
```
- **参数**：
  - `id`: 文档ID

#### 验证URL
```typescript
static async validateUrl(url: string): Promise<ValidateUrlResponse>
```
- **参数**：
  - `url`: 要验证的URL
- **返回值**：验证结果

### HealthService

健康检查相关的API服务类。

#### 检查服务健康状态
```typescript
static async checkHealth(): Promise<HealthCheckResponse>
```
- **返回值**：健康状态信息

## 错误处理

### ApiError 类
```typescript
class ApiError extends Error {
  status: number;
  code?: string;
}
```

### 错误处理函数

#### handleApiError
```typescript
function handleApiError(error: AxiosError): never
```
- **参数**：
  - `error`: Axios错误对象
- **抛出**：处理后的ApiError

#### formatErrorMessage
```typescript
function formatErrorMessage(error: unknown): string
```
- **参数**：
  - `error`: 错误对象
- **返回值**：格式化后的错误消息

## 使用示例

### 基本用法
```typescript
import { apiDocService, healthService } from '@/api/apiService';
import { formatErrorMessage } from '@/api/errorHandler';

// 获取文档列表
try {
  const docs = await apiDocService.getDocs({ skip: 0, limit: 10 });
  // 处理文档列表
} catch (error) {
  console.error(formatErrorMessage(error));
}

// 创建新文档
try {
  const newDoc = await apiDocService.createDoc({
    url: 'https://api.example.com',
    title: '示例API',
    description: '这是一个示例API文档'
  });
  // 处理新创建的文档
} catch (error) {
  console.error(formatErrorMessage(error));
}

// 检查服务健康状态
try {
  const health = await healthService.checkHealth();
  // 处理健康状态
} catch (error) {
  console.error(formatErrorMessage(error));
}
```

### 在Vue组件中使用
```typescript
import { defineComponent, ref, onMounted } from 'vue';
import { apiDocService } from '@/api/apiService';
import type { ApiDoc } from '@/types/api';

export default defineComponent({
  setup() {
    const docs = ref<ApiDoc[]>([]);
    const loading = ref(false);
    const error = ref<string | null>(null);

    const fetchDocs = async () => {
      loading.value = true;
      error.value = null;
      try {
        docs.value = await apiDocService.getDocs();
      } catch (err) {
        error.value = formatErrorMessage(err);
      } finally {
        loading.value = false;
      }
    };

    onMounted(fetchDocs);

    return {
      docs,
      loading,
      error
    };
  }
});
```

## 注意事项

1. 所有API调用都应该使用try-catch包装，并使用formatErrorMessage处理错误
2. 在组件中使用时，建议处理加载状态和错误状态
3. 分页查询时注意合理设置skip和limit参数
4. URL验证会在创建文档时自动进行
5. 所有时间字段均使用ISO 8601格式

## 环境配置

API基础URL配置在axios.ts中：
```typescript
const axiosInstance = axios.create({
  baseURL: 'http://localhost:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});
```
