import {
  PrimeVueToastSymbol
} from "./chunk-G7QTARBN.js";
import {
  ToastEventBus
} from "./chunk-2LGST6PE.js";
import "./chunk-LQERBOIJ.js";
import "./chunk-U3LI7FBV.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/primevue/toastservice/index.mjs
var ToastService = {
  install: function install(app) {
    var ToastService2 = {
      add: function add(message) {
        ToastEventBus.emit("add", message);
      },
      remove: function remove(message) {
        ToastEventBus.emit("remove", message);
      },
      removeGroup: function removeGroup(group) {
        ToastEventBus.emit("remove-group", group);
      },
      removeAllGroups: function removeAllGroups() {
        ToastEventBus.emit("remove-all-groups");
      }
    };
    app.config.globalProperties.$toast = ToastService2;
    app.provide(PrimeVueToastSymbol, ToastService2);
  }
};
export {
  ToastService as default
};
//# sourceMappingURL=primevue_toastservice.js.map
