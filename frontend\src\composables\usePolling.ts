import { ref, onUnmounted } from 'vue'
import type { Ref } from 'vue'

export interface PollingOptions {
  interval?: number
  maxRetries?: number
  immediate?: boolean
  retryDelay?: number
}

export function usePolling<T>(
  callback: () => Promise<T>,
  options: PollingOptions = {}
) {
  const {
    interval = 10000,
    maxRetries = 3,
    immediate = true,
    retryDelay = 1000
  } = options

  const isPolling = ref(false)
  const error = ref<Error | null>(null)
  const retryCount = ref(0)
  let timer: NodeJS.Timeout | null = null
  let isDestroyed = false

  async function start() {
    if (isPolling.value || isDestroyed) return

    isPolling.value = true
    error.value = null
    retryCount.value = 0

    try {
      await poll()
    } catch (err) {
      error.value = err as Error
      isPolling.value = false
    }
  }

  function stop() {
    if (timer) {
      clearTimeout(timer)
      timer = null
    }
    isPolling.value = false
    error.value = null
    retryCount.value = 0
  }

  async function poll() {
    if (isDestroyed) return

    try {
      await callback()
      retryCount.value = 0

      if (isPolling.value && !isDestroyed) {
        timer = setTimeout(poll, interval)
      }
    } catch (err) {
      error.value = err as Error
      retryCount.value++

      if (retryCount.value <= maxRetries && !isDestroyed) {
        // 使用指数退避策略
        const delay = retryDelay * Math.pow(2, retryCount.value - 1)
        timer = setTimeout(poll, delay)
      } else {
        isPolling.value = false
        throw err
      }
    }
  }

  if (immediate) {
    start()
  }

  onUnmounted(() => {
    isDestroyed = true
    stop()
  })

  return {
    isPolling,
    error,
    retryCount,
    start,
    stop
  }
}
