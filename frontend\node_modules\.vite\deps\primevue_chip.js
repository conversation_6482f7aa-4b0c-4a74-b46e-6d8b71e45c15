import {
  script as script2
} from "./chunk-4BDOVYAD.js";
import "./chunk-RQPT333Y.js";
import {
  script
} from "./chunk-BXQMK4YT.js";
import "./chunk-B73TEBOQ.js";
import {
  BaseStyle
} from "./chunk-M2QMAZBN.js";
import "./chunk-LQERBOIJ.js";
import {
  createBlock,
  createCommentVNode,
  createElementBlock,
  mergeProps,
  openBlock,
  renderSlot,
  resolveDynamicComponent,
  toDisplayString
} from "./chunk-U3LI7FBV.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/primevue/chip/style/index.mjs
var theme = function theme2(_ref) {
  var dt = _ref.dt;
  return "\n.p-chip {\n    display: inline-flex;\n    align-items: center;\n    background: ".concat(dt("chip.background"), ";\n    color: ").concat(dt("chip.color"), ";\n    border-radius: ").concat(dt("chip.border.radius"), ";\n    padding-block: ").concat(dt("chip.padding.y"), ";\n    padding-inline: ").concat(dt("chip.padding.x"), ";\n    gap: ").concat(dt("chip.gap"), ";\n}\n\n.p-chip-icon {\n    color: ").concat(dt("chip.icon.color"), ";\n    font-size: ").concat(dt("chip.icon.font.size"), ";\n    width: ").concat(dt("chip.icon.size"), ";\n    height: ").concat(dt("chip.icon.size"), ";\n}\n\n.p-chip-image {\n    border-radius: 50%;\n    width: ").concat(dt("chip.image.width"), ";\n    height: ").concat(dt("chip.image.height"), ";\n    margin-inline-start: calc(-1 * ").concat(dt("chip.padding.y"), ");\n}\n\n.p-chip:has(.p-chip-remove-icon) {\n    padding-inline-end: ").concat(dt("chip.padding.y"), ";\n}\n\n.p-chip:has(.p-chip-image) {\n    padding-block-start: calc(").concat(dt("chip.padding.y"), " / 2);\n    padding-block-end: calc(").concat(dt("chip.padding.y"), " / 2);\n}\n\n.p-chip-remove-icon {\n    cursor: pointer;\n    font-size: ").concat(dt("chip.remove.icon.size"), ";\n    width: ").concat(dt("chip.remove.icon.size"), ";\n    height: ").concat(dt("chip.remove.icon.size"), ";\n    color: ").concat(dt("chip.remove.icon.color"), ";\n    border-radius: 50%;\n    transition: outline-color ").concat(dt("chip.transition.duration"), ", box-shadow ").concat(dt("chip.transition.duration"), ";\n    outline-color: transparent;\n}\n\n.p-chip-remove-icon:focus-visible {\n    box-shadow: ").concat(dt("chip.remove.icon.focus.ring.shadow"), ";\n    outline: ").concat(dt("chip.remove.icon.focus.ring.width"), " ").concat(dt("chip.remove.icon.focus.ring.style"), " ").concat(dt("chip.remove.icon.focus.ring.color"), ";\n    outline-offset: ").concat(dt("chip.remove.icon.focus.ring.offset"), ";\n}\n");
};
var classes = {
  root: "p-chip p-component",
  image: "p-chip-image",
  icon: "p-chip-icon",
  label: "p-chip-label",
  removeIcon: "p-chip-remove-icon"
};
var ChipStyle = BaseStyle.extend({
  name: "chip",
  theme,
  classes
});

// node_modules/primevue/chip/index.mjs
var script$1 = {
  name: "BaseChip",
  "extends": script,
  props: {
    label: {
      type: String,
      "default": null
    },
    icon: {
      type: String,
      "default": null
    },
    image: {
      type: String,
      "default": null
    },
    removable: {
      type: Boolean,
      "default": false
    },
    removeIcon: {
      type: String,
      "default": void 0
    }
  },
  style: ChipStyle,
  provide: function provide() {
    return {
      $pcChip: this,
      $parentInstance: this
    };
  }
};
var script3 = {
  name: "Chip",
  "extends": script$1,
  inheritAttrs: false,
  emits: ["remove"],
  data: function data() {
    return {
      visible: true
    };
  },
  methods: {
    onKeydown: function onKeydown(event) {
      if (event.key === "Enter" || event.key === "Backspace") {
        this.close(event);
      }
    },
    close: function close(event) {
      this.visible = false;
      this.$emit("remove", event);
    }
  },
  components: {
    TimesCircleIcon: script2
  }
};
var _hoisted_1 = ["aria-label"];
var _hoisted_2 = ["src"];
function render(_ctx, _cache, $props, $setup, $data, $options) {
  return $data.visible ? (openBlock(), createElementBlock("div", mergeProps({
    key: 0,
    "class": _ctx.cx("root"),
    "aria-label": _ctx.label
  }, _ctx.ptmi("root")), [renderSlot(_ctx.$slots, "default", {}, function() {
    return [_ctx.image ? (openBlock(), createElementBlock("img", mergeProps({
      key: 0,
      src: _ctx.image
    }, _ctx.ptm("image"), {
      "class": _ctx.cx("image")
    }), null, 16, _hoisted_2)) : _ctx.$slots.icon ? (openBlock(), createBlock(resolveDynamicComponent(_ctx.$slots.icon), mergeProps({
      key: 1,
      "class": _ctx.cx("icon")
    }, _ctx.ptm("icon")), null, 16, ["class"])) : _ctx.icon ? (openBlock(), createElementBlock("span", mergeProps({
      key: 2,
      "class": [_ctx.cx("icon"), _ctx.icon]
    }, _ctx.ptm("icon")), null, 16)) : createCommentVNode("", true), _ctx.label ? (openBlock(), createElementBlock("div", mergeProps({
      key: 3,
      "class": _ctx.cx("label")
    }, _ctx.ptm("label")), toDisplayString(_ctx.label), 17)) : createCommentVNode("", true)];
  }), _ctx.removable ? renderSlot(_ctx.$slots, "removeicon", {
    key: 0,
    removeCallback: $options.close,
    keydownCallback: $options.onKeydown
  }, function() {
    return [(openBlock(), createBlock(resolveDynamicComponent(_ctx.removeIcon ? "span" : "TimesCircleIcon"), mergeProps({
      "class": [_ctx.cx("removeIcon"), _ctx.removeIcon],
      onClick: $options.close,
      onKeydown: $options.onKeydown
    }, _ctx.ptm("removeIcon")), null, 16, ["class", "onClick", "onKeydown"]))];
  }) : createCommentVNode("", true)], 16, _hoisted_1)) : createCommentVNode("", true);
}
script3.render = render;
export {
  script3 as default
};
//# sourceMappingURL=primevue_chip.js.map
