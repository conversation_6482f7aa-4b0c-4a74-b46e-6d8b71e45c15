前端开发指南
==========

技术栈
-----
* Vue 3
* TypeScript
* Vite
* PrimeVue

项目结构
-------

目录说明
~~~~~~~
.. code-block:: text

   frontend/src/
     ├─ types/              # 全局类型定义，按模块分文件（如 user.d.ts）
     ├─ plugins/
     │   └─ primevue.ts    # PrimeVue 初始化配置
     └─ vite-env.d.ts      # Vite 环境变量类型扩展

Vite 配置规范
-----------

构建规则
~~~~~~~
* build.cssTarget 设置为 "chrome80"（兼容 PrimeVue 主题）
* 使用 build.rollupOptions.output.manualChunks 按路由分块
* 禁用 build.cssCodeSplit（保持主题完整性）
* 必须包含 vite-plugin-checker 用于 TS 类型检查

TypeScript 规范
-------------

组件开发规范
~~~~~~~~~~
* 使用 PrimeVue 组件时必须导入对应类型（如 ButtonProps）
* 组件 props 必须使用 interface 定义，禁止内联类型
* 自定义事件必须使用类型声明（通过 defineEmits<T>()）
* 全局注册的 PrimeVue 组件需在 src/types/components.d.ts 中扩展类型

依赖管理
-------

npm 规范
~~~~~~~
* PrimeVue 相关依赖必须固定版本（禁止使用 ^ 或 ~）
* 强制使用 npm install --save-exact 安装依赖
* package-lock.json 必须提交且每周执行 npm audit
* 检查 PrimeVue 与 Vue/TypeScript 的版本兼容性

性能优化
-------

基本原则
~~~~~~~
* 必须使用 Vite 动态导入语法：() => import(/* @vite-ignore */ '...')
* PrimeVue 图标必须通过 npm 导入，禁止使用本地复制

代码风格
-------

基本要求
~~~~~~~
* 强制使用 <script setup lang="ts"> 语法
* 类型导入必须使用 import type 语法
* 禁止在 PrimeVue 事件处理函数中使用 any 类型
* 公共类型必须放在 src/types/ 目录下

安全规范
-------

安全要求
~~~~~~~
* 使用 PrimeVue 富文本组件时必须配置 :sanitize="false" 并手动调用 DOMPurify
* 使用 v-html 的组件必须添加 @vite-ignore 注释并提交安全审查

自动化工具
--------

配置要求
~~~~~~~
* ESLint 必须包含以下规则：
    - @typescript-eslint/no-explicit-any: error
    - vue/require-prop-types: error
* Prettier 配置必须与 .vscode/settings.json 中的格式化配置一致
* Commit 消息格式：[scope] description，scope 包含 [prime] 或 [vite] 时触发专项检查

CI/CD 规范
---------

流水线要求
~~~~~~~~
GitHub Actions 必须包含以下步骤：

1. npm run type-check
2. npm run build -- --mode=production
3. npx primevue-optimize-analyzer --bundle
4. npm audit --audit-level=moderate
