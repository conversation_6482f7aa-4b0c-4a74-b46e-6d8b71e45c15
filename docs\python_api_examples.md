# Python API 使用示例

## API 文档管理接口

### 1. 模型定义

```python
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, HttpUrl

class ApiDocBase(BaseModel):
    """API文档基础模型"""
    title: str
    url: HttpUrl
    description: Optional[str] = None
    tags: List[str] = []

class ApiDocCreate(ApiDocBase):
    """创建API文档模型"""
    pass

class ApiDocUpdate(BaseModel):
    """更新API文档模型"""
    title: Optional[str] = None
    url: Optional[HttpUrl] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = None

class ApiDoc(ApiDocBase):
    """API文档完整模型"""
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True
```

### 2. CRUD 操作

```python
from typing import List, Optional
from fastapi import HTTPException, status
from sqlalchemy.orm import Session
from .models import ApiDoc
from .schemas import ApiDocCreate, ApiDocUpdate

class ApiDocCRUD:
    """API文档CRUD操作类"""

    @staticmethod
    async def create_doc(db: Session, doc: ApiDocCreate) -> ApiDoc:
        """创建新文档"""
        try:
            db_doc = ApiDoc(**doc.dict())
            db.add(db_doc)
            await db.commit()
            await db.refresh(db_doc)
            return db_doc
        except Exception as e:
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"创建文档失败: {str(e)}"
            )

    @staticmethod
    async def update_doc(
        db: Session,
        doc_id: str,
        doc_update: ApiDocUpdate
    ) -> Optional[ApiDoc]:
        """更新文档"""
        try:
            db_doc = await db.query(ApiDoc).filter(ApiDoc.id == doc_id).first()
            if not db_doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"未找到ID为{doc_id}的文档"
                )

            update_data = doc_update.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(db_doc, field, value)

            db_doc.updated_at = datetime.utcnow()
            await db.commit()
            await db.refresh(db_doc)
            return db_doc
        except HTTPException:
            raise
        except Exception as e:
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"更新文档失败: {str(e)}"
            )

    @staticmethod
    async def delete_doc(db: Session, doc_id: str) -> bool:
        """删除文档"""
        try:
            db_doc = await db.query(ApiDoc).filter(ApiDoc.id == doc_id).first()
            if not db_doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"未找到ID为{doc_id}的文档"
                )

            await db.delete(db_doc)
            await db.commit()
            return True
        except HTTPException:
            raise
        except Exception as e:
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"删除文档失败: {str(e)}"
            )

    @staticmethod
    async def get_docs(
        db: Session,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None
    ) -> List[ApiDoc]:
        """获取文档列表"""
        query = db.query(ApiDoc)

        if search:
            search = f"%{search}%"
            query = query.filter(
                ApiDoc.title.ilike(search) |
                ApiDoc.description.ilike(search) |
                ApiDoc.url.ilike(search)
            )

        return await query.offset(skip).limit(limit).all()

    @staticmethod
    async def get_doc_by_id(db: Session, doc_id: str) -> Optional[ApiDoc]:
        """通过ID获取文档"""
        return await db.query(ApiDoc).filter(ApiDoc.id == doc_id).first()
```

### 3. API路由

```python
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from .dependencies import get_db
from .crud import ApiDocCRUD
from .schemas import ApiDoc, ApiDocCreate, ApiDocUpdate

router = APIRouter(prefix="/api/docs", tags=["documents"])

@router.post("/", response_model=ApiDoc)
async def create_doc(
    doc: ApiDocCreate,
    db: Session = Depends(get_db)
) -> ApiDoc:
    """创建新文档"""
    return await ApiDocCRUD.create_doc(db, doc)

@router.patch("/{doc_id}", response_model=ApiDoc)
async def update_doc(
    doc_id: str,
    doc_update: ApiDocUpdate,
    db: Session = Depends(get_db)
) -> ApiDoc:
    """更新文档"""
    return await ApiDocCRUD.update_doc(db, doc_id, doc_update)

@router.delete("/{doc_id}")
async def delete_doc(
    doc_id: str,
    db: Session = Depends(get_db)
) -> dict:
    """删除文档"""
    success = await ApiDocCRUD.delete_doc(db, doc_id)
    return {"success": success, "message": "文档已删除"}

@router.get("/", response_model=List[ApiDoc])
async def get_docs(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    search: Optional[str] = None,
    db: Session = Depends(get_db)
) -> List[ApiDoc]:
    """获取文档列表"""
    return await ApiDocCRUD.get_docs(db, skip, limit, search)

@router.get("/{doc_id}", response_model=ApiDoc)
async def get_doc(
    doc_id: str,
    db: Session = Depends(get_db)
) -> ApiDoc:
    """获取单个文档"""
    doc = await ApiDocCRUD.get_doc_by_id(db, doc_id)
    if not doc:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到ID为{doc_id}的文档"
        )
    return doc
```

### 4. 使用示例

```python
import httpx
from typing import Optional
from pydantic import HttpUrl

class ApiDocClient:
    """API文档客户端"""

    def __init__(self, base_url: str, api_key: Optional[str] = None):
        self.base_url = base_url.rstrip('/')
        self.headers = {
            'Content-Type': 'application/json'
        }
        if api_key:
            self.headers['Authorization'] = f'Bearer {api_key}'

    async def create_doc(self, title: str, url: HttpUrl, description: Optional[str] = None) -> dict:
        """创建新文档"""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/docs",
                headers=self.headers,
                json={
                    "title": title,
                    "url": str(url),
                    "description": description
                }
            )
            response.raise_for_status()
            return response.json()

    async def update_doc(self, doc_id: str, **updates) -> dict:
        """更新文档"""
        async with httpx.AsyncClient() as client:
            response = await client.patch(
                f"{self.base_url}/api/docs/{doc_id}",
                headers=self.headers,
                json=updates
            )
            response.raise_for_status()
            return response.json()

    async def delete_doc(self, doc_id: str) -> dict:
        """删除文档"""
        async with httpx.AsyncClient() as client:
            response = await client.delete(
                f"{self.base_url}/api/docs/{doc_id}",
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()

    async def get_docs(self, skip: int = 0, limit: int = 100, search: Optional[str] = None) -> list:
        """获取文档列表"""
        params = {"skip": skip, "limit": limit}
        if search:
            params["search"] = search

        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/api/docs",
                headers=self.headers,
                params=params
            )
            response.raise_for_status()
            return response.json()

# 使用示例
async def main():
    client = ApiDocClient("http://localhost:8000", api_key="your-api-key")

    # 创建文档
    new_doc = await client.create_doc(
        title="示例API文档",
        url="https://api.example.com/docs",
        description="这是一个示例API文档"
    )
    print("创建的文档:", new_doc)

    # 更新文档
    updated_doc = await client.update_doc(
        new_doc["id"],
        title="更新后的标题"
    )
    print("更新后的文档:", updated_doc)

    # 获取文档列表
    docs = await client.get_docs(limit=10)
    print("文档列表:", docs)

    # 删除文档
    result = await client.delete_doc(new_doc["id"])
    print("删除结果:", result)

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
```

## 注意事项

1. 异常处理
   - 使用自定义异常类
   - 合理处理数据库异常
   - 提供清晰的错误信息

2. 数据验证
   - 使用Pydantic模型
   - 验证输入数据
   - 类型提示和检查

3. 数据库操作
   - 使用事务管理
   - 正确处理会话
   - 异步操作注意事项

4. 安全性
   - API认证
   - 输入验证
   - CORS设置

5. 性能优化
   - 使用异步操作
   - 合理的分页
   - 缓存策略
