# Import built-in modules
from datetime import datetime
from typing import Optional

# Import third-party modules
from pydantic import BaseModel


class ApiDocBase(BaseModel):
    name: str
    description: Optional[str] = None
    url: str


class ApiDocCreate(ApiDocBase):
    pass


class ApiDocUpdate(ApiDocBase):
    name: Optional[str] = None
    url: Optional[str] = None


class ApiDoc(ApiDocBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
