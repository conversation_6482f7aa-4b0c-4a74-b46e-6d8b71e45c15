/// <reference types="vitest" />
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    include: ['src/**/*.{test,spec}.{js,ts}'],
    coverage: {
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/setup.ts',
      ],
    },
    testTimeout: 10000,  // 设置测试超时时间为10秒
    hookTimeout: 10000,  // 设置钩子函数超时时间为10秒
    pool: 'forks',      // 使用进程池来运行测试
    poolOptions: {
      threads: {
        singleThread: true,  // 使用单线程模式
      },
    },
  },
});
