import { createI18n } from 'vue-i18n'
import enUS from './locales/en-US'
import zhCN from './locales/zh-CN'

// PrimeVue 内置语言
import { usePrimeVue } from 'primevue/config'
import { useCurrentInstance } from '@/composables/useCurrentInstance'

// 创建 i18n 实例
const i18n = createI18n({
  legacy: false, // 使用 Composition API 模式
  locale: 'zh-CN', // 默认语言
  fallbackLocale: 'en-US', // 备用语言
  messages: {
    'en-US': enUS,
    'zh-CN': zhCN
  }
})

// 语言切换函数
export const useI18n = () => {
  const { proxy } = useCurrentInstance()
  const primeVue = usePrimeVue()

  const setLocale = async (locale: string) => {
    // 1. 切换 Vue I18n 语言
    i18n.global.locale.value = locale
    
    // 2. 切换 PrimeVue 语言
    const primeLang = locale.toLowerCase().replace('-', '')
    try {
      const module = await import(/* @vite-ignore */ `primevue/locales/${primeLang}.json`)
      primeVue.config.locale = module.default
    } catch (err) {
      console.warn(`Failed to load PrimeVue locale: ${locale}`, err)
    }

    // 3. 保存用户偏好
    localStorage.setItem('locale', locale)
  }

  // 初始化语言
  const initLocale = () => {
    const savedLocale = localStorage.getItem('locale')
    if (savedLocale) {
      setLocale(savedLocale)
    }
  }

  return {
    setLocale,
    initLocale
  }
}

export default i18n
