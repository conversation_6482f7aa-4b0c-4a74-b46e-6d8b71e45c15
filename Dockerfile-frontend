# Build frontend
FROM --platform=linux/amd64 node:20-alpine as builder

# Set working directory
WORKDIR /app/frontend

# Set CI environment variable
ENV CI=1

# Install dependencies first (better layer caching)
COPY frontend/package.json frontend/package-lock.json* ./

# Install dependencies
RUN npm install

# Copy all configuration files first
COPY frontend/tsconfig*.json ./
COPY frontend/vite.config.ts ./
COPY frontend/.env* ./
COPY frontend/index.html ./

# Copy source code and scripts
COPY frontend/src ./src
COPY frontend/public ./public
COPY frontend/scripts ./scripts

# Copy font files
COPY frontend/src/assets/fonts ./src/assets/fonts

# Create temp directory for font processing
RUN mkdir -p .temp/fonts

# Build the app (in CI mode)
RUN npm run build

# Production stage
FROM --platform=linux/amd64 caddy:2.7-alpine

# Copy Caddy configuration
COPY Caddyfile /etc/caddy/Caddyfile

# Copy built assets from builder stage
COPY --from=builder /app/frontend/dist /usr/share/caddy

# Create a simple health check file
RUN echo "OK" > /usr/share/caddy/health

# Set up Caddy
EXPOSE 80
EXPOSE 443

# Add healthcheck
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost/health || exit 1

# Set Caddy as entrypoint
ENTRYPOINT ["caddy"]
CMD ["run", "--config", "/etc/caddy/Caddyfile", "--adapter", "caddyfile"]
