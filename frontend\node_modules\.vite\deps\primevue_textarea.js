import {
  script
} from "./chunk-BLJE6ZRB.js";
import "./chunk-CXETA7UJ.js";
import "./chunk-47K6FRN7.js";
import "./chunk-IDNK5ZN5.js";
import "./chunk-B73TEBOQ.js";
import {
  BaseStyle
} from "./chunk-TZR35JV3.js";
import "./chunk-LQERBOIJ.js";
import {
  createElementBlock,
  mergeProps,
  openBlock
} from "./chunk-U3LI7FBV.js";
import "./chunk-HKJ2B2AA.js";

// node_modules/primevue/textarea/style/index.mjs
var theme = function theme2(_ref) {
  var dt = _ref.dt;
  return "\n.p-textarea {\n    font-family: inherit;\n    font-feature-settings: inherit;\n    font-size: 1rem;\n    color: ".concat(dt("textarea.color"), ";\n    background: ").concat(dt("textarea.background"), ";\n    padding-block: ").concat(dt("textarea.padding.y"), ";\n    padding-inline: ").concat(dt("textarea.padding.x"), ";\n    border: 1px solid ").concat(dt("textarea.border.color"), ";\n    transition: background ").concat(dt("textarea.transition.duration"), ", color ").concat(dt("textarea.transition.duration"), ", border-color ").concat(dt("textarea.transition.duration"), ", outline-color ").concat(dt("textarea.transition.duration"), ", box-shadow ").concat(dt("textarea.transition.duration"), ";\n    appearance: none;\n    border-radius: ").concat(dt("textarea.border.radius"), ";\n    outline-color: transparent;\n    box-shadow: ").concat(dt("textarea.shadow"), ";\n}\n\n.p-textarea:enabled:hover {\n    border-color: ").concat(dt("textarea.hover.border.color"), ";\n}\n\n.p-textarea:enabled:focus {\n    border-color: ").concat(dt("textarea.focus.border.color"), ";\n    box-shadow: ").concat(dt("textarea.focus.ring.shadow"), ";\n    outline: ").concat(dt("textarea.focus.ring.width"), " ").concat(dt("textarea.focus.ring.style"), " ").concat(dt("textarea.focus.ring.color"), ";\n    outline-offset: ").concat(dt("textarea.focus.ring.offset"), ";\n}\n\n.p-textarea.p-invalid {\n    border-color: ").concat(dt("textarea.invalid.border.color"), ";\n}\n\n.p-textarea.p-variant-filled {\n    background: ").concat(dt("textarea.filled.background"), ";\n}\n\n.p-textarea.p-variant-filled:enabled:focus {\n    background: ").concat(dt("textarea.filled.focus.background"), ";\n}\n\n.p-textarea:disabled {\n    opacity: 1;\n    background: ").concat(dt("textarea.disabled.background"), ";\n    color: ").concat(dt("textarea.disabled.color"), ";\n}\n\n.p-textarea::placeholder {\n    color: ").concat(dt("textarea.placeholder.color"), ";\n}\n\n.p-textarea.p-invalid::placeholder {\n    color: ").concat(dt("textarea.invalid.placeholder.color"), ";\n}\n\n.p-textarea-fluid {\n    width: 100%;\n}\n\n.p-textarea-resizable {\n    overflow: hidden;\n    resize: none;\n}\n\n.p-textarea-sm {\n    font-size: ").concat(dt("textarea.sm.font.size"), ";\n    padding-block: ").concat(dt("textarea.sm.padding.y"), ";\n    padding-inline: ").concat(dt("textarea.sm.padding.x"), ";\n}\n\n.p-textarea-lg {\n    font-size: ").concat(dt("textarea.lg.font.size"), ";\n    padding-block: ").concat(dt("textarea.lg.padding.y"), ";\n    padding-inline: ").concat(dt("textarea.lg.padding.x"), ";\n}\n");
};
var classes = {
  root: function root(_ref2) {
    var instance = _ref2.instance, props = _ref2.props;
    return ["p-textarea p-component", {
      "p-filled": instance.$filled,
      "p-textarea-resizable ": props.autoResize,
      "p-textarea-sm p-inputfield-sm": props.size === "small",
      "p-textarea-lg p-inputfield-lg": props.size === "large",
      "p-invalid": instance.$invalid,
      "p-variant-filled": instance.$variant === "filled",
      "p-textarea-fluid": instance.$fluid
    }];
  }
};
var TextareaStyle = BaseStyle.extend({
  name: "textarea",
  theme,
  classes
});

// node_modules/primevue/textarea/index.mjs
var script$1 = {
  name: "BaseTextarea",
  "extends": script,
  props: {
    autoResize: Boolean
  },
  style: TextareaStyle,
  provide: function provide() {
    return {
      $pcTextarea: this,
      $parentInstance: this
    };
  }
};
var script2 = {
  name: "Textarea",
  "extends": script$1,
  inheritAttrs: false,
  observer: null,
  mounted: function mounted() {
    var _this = this;
    if (this.autoResize) {
      this.observer = new ResizeObserver(function() {
        _this.resize();
      });
      this.observer.observe(this.$el);
    }
  },
  updated: function updated() {
    if (this.autoResize) {
      this.resize();
    }
  },
  beforeUnmount: function beforeUnmount() {
    if (this.observer) {
      this.observer.disconnect();
    }
  },
  methods: {
    resize: function resize() {
      if (!this.$el.offsetParent) return;
      this.$el.style.height = "auto";
      this.$el.style.height = this.$el.scrollHeight + "px";
      if (parseFloat(this.$el.style.height) >= parseFloat(this.$el.style.maxHeight)) {
        this.$el.style.overflowY = "scroll";
        this.$el.style.height = this.$el.style.maxHeight;
      } else {
        this.$el.style.overflow = "hidden";
      }
    },
    onInput: function onInput(event) {
      if (this.autoResize) {
        this.resize();
      }
      this.writeValue(event.target.value, event);
    }
  },
  computed: {
    attrs: function attrs() {
      return mergeProps(this.ptmi("root", {
        context: {
          filled: this.$filled,
          disabled: this.disabled
        }
      }), this.formField);
    }
  }
};
var _hoisted_1 = ["value", "disabled", "aria-invalid"];
function render(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("textarea", mergeProps({
    "class": _ctx.cx("root"),
    value: _ctx.d_value,
    disabled: _ctx.disabled,
    "aria-invalid": _ctx.invalid || void 0,
    onInput: _cache[0] || (_cache[0] = function() {
      return $options.onInput && $options.onInput.apply($options, arguments);
    })
  }, $options.attrs), null, 16, _hoisted_1);
}
script2.render = render;
export {
  script2 as default
};
//# sourceMappingURL=primevue_textarea.js.map
