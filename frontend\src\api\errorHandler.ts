import type { AxiosError } from 'axios';

/**
 * API错误类
 */
export class ApiError extends Error {
  status: number;
  code?: string;

  constructor(message: string, status: number, code?: string) {
    super(message);
    this.status = status;
    this.code = code;
    this.name = 'ApiError';
  }
}

/**
 * 处理API错误
 * @param error 错误对象
 * @throws {ApiError} 处理后的API错误
 */
export function handleApiError(error: any): never {
  if (error.isAxiosError) {
    const axiosError = error as AxiosError;
    if (axiosError.response) {
      // 服务器返回错误响应
      const status = axiosError.response.status;
      const message = (axiosError.response.data as any)?.detail || axiosError.message;

      throw new ApiError(message, status);
    } else if (axiosError.message === 'Network Error') {
      // 网络错误
      throw new ApiError('Network Error', 0, 'NETWORK_ERROR');
    } else if (axiosError.code === 'ECONNABORTED') {
      // 请求超时
      throw new ApiError('Request timeout', 408, 'TIMEOUT');
    }
  }
  // 未知错误
  throw new ApiError(error.message || 'Unknown error', 500);
}

/**
 * 格式化错误消息
 * @param error 错误对象
 * @returns 格式化后的错误消息
 */
export function formatErrorMessage(error: unknown): string {
  if (typeof error === 'string') {
    return error;
  }
  if (error instanceof Error) {
    return error.message;
  }
  return 'An unknown error occurred';
}
