import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const FONTS_DIR = path.join(__dirname, '../src/assets/fonts');
const HTML_FILE = path.join(__dirname, '../index.html');

// 运行 subfont
const subfont = spawn('npx', [
    'subfont',
    HTML_FILE,
    '--root', path.join(__dirname, '..'),
    '--formats', 'woff2',
    '--no-fallbacks',
    '--inline-css',
    '--inline-fonts',
    '--font-display', 'swap'
]);

subfont.stdout.on('data', (data) => {
    console.log(data.toString());
});

subfont.stderr.on('data', (data) => {
    console.error(data.toString());
});

subfont.on('close', (code) => {
    console.log(`Subfont process exited with code ${code}`);
});
