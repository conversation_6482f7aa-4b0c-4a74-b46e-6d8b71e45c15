param(
    [Parameter(Mandatory=$false)]
    [string]$InputString,
    
    [Parameter(Mandatory=$false)]
    [switch]$Help
)

function Show-Help {
    Write-Host @"
str_to_base64.2.ps1 - Convert string to base64 encoded string

Usage:
    .\str_to_base64.2.ps1 [-InputString <string>] [-Help]

Options:
    -InputString     The string to encode
    -Help           Show this help message

Examples:
    .\str_to_base64.2.ps1 -InputString "Hello World"
    .\str_to_base64.2.ps1 -Help
"@
    exit 0
}

if ($Help) {
    Show-Help
}

if (-not $InputString) {
    $InputString = Read-Host "Enter the string to encode"
}

$Bytes = [System.Text.Encoding]::UTF8.GetBytes($InputString)
$EncodedText = [Convert]::ToBase64String($Bytes)
Write-Output $EncodedText
