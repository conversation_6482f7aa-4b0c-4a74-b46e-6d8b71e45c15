/**
 * 随机图标生成器
 * 用于生成随机的图标和背景色，使卡片更美观
 */

// 可用的图标列表 - 混合使用 PrimeIcons 和 Font Awesome
const icons = [
  // PrimeIcons
  'pi pi-file-pdf',
  'pi pi-file-excel',
  'pi pi-file-word',
  'pi pi-file-code',
  'pi pi-book',
  'pi pi-database',
  'pi pi-server',
  'pi pi-cog',
  'pi pi-wrench',
  'pi pi-code',
  'pi pi-github',
  'pi pi-globe',
  'pi pi-cloud',
  'pi pi-desktop',
  'pi pi-mobile',
  'pi pi-chart-bar',
  'pi pi-chart-line',
  'pi pi-sitemap',
  'pi pi-palette',
  'pi pi-bolt',

  // Font Awesome - Solid
  'fas fa-atom',
  'fas fa-brain',
  'fas fa-code-branch',
  'fas fa-cubes',
  'fas fa-database',
  'fas fa-dragon',
  'fas fa-fire',
  'fas fa-flask',
  'fas fa-gamepad',
  'fas fa-graduation-cap',
  'fas fa-laptop-code',
  'fas fa-layer-group',
  'fas fa-microchip',
  'fas fa-network-wired',
  'fas fa-project-diagram',
  'fas fa-robot',
  'fas fa-rocket',
  'fas fa-satellite',
  'fas fa-server',
  'fas fa-sitemap',
  'fas fa-terminal',
  'fas fa-vr-cardboard',
  'fas fa-wand-magic-sparkles',

  // Font Awesome - Brands
  'fab fa-python',
  'fab fa-js',
  'fab fa-react',
  'fab fa-vuejs',
  'fab fa-angular',
  'fab fa-node-js',
  'fab fa-docker',
  'fab fa-aws',
  'fab fa-github',
  'fab fa-gitlab',
  'fab fa-unity',
  'fab fa-unreal-engine'
];

// 背景色列表 - 更丰富的色彩选择
const bgColors = [
  // 浅色系
  '#e3f2fd', // 浅蓝
  '#e8f5e9', // 浅绿
  '#fff8e1', // 浅黄
  '#fce4ec', // 浅粉
  '#f3e5f5', // 浅紫
  '#e0f7fa', // 浅青
  '#fff3e0', // 浅橙
  '#efebe9', // 浅棕
  '#f1f8e9', // 浅淡绿
  '#e8eaf6', // 浅靛蓝
  '#ffebee', // 浅红
  '#e0f2f1', // 浅蓝绿

  // 柔和色系
  '#f0f4f8', // 柔和蓝灰
  '#f8f9fa', // 柔和灰白
  '#f6f9fc', // 柔和淡蓝
  '#f9f7f7', // 柔和灰白
  '#f7f7f7', // 柔和淡灰
  '#f8f8f8', // 柔和淡灰

  // 现代感色系
  '#eceff1', // 现代淡蓝灰
  '#f5f7fa', // 现代淡蓝白
  '#eef2f7', // 现代淡蓝
  '#f0f5f9', // 现代淡蓝白
  '#f7fafc', // 现代极淡蓝
  '#f9fafb', // 现代极淡灰

  // 科技感色系
  '#f0f7ff', // 科技淡蓝
  '#f5fcff', // 科技淡青
  '#f2f8ff', // 科技淡蓝
  '#f0faff', // 科技淡青蓝
  '#f5f8ff', // 科技淡紫蓝
  '#f8f9ff'  // 科技极淡紫
];

// 文本颜色列表 - 更丰富的色彩选择
const textColors = [
  // 基础色系
  '#1565c0', // 蓝色
  '#2e7d32', // 绿色
  '#f57f17', // 黄色
  '#c2185b', // 粉色
  '#7b1fa2', // 紫色
  '#00838f', // 青色
  '#ef6c00', // 橙色
  '#4e342e', // 棕色
  '#558b2f', // 浅绿色
  '#303f9f', // 靛蓝色
  '#c62828', // 红色
  '#00695c', // 蓝绿色

  // 现代色系
  '#3949ab', // 现代靛蓝
  '#00acc1', // 现代青蓝
  '#43a047', // 现代绿
  '#039be5', // 现代蓝
  '#8e24aa', // 现代紫
  '#d81b60', // 现代粉
  '#fb8c00', // 现代橙
  '#e53935', // 现代红

  // 科技色系
  '#0d47a1', // 科技深蓝
  '#006064', // 科技深青
  '#1a237e', // 科技深靛蓝
  '#01579b', // 科技深浅蓝
  '#004d40', // 科技深绿
  '#311b92', // 科技深紫
  '#880e4f', // 科技深粉
  '#b71c1c', // 科技深红
];

/**
 * 根据字符串生成一个确定的随机数
 * @param str 输入字符串
 * @returns 0-1之间的随机数
 */
function hashString(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = ((hash << 5) - hash) + str.charCodeAt(i);
    hash |= 0; // Convert to 32bit integer
  }
  // 将hash转换为0-1之间的数
  return Math.abs(hash) / 2147483647;
}

/**
 * 根据输入字符串生成一个确定的图标
 * @param input 输入字符串（如URL或标题）
 * @returns 图标类名
 */
export function generateIcon(input: string): string {
  const index = Math.floor(hashString(input) * icons.length);
  return icons[index];
}

/**
 * 根据输入字符串生成一个确定的背景色
 * @param input 输入字符串（如URL或标题）
 * @returns 背景色
 */
export function generateBgColor(input: string): string {
  const index = Math.floor(hashString(input) * bgColors.length);
  return bgColors[index];
}

/**
 * 根据输入字符串生成一个确定的文本颜色
 * @param input 输入字符串（如URL或标题）
 * @returns 文本颜色
 */
export function generateTextColor(input: string): string {
  const index = Math.floor(hashString(input) * textColors.length);
  return textColors[index];
}

/**
 * 根据输入字符串生成一个图标配置对象
 * @param input 输入字符串（如URL或标题）
 * @returns 图标配置对象
 */
export function generateIconConfig(input: string): { icon: string, bgColor: string, textColor: string } {
  return {
    icon: generateIcon(input),
    bgColor: generateBgColor(input),
    textColor: generateTextColor(input)
  };
}

export default {
  generateIcon,
  generateBgColor,
  generateTextColor,
  generateIconConfig
};
