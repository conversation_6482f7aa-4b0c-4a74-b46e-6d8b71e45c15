/**
 * 图标缓存服务
 * 用于缓存文档的图标配置，避免每次刷新都生成不同的图标
 */

import { generateIconConfig } from '../utils/iconGenerator';

// 图标配置缓存
interface IconConfig {
  icon: string;
  bgColor: string;
  textColor: string;
}

// 缓存对象
const iconCache: Record<string, IconConfig> = {};

// 本地存储键名
const STORAGE_KEY = 'api_docs_icon_cache';

/**
 * 从本地存储加载缓存
 */
const loadCacheFromStorage = (): void => {
  try {
    const cachedData = localStorage.getItem(STORAGE_KEY);
    if (cachedData) {
      const parsedData = JSON.parse(cachedData);
      Object.assign(iconCache, parsedData);
    }
  } catch (error) {
    console.error('Failed to load icon cache from storage:', error);
  }
};

/**
 * 将缓存保存到本地存储
 */
const saveCacheToStorage = (): void => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(iconCache));
  } catch (error) {
    console.error('Failed to save icon cache to storage:', error);
  }
};

/**
 * 获取文档的图标配置
 * 如果缓存中存在，则返回缓存的配置
 * 否则生成新的配置并缓存
 * 
 * @param docId 文档ID
 * @param seed 用于生成图标的种子字符串（通常是文档标题+URL）
 * @returns 图标配置
 */
export const getIconConfig = (docId: string, seed: string): IconConfig => {
  // 如果缓存中不存在，则生成新的配置并缓存
  if (!iconCache[docId]) {
    iconCache[docId] = generateIconConfig(seed);
    // 异步保存到本地存储
    setTimeout(saveCacheToStorage, 0);
  }
  
  return iconCache[docId];
};

/**
 * 清除缓存
 */
export const clearIconCache = (): void => {
  Object.keys(iconCache).forEach(key => delete iconCache[key]);
  localStorage.removeItem(STORAGE_KEY);
};

/**
 * 初始化缓存服务
 */
export const initIconCacheService = (): void => {
  loadCacheFromStorage();
};

// 导出服务
export default {
  getIconConfig,
  clearIconCache,
  initIconCacheService
};
