import { fileURLToPath } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  base: '/',  // 使用绝对路径
  plugins: [
    vue({
      template: {
        compilerOptions: {
          // 这里可以配置模板编译选项
        }
      }
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      'primeflex': 'primeflex/primeflex.css',
      'primeicons': 'primeicons/primeicons.css'
    }
  },
  server: {
    host: process.env.VITE_FRONTEND_HOST || 'localhost',
    port: parseInt(process.env.VITE_FRONTEND_PORT || '3003'),
    strictPort: true,
    cors: true,
    proxy: {
      '/backend': {
        target: 'http://localhost:8001',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/backend/, '')  // 只移除 /backend 前缀
      }
    },
    fs: {
      strict: false,
      allow: ['..']
    }
  },
  preview: {
    port: 3000,
    strictPort: true,
    cors: true
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          'ui-vendor': ['primevue', 'primeicons', 'primeflex']
        },
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
      }
    }
  },
  optimizeDeps: {
    include: [],
    exclude: []
  },
  chunkSizeWarningLimit: 2000,
  define: {
    'process.env': {}
  },
  publicDir: 'public'
})
