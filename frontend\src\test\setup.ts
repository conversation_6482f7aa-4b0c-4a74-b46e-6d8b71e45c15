import { config } from '@vue/test-utils'
import '@testing-library/jest-dom'
import { beforeAll, afterEach, afterAll, vi } from 'vitest'
import axios from 'axios'
import MockAdapter from 'axios-mock-adapter'

// 创建 axios mock 适配器
export const mockAxios = new MockAdapter(axios)

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Vue Test Utils 配置
config.global.stubs = {
  transition: false,
  'transition-group': false
}

// 模拟控制台错误，防止测试输出太多错误信息
const originalConsoleError = console.error
console.error = (...args: any[]) => {
  if (
    typeof args[0] === 'string' &&
    args[0].includes('Vue received a Component which was made a reactive object')
  ) {
    return
  }
  originalConsoleError(...args)
}

// 设置全局 mock
beforeAll(() => {
  // 模拟环境变量
  vi.stubGlobal('import.meta', {
    env: {
      VITE_API_BASE_URL: 'http://localhost:8000',
      VITE_ENV: 'test',
      DEV: true,
      PROD: false
    }
  })
})

// 清理 mock
afterEach(() => {
  mockAxios.reset()
})

// 清理全局 mock
afterAll(() => {
  mockAxios.restore()
  vi.unstubAllGlobals()
})
