import { ref } from 'vue'
import type { HealthStatus } from '@/config/health'
import { HEALTH_CONFIG } from '@/config/health'
import api from '@/api'

export function useHealthCheck(options = HEALTH_CONFIG) {
  const status = ref<HealthStatus | null>(null)
  const error = ref<Error | null>(null)
  const isChecking = ref(false)
  let timer: number | null = null

  const checkHealth = async () => {
    if (isChecking.value) return

    isChecking.value = true
    error.value = null

    try {
      const response = await api.get<HealthStatus>(options.endpoint, {
        timeout: options.timeout
      })
      status.value = response.data
    } catch (err) {
      error.value = err as Error
      status.value = {
        status: 'unhealthy',
        timestamp: new Date().toISOString()
      }
    } finally {
      isChecking.value = false
    }
  }

  const startPolling = () => {
    if (timer !== null) return
    checkHealth()
    timer = window.setInterval(checkHealth, options.interval)
  }

  const stopPolling = () => {
    if (timer !== null) {
      clearInterval(timer)
      timer = null
    }
  }

  return {
    status,
    error,
    isChecking,
    checkHealth,
    startPolling,
    stopPolling
  }
}
