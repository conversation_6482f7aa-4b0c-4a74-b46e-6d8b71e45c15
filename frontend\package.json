{"name": "frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"predev": "node scripts/convert-font.js", "dev": "vite", "build": "vite build", "build:check": "vue-tsc && vite build", "preview": "vite preview", "test": "vitest", "test:coverage": "vitest run --coverage", "test:e2e": "cypress run", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "convert-font": "node scripts/convert-font.js", "optimize-font": "node scripts/optimize-font.js", "prebuild": "node scripts/prebuild.mjs", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@primevue/themes": "^4.2.5", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "axios": "^1.7.9", "core-js": "^3.40.0", "pinia": "^2.1.7", "primeflex": "^3.3.1", "primeicons": "^7.0.0", "primevue": "^4.0.0", "vue": "^3.5.13", "vue-i18n": "^9.14.2", "vue-router": "^4.5.0"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/eslint-parser": "^7.26.5", "@rushstack/eslint-patch": "^1.3.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/vue": "^8.1.0", "@tsconfig/node18": "^18.2.2", "@types/lodash": "^4.17.14", "@types/node": "^20.10.5", "@vitejs/plugin-vue": "^4.6.2", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vitest/coverage-v8": "^1.6.0", "@vitest/ui": "^1.6.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.3", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.20", "browserify-zlib": "^0.2.0", "chalk": "^5.4.1", "cross-env": "^7.0.3", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "fontmin": "^0.9.9", "https-browserify": "^1.0.0", "jsdom": "^24.1.3", "npm-run-all2": "^6.1.1", "ora": "7.0.1", "postcss": "^8.5.1", "prettier": "^3.0.3", "sass": "^1.83.4", "sass-loader": "^13.3.3", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "subfont": "^7.1.0", "tailwindcss": "^3.4.17", "ttf2woff2": "^5.0.0", "typescript": "~5.3.3", "vite": "^5.0.10", "vitest": "^1.6.0", "vue-tsc": "^1.8.25"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}