# 停止可能正在运行的 Caddy 进程
Write-Host "Stopping any running Caddy processes..."
$caddyProcesses = Get-Process -Name "caddy" -ErrorAction SilentlyContinue
if ($caddyProcesses) {
    $caddyProcesses | Stop-Process -Force
    Start-Sleep -Seconds 2
}

# 构建前端
Write-Host "Building frontend..."
npm run build

# 启动 Caddy 服务器
Write-Host "Starting Caddy server..."
caddy run --config Caddyfile.dev

# 设置 Ctrl+C 处理
try {
    while ($true) {
        Start-Sleep -Seconds 1
    }
} finally {
    Write-Host "`nStopping Caddy server..."
    Get-Process -Name "caddy" -ErrorAction SilentlyContinue | Stop-Process -Force
}