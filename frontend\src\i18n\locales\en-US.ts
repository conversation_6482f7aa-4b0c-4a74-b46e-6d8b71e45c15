export default {
  app: {
    title: 'API Docs',
    footer: {
      powered: 'Powered by Lightbox'
    }
  },
  common: {
    actions: {
      create: 'Create',
      edit: 'Edit',
      delete: 'Delete',
      refresh: 'Refresh',
      save: 'Save',
      cancel: 'Cancel',
      confirm: 'Confirm',
      search: 'Search'
    },
    messages: {
      confirmDelete: 'Are you sure you want to delete?',
      saveSuccess: 'Saved successfully',
      saveFailed: 'Failed to save',
      deleteSuccess: 'Deleted successfully',
      deleteFailed: 'Failed to delete'
    }
  },
  apiDoc: {
    title: 'Title',
    url: 'URL',
    description: 'Description',
    createdAt: 'Created At',
    viewCount: 'Views',
    actions: {
      openInNewTab: 'Open in New Tab',
      refreshMetadata: 'Refresh Metadata'
    },
    empty: {
      title: 'No Documents',
      description: 'Click the create button in the top right to add a new document'
    }
  },
  hero: {
    description: 'Manage and explore your API documentation. Create, organize, and share your APIs.',
    newDoc: 'New Document'
  },
  language: {
    switch: 'Switch Language'
  },
  search: {
    placeholder: 'Search docs... ({total} documents)',
    clear: 'Clear Search',
    noResults: {
      title: 'No Results Found',
      description: 'No documents found matching "{query}"'
    }
  },
  auth: {
    welcome: 'Welcome',
    loginDescription: 'Please login with your Taihu account',
    loginWithTaihu: 'Login with Taihu',
    loginHint: 'Click the button above to redirect to Taihu login page',
    processing: 'Processing login...',
    processingDescription: 'Please wait while we verify your identity',
    error: 'Login Failed',
    backToLogin: 'Back to Login'
  }
}
