
from pydantic import BaseModel
from typing import Optional, Dict, Any

class Profile(BaseModel):
    """Model representing user profile information."""
    sub: str  # Unique identifier for the user
    name: Optional[str] = None
    email: Optional[str] = None
    picture: Optional[str] = None
    
    @classmethod
    def from_taihu_userinfo(cls, userinfo: Dict[str, Any]) -> "Profile":
        """Create a profile from Taihu production environment user info."""
        return cls(
            sub=userinfo.get("sub", "unknown"),
            name=userinfo.get("name"),
            email=userinfo.get("email"),
            picture=userinfo.get("picture")
        )