# 设置环境变量
$env:ENV = "development"

Write-Host "Starting backend server in development mode..."

# 检查 Python 版本
$pythonVersion = (python --version 2>&1) | Out-String
if (-not ($pythonVersion -match "Python 3\.10\.*")) {
    Write-Host "Error: Python 3.10.x is required. Current version: $pythonVersion"
    Write-Host "Please install Python 3.10 and try again."
    exit 1
}

# 检查虚拟环境是否存在和激活
if (-not (Test-Path ".venv")) {
    Write-Host "Virtual environment not found. Please run init-dev-env.ps1 first."
    exit 1
}

# 激活虚拟环境
Write-Host "Activating virtual environment..."
.\.venv\Scripts\Activate.ps1

# 启动后端服务
Write-Host "Starting FastAPI server..."
uvicorn api_docs_server.main:app --reload --host 0.0.0.0 --port 8000
