import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import ApiDocPreview from '../ApiDocPreview.vue'
import { Dialog } from '../../test/mocks/components'

describe('ApiDocPreview', () => {
  const mountComponent = (props = {}) => {
    return mount(ApiDocPreview, {
      props: {
        modelValue: true,
        ...props
      },
      global: {
        components: {
          Dialog
        },
        stubs: {
          Dialog: {
            template: '<div><slot></slot><slot name="header"></slot></div>'
          }
        }
      }
    })
  }

  it('renders properly', () => {
    const wrapper = mountComponent()
    expect(wrapper.exists()).toBe(true)
  })

  it('shows loading state', () => {
    const wrapper = mountComponent({
      loading: true
    })

    const loading = wrapper.find('.loading-spinner')
    expect(loading.exists()).toBe(true)
  })

  it('shows placeholder when no doc', () => {
    const wrapper = mountComponent()

    const placeholder = wrapper.find('.preview-placeholder')
    expect(placeholder.exists()).toBe(true)
    expect(placeholder.text()).toContain('无效的文档地址')
  })

  it('shows preview content when doc has url', () => {
    const url = 'https://example.com'
    const wrapper = mountComponent({
      doc: {
        id: '1',
        title: 'Test Doc',
        url,
        type: 'swagger'
      }
    })

    const content = wrapper.find('.preview-content')
    expect(content.exists()).toBe(true)
    expect(content.text()).toContain('在新标签页中打开')
  })

  it('emits update:modelValue event when dialog is closed', async () => {
    const wrapper = mountComponent()
    await wrapper.vm.$emit('update:modelValue', false)

    expect(wrapper.emitted('update:modelValue')).toBeTruthy()
    expect(wrapper.emitted('update:modelValue')![0]).toEqual([false])
  })
})
