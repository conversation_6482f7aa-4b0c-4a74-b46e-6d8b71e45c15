<template>
  <div class="api-doc-list">
    <ApiDocHero
      :total-docs="docs.length"
      :is-error="isLoading"
      :search-query="searchQuery"
      @search="handleSearch"
      @new="openNewDialog"
      ref="heroComponent"
    />

    <!-- 文档列表 -->
    <div v-if="filteredDocs.length > 0" class="grid">
      <div
        v-for="doc in filteredDocs"
        :key="doc.id"
        class="col-12 md:col-6 lg:col-4 xl:col-3"
      >
        <ApiDocCard
          :doc="doc"
          @edit="openEditDialog(doc)"
          @delete="handleDelete(doc)"
          @refresh="refreshMetadata(doc)"
          @view="handleDocView(doc)"
        />
      </div>
    </div>

    <!-- 空状态提示 -->
    <div v-else class="empty-state">
      <i class="pi pi-search empty-icon"></i>
      <h3>{{ searchQuery ? '未找到匹配的文档' : '暂无文档' }}</h3>
      <p>
        <template v-if="searchQuery">
          没有找到与"{{ searchQuery }}"相关的文档
          <br>
          <Button
            class="p-button-text mt-3"
            label="清除搜索"
            icon="pi pi-times"
            @click="clearSearch"
          />
        </template>
        <template v-else>
          点击右上角的"新建文档"按钮创建第一个文档
        </template>
      </p>
    </div>

    <!-- 编辑对话框 -->
    <EditDocDialog
      :visible="showEditDialog"
      :doc="selectedDoc"
      @update:visible="showEditDialog = $event"
      @save="handleEditSave"
    />

    <!-- 新建文档对话框 -->
    <NewDocDialog
      v-model:visible="showNewDialog"
      @save="handleNewSave"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useToast } from 'primevue/usetoast'
import type { ApiDoc } from '@/types'
import { ApiDocService } from '@/api/apiService'
import { useApiDocs } from '@/composables/useApiDocs'
import Button from 'primevue/button'
import ApiDocHero from '@/components/ApiDocHero.vue'
import ApiDocCard from '@/components/ApiDocCard.vue'
import EditDocDialog from '@/components/EditDocDialog.vue'
import NewDocDialog from '@/components/NewDocDialog.vue'

// 状态管理
const showEditDialog = ref(false)
const showNewDialog = ref(false)
const selectedDoc = ref<ApiDoc | null>(null)
const searchQuery = ref('')
const heroComponent = ref<InstanceType<typeof ApiDocHero>>()

// 工具函数
const toast = useToast()

// API文档状态管理
const { 
  docs, 
  isLoading,
  searchDocs,
  smartRefresh,
  optimisticAdd,
  optimisticUpdate,
  optimisticDelete,
  incrementViewCount
} = useApiDocs()

// 计算属性：过滤后的文档列表
const filteredDocs = computed(() => {
  return searchQuery.value ? searchDocs(searchQuery.value) : docs.value
})

// 处理文档访问
const handleDocView = (doc: ApiDoc) => {
  incrementViewCount(doc.id)
}

// 搜索处理
const handleSearch = (query: string) => {
  searchQuery.value = query
}

// 清除搜索
const clearSearch = () => {
  searchQuery.value = ''
  if (heroComponent.value) {
    heroComponent.value.clearSearch()
  }
}

// 生命周期钩子
onMounted(async () => {
  await smartRefresh()
})

const handleRefresh = () => {
  smartRefresh(true)
}

const openNewDialog = () => {
  showNewDialog.value = true
}

const handleNewSave = async (doc: ApiDoc) => {
  try {
    const savedDoc = await ApiDocService.createDoc({
      url: doc.url,
      title: doc.title,
      description: doc.description,
      auto_metadata: doc.auto_metadata
    })
    optimisticAdd(savedDoc)
    showNewDialog.value = false
    toast.add({
      severity: 'success',
      summary: '成功',
      detail: '文档创建成功',
      life: 3000
    })
  } catch (error) {
    console.error('Error creating doc:', error)
    toast.add({
      severity: 'error',
      summary: '错误',
      detail: '文档创建失败',
      life: 3000
    })
  }
}

const handleEditSave = async (doc: ApiDoc) => {
  try {
    const updatedDoc = await ApiDocService.updateDoc(doc.id, {
      url: doc.url,
      title: doc.title,
      description: doc.description,
      auto_metadata: doc.auto_metadata
    })
    optimisticUpdate(updatedDoc)
    showEditDialog.value = false
    toast.add({
      severity: 'success',
      summary: '成功',
      detail: '文档更新成功',
      life: 3000
    })
  } catch (error: any) {
    console.error('Error updating doc:', error)
    const errorMessage = error.response?.data?.message || error.message || '未知错误'
    toast.add({
      severity: 'error',
      summary: '文档更新失败',
      detail: `更新失败：${errorMessage}`,
      life: 5000,
      sticky: errorMessage.length > 50  // 如果错误信息较长，保持提示框显示
    })
  }
}

const handleDelete = async (doc: ApiDoc) => {
  try {
    await ApiDocService.deleteDoc(doc.id)
    optimisticDelete(doc.id)
    toast.add({
      severity: 'success',
      summary: '成功',
      detail: '文档删除成功',
      life: 3000
    })
  } catch (error) {
    console.error('Error deleting doc:', error)
    toast.add({
      severity: 'error',
      summary: '错误',
      detail: '文档删除失败',
      life: 3000
    })
  }
}

const openEditDialog = (doc: ApiDoc) => {
  selectedDoc.value = doc
  showEditDialog.value = true
}

const refreshMetadata = async (doc: ApiDoc) => {
  try {
    const updatedDoc = await ApiDocService.refreshMetadata(doc.id)
    optimisticUpdate(updatedDoc)
    toast.add({
      severity: 'success',
      summary: '成功',
      detail: '元数据更新成功',
      life: 3000
    })
  } catch (error) {
    console.error('Error refreshing metadata:', error)
    toast.add({
      severity: 'error',
      summary: '错误',
      detail: '元数据更新失败',
      life: 3000
    })
  }
}
</script>

<style scoped>
.api-doc-list {
  padding: 1.5rem;
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
}

.empty-icon {
  font-size: 3rem;
  color: var(--text-color-secondary);
  margin-bottom: 1rem;
}

.empty-state h3 {
  color: var(--text-color);
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

.empty-state p {
  color: var(--text-color-secondary);
  margin: 0;
}
</style>
