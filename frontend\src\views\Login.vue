<template>
  <div class="login-container">
    <h1>Login with <PERSON><PERSON></h1>
    <button @click="loginWithTaihu" class="login-button">Login</button>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { useRouter } from 'vue-router';

export default defineComponent({
  name: 'Login',
  setup() {
    const router = useRouter();

    const loginWithTaihu = async () => {
      try {
        // 获取当前主机和端口
        const host = window.location.hostname;
        const port = window.location.port ? `:${window.location.port}` : '';
        const protocol = window.location.protocol;

        // 构建重定向URI
        const redirectUri = `${protocol}//${host}${port}/api/auth/callback`;
        console.log(`Using redirect URI: ${redirectUri}`);

        // 跳转到太湖 OAuth2.0 登录页面
        window.location.href = `https://test-odc.it.woa.com/api/auth-center/oauth2/authorize?response_type=code&client_id=test-app&redirect_uri=${encodeURIComponent(redirectUri)}&scope=openid`;
      } catch (error) {
        console.error('Login failed:', error);
        alert('登录失败，请稍后重试');
      }
    };

    return { loginWithTaihu };
  },
});
</script>

<style scoped>
.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.login-button {
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
}
</style>