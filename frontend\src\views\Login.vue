<template>
  <div class="login-container">
    <div class="flex flex-column align-items-center justify-content-center min-h-screen">
      <div class="card p-6 shadow-2 border-round w-full max-w-md">
        <div class="text-center mb-6">
          <img src="@/assets/logo.svg" alt="Logo" class="h-3rem mb-3" />
          <h1 class="text-2xl font-bold text-900 mb-2">API文档管理系统</h1>
          <p class="text-600 m-0">请选择登录环境</p>
        </div>
        
        <div class="flex flex-column gap-3">
          <Button
            @click="loginWithTaihu('development')"
            :loading="loading"
            label="太湖测试环境登录"
            icon="pi pi-sign-in"
            class="w-full p-3 text-lg"
            severity="primary"
          />
          
          <Button
            @click="loginWithTaihu('production')"
            :loading="loading"
            label="太湖生产环境登录"
            icon="pi pi-sign-in"
            class="w-full p-3 text-lg"
            severity="success"
          />

          <div class="text-center">
            <small class="text-500">
              选择对应的环境进行登录
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { useToast } from 'primevue/usetoast'
import Button from 'primevue/button'

const route = useRoute()
const toast = useToast()
const loading = ref(false)

const loginWithTaihu = async (env: 'development' | 'production') => {
  try {
    loading.value = true

    // 获取重定向目标
    const redirect = route.query.redirect as string || '/'
    
    // 根据环境选择不同的登录端点
    const loginEndpoint = env === 'production' ? '/backend/auth/taihu-production' : '/backend/auth/taihu'
    
    console.log(`Redirecting to Taihu ${env} environment: ${loginEndpoint}`)

    // 直接跳转到后端登录端点
    window.location.href = loginEndpoint
  } catch (error) {
    console.error('Login failed:', error)
    toast.add({
      severity: 'error',
      summary: '登录失败',
      detail: '跳转到登录页面失败，请稍后重试',
      life: 5000
    })
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}
</style>
