<template>
  <div class="login-container">
    <div class="flex flex-column align-items-center justify-content-center min-h-screen">
      <div class="card p-6 shadow-2 border-round w-full max-w-md">
        <div class="text-center mb-6">
          <img src="@/assets/logo.svg" alt="Logo" class="h-3rem mb-3" />
          <h1 class="text-2xl font-bold text-900 mb-2">{{ $t('auth.welcome') }}</h1>
          <p class="text-600 m-0">{{ $t('auth.loginDescription') }}</p>
        </div>

        <div class="flex flex-column gap-3">
          <Button
            @click="loginWithTaihu"
            :loading="loading"
            :label="$t('auth.loginWithTaihu')"
            icon="pi pi-sign-in"
            class="w-full p-3 text-lg"
            severity="primary"
          />

          <div class="text-center">
            <small class="text-500">
              {{ $t('auth.loginHint') }}
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { useToast } from 'primevue/usetoast'
import Button from 'primevue/button'

const route = useRoute()
const toast = useToast()
const loading = ref(false)

const loginWithTaihu = async () => {
  try {
    loading.value = true

    // 获取重定向目标
    const redirect = route.query.redirect as string || '/'

    // 跳转到太湖 OAuth2.0 登录页面 - 使用太湖配置的回调地址
    const authUrl = new URL('https://tai.it.tencent.com/api/auth-center/oauth2/authorize')
    authUrl.searchParams.set('client_id', 'api-docs')
    authUrl.searchParams.set('response_type', 'code')
    authUrl.searchParams.set('scope', 'openid offline')
    authUrl.searchParams.set('state', redirect) // 传递重定向目标
    authUrl.searchParams.set('redirect_uri', 'https://api-docs.woa.com/taihu_callback')

    console.log(`Redirecting to Taihu OAuth: ${authUrl.toString()}`)

    window.location.href = authUrl.toString()
  } catch (error) {
    console.error('Login failed:', error)
    toast.add({
      severity: 'error',
      summary: '登录失败',
      detail: '跳转到登录页面失败，请稍后重试',
      life: 5000
    })
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}
</style>