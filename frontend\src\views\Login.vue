<template>
  <div>
    <h1>登录</h1>
    <button @click="redirectToAuth">使用太湖登录</button>
  </div>
</template>

<script setup>
const redirectToAuth = () => {
  const authUrl = `https://tai.it.tencent.com/api/auth-center/oauth2/authorize?client_id=api-docs&response_type=code&scope=openid+offline&state=abcdefg123456&redirect_uri=https://api-docs.woa.com/taihu_callback`;
  window.location.href = authUrl;
};
</script>