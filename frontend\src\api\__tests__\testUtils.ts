import type { ApiDoc } from '../../types/api';

export const createMockDoc = (overrides: Partial<ApiDoc> = {}): ApiDoc => ({
  id: '1',
  url: 'https://api.example.com',
  title: 'Example API',
  description: 'Example description',
  created_at: '2025-01-23T12:00:00Z',
  updated_at: '2025-01-23T12:00:00Z',
  ...overrides,
});

export const createMockError = (status: number, message: string) => ({
  response: {
    status,
    data: {
      detail: message,
    },
  },
});
