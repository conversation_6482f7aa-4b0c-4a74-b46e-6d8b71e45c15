"""change id to uuid

Revision ID: 20240124_change_id_to_uuid
Revises: 79499a4b96f3
Create Date: 2024-01-24 00:49:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import uuid

# revision identifiers, used by Alembic.
revision: str = '20240124_change_id_to_uuid'
down_revision: Union[str, None] = '79499a4b96f3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 创建一个临时的 UUID 列
    op.add_column('api_docs', sa.Column('new_id', postgresql.UUID(as_uuid=True), nullable=True))
    
    # 获取连接
    connection = op.get_bind()
    
    # 获取所有现有记录的 ID
    result = connection.execute(sa.text("SELECT id FROM api_docs"))
    rows = result.fetchall()
    
    # 为每个记录生成新的 UUID
    for row in rows:
        new_uuid = str(uuid.uuid4())
        connection.execute(
            sa.text("UPDATE api_docs SET new_id = :uuid WHERE id = :old_id"),
            parameters={"uuid": new_uuid, "old_id": row[0]}
        )
    
    # 删除旧的主键约束
    op.drop_constraint('api_docs_pkey', 'api_docs', type_='primary')
    
    # 删除旧的 id 列
    op.drop_column('api_docs', 'id')
    
    # 重命名新列为 id
    op.alter_column('api_docs', 'new_id', new_column_name='id')
    
    # 设置新列为非空
    op.alter_column('api_docs', 'id', nullable=False)
    
    # 添加新的主键约束
    op.create_primary_key('api_docs_pkey', 'api_docs', ['id'])


def downgrade() -> None:
    # 创建一个临时的整数 id 列
    op.add_column('api_docs', sa.Column('new_id', sa.Integer(), nullable=True))
    
    # 获取连接
    connection = op.get_bind()
    
    # 获取所有现有记录
    result = connection.execute(sa.text("SELECT id FROM api_docs"))
    rows = result.fetchall()
    
    # 为每个记录生成新的整数 ID
    for i, row in enumerate(rows, 1):
        connection.execute(
            sa.text("UPDATE api_docs SET new_id = :new_id WHERE id = :old_id"),
            parameters={"new_id": i, "old_id": row[0]}
        )
    
    # 删除旧的主键约束
    op.drop_constraint('api_docs_pkey', 'api_docs', type_='primary')
    
    # 删除旧的 UUID 列
    op.drop_column('api_docs', 'id')
    
    # 重命名新列为 id
    op.alter_column('api_docs', 'new_id', new_column_name='id')
    
    # 设置新列为非空
    op.alter_column('api_docs', 'id', nullable=False)
    
    # 添加新的主键约束
    op.create_primary_key('api_docs_pkey', 'api_docs', ['id'])
    
    # 创建索引
    op.create_index(op.f('ix_api_docs_id'), 'api_docs', ['id'], unique=False)
