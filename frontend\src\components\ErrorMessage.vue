<template>
  <div class="error-message">
    <div class="error-content">
      <i class="pi pi-exclamation-circle error-icon"></i>
      <div class="error-details">
        <h3 class="error-title">{{ title || '出错了' }}</h3>
        <p class="error-description">{{ message }}</p>
        <div v-if="$slots.actions" class="error-actions">
          <slot name="actions"></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title?: string;
  message: string;
}

const props = defineProps<Props>()
</script>

<style scoped>
.error-message {
  padding: 1rem;
  border-radius: 12px;
  background: var(--red-50);
  border: 1px solid var(--red-200);
  animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

.error-content {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.error-icon {
  flex-shrink: 0;
  font-size: 1.5rem;
  color: var(--red-500);
  margin-top: 0.125rem;
}

.error-details {
  flex: 1;
  min-width: 0;
}

.error-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--red-700);
  margin: 0 0 0.25rem;
}

.error-description {
  font-size: 0.875rem;
  color: var(--red-600);
  margin: 0;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

@keyframes shake {
  10%, 90% {
    transform: translateX(-1px);
  }
  20%, 80% {
    transform: translateX(2px);
  }
  30%, 50%, 70% {
    transform: translateX(-4px);
  }
  40%, 60% {
    transform: translateX(4px);
  }
}

/* 暗色主题适配 */
:root[data-theme-mode="dark"] {
  & .error-message {
    background: rgba(var(--red-500-rgb), 0.1);
    border-color: rgba(var(--red-500-rgb), 0.2);
  }

  & .error-title {
    color: var(--red-400);
  }

  & .error-description {
    color: var(--red-300);
  }
}
</style>
