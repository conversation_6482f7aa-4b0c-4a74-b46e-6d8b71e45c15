import { vi } from 'vitest'

const mockInterceptors = {
  request: { use: vi.fn() },
  response: { use: vi.fn() }
}

const mockAxiosInstance = {
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
  interceptors: mockInterceptors,
  defaults: {},
  getUri: vi.fn(),
  request: vi.fn(),
  head: vi.fn(),
  options: vi.fn(),
  patch: vi.fn()
}

const mockAxios = {
  create: vi.fn(() => mockAxiosInstance),
  get: mockAxiosInstance.get,
  post: mockAxiosInstance.post,
  put: mockAxiosInstance.put,
  delete: mockAxiosInstance.delete,
  interceptors: mockInterceptors,
  defaults: {},
  getUri: vi.fn(),
  request: vi.fn(),
  head: vi.fn(),
  options: vi.fn(),
  patch: vi.fn(),
  isAxiosError: vi.fn((error: any) => error?.isAxiosError ?? false)
}

export default mockAxios
export { mockAxiosInstance }
