<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎉 Node.js兼容的太湖登录功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 50px auto;
            padding: 20px;
            line-height: 1.6;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .button {
            display: inline-block;
            padding: 15px 30px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 10px 5px;
            border: none;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .button:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        .button.github {
            background: #333;
        }
        .button.github:hover {
            background: #24292e;
        }
        .button.success {
            background: #28a745;
        }
        .button.success:hover {
            background: #1e7e34;
        }
        .button.warning {
            background: #ffc107;
            color: #212529;
        }
        .button.warning:hover {
            background: #e0a800;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        .status.running {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .config {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-line;
            border-left: 4px solid #007bff;
        }
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }
        .nodejs {
            border-left: 4px solid #68a063;
        }
        .python {
            border-left: 4px solid #3776ab;
        }
    </style>
</head>
<body>
    <h1>🎉 Node.js兼容的太湖登录功能</h1>
    
    <div class="container">
        <h2>📊 实现状态</h2>
        <div class="status running">
            ✅ 后端服务器: http://localhost:8001 (运行中)
        </div>
        <div class="status running">
            ✅ 按照Node.js代码参数完全实现
        </div>
        <div class="status running">
            ✅ 支持GitHub + 太湖双环境登录
        </div>
    </div>

    <div class="container">
        <h2>🔐 功能测试</h2>
        
        <h3>主要页面</h3>
        <a href="http://localhost:8001" class="button" target="_blank">
            🏠 首页 (类似Node.js根路由)
        </a>
        <a href="http://localhost:8001/auth/login" class="button" target="_blank">
            🔐 登录页面
        </a>
        <a href="http://localhost:8001/auth/profile" class="button warning" target="_blank">
            👤 Profile页面 (类似Node.js profile路由)
        </a>

        <h3>GitHub登录 (完全兼容Node.js参数)</h3>
        <a href="http://localhost:8001/auth/github" class="button github" target="_blank">
            🐙 GitHub登录
        </a>

        <h3>太湖登录 (完全兼容Node.js参数)</h3>
        <a href="http://localhost:8001/auth/taihu" class="button" target="_blank">
            🧪 腾讯太湖测试环境登录
        </a>
        <a href="http://localhost:8001/auth/taihu-production" class="button success" target="_blank">
            🏭 腾讯太湖生产环境登录
        </a>
        
        <h3>其他功能</h3>
        <a href="http://localhost:8001/auth/logout" class="button warning" target="_blank">
            🚪 注销 (类似Node.js logout路由)
        </a>
        <a href="http://localhost:8001/api/health" class="button" target="_blank">
            ❤️ 健康检查
        </a>
    </div>

    <div class="container">
        <h2>🔄 Node.js vs Python 实现对比</h2>
        <div class="comparison">
            <div class="comparison-item nodejs">
                <h4>Node.js 原始代码</h4>
                <ul>
                    <li>Express + Passport.js</li>
                    <li>Session中间件</li>
                    <li>GitHubStrategy</li>
                    <li>OIDCStrategy</li>
                    <li>req.isAuthenticated()</li>
                    <li>req.logout()</li>
                </ul>
            </div>
            <div class="comparison-item python">
                <h4>Python FastAPI 实现</h4>
                <ul>
                    <li>FastAPI + 自定义OAuth</li>
                    <li>SessionMiddleware</li>
                    <li>GitHub OAuth2.0</li>
                    <li>太湖OIDC</li>
                    <li>session检查</li>
                    <li>session.clear()</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>⚙️ 配置参数 (与Node.js完全一致)</h2>
        
        <h3>GitHub配置</h3>
        <div class="config">GITHUB_CLIENT_ID = '********************'
GITHUB_CLIENT_SECRET = 'a83bbda7f7b9eabab63f5b6592e674fc1bf9286c'
callbackURL = "http://localhost:8001/auth/github/callback"</div>

        <h3>太湖测试环境配置</h3>
        <div class="config">TAIHU_CLIENT_ID = 'test-app'
TAIHU_CLIENT_SECRET = 'ilX3uqWyJRbK'
TAIHU_ISSUER = 'https://test-odc.it.woa.com/api/auth-center/oauth2/'
TAIHU_AUTH_URL = 'https://test-odc.it.woa.com/api/auth-center/oauth2/authorize'
TAIHU_TOKEN_URL = 'https://test-odc.it.woa.com/api/auth-center/oauth2/token'
TAIHU_USERINFO_URL = 'https://test-odc.it.woa.com/api/auth-center/oauth2/userinfo'
callbackURL = "http://localhost:8001/auth/taihu/callback"</div>

        <h3>太湖生产环境配置</h3>
        <div class="config">PRODUCTION_TAIHU_CLIENT_ID = 'api-docs'
PRODUCTION_TAIHU_CLIENT_SECRET = 'WNEK28UBJHHMN0D81GMPBHAWWZ4GEL2U'
PRODUCTION_TAIHU_ISSUER = 'https://tai.it.tencent.com/api/auth-center/oauth2/'
PRODUCTION_TAIHU_AUTH_URL = 'https://tai.it.tencent.com/api/auth-center/oauth2/authorize'
PRODUCTION_TAIHU_TOKEN_URL = 'https://tai.it.tencent.com/api/auth-center/oauth2/token'
PRODUCTION_TAIHU_USERINFO_URL = 'https://tai.it.tencent.com/api/auth-center/oauth2/userinfo'
callbackURL = "https://api-docs.woa.com/auth/tencent/callback"</div>
    </div>

    <div class="container">
        <h2>🔄 路由映射对比</h2>
        <table style="width: 100%; border-collapse: collapse;">
            <tr style="background: #f8f9fa;">
                <th style="padding: 10px; border: 1px solid #ddd;">Node.js路由</th>
                <th style="padding: 10px; border: 1px solid #ddd;">Python FastAPI路由</th>
                <th style="padding: 10px; border: 1px solid #ddd;">功能</th>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">GET /</td>
                <td style="padding: 10px; border: 1px solid #ddd;">GET /</td>
                <td style="padding: 10px; border: 1px solid #ddd;">首页，显示登录选项</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">GET /auth/github</td>
                <td style="padding: 10px; border: 1px solid #ddd;">GET /auth/github</td>
                <td style="padding: 10px; border: 1px solid #ddd;">GitHub登录</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">GET /auth/github/callback</td>
                <td style="padding: 10px; border: 1px solid #ddd;">GET /auth/github/callback</td>
                <td style="padding: 10px; border: 1px solid #ddd;">GitHub回调</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">GET /auth/taihu</td>
                <td style="padding: 10px; border: 1px solid #ddd;">GET /auth/taihu</td>
                <td style="padding: 10px; border: 1px solid #ddd;">太湖测试环境登录</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">GET /auth/taihu/callback</td>
                <td style="padding: 10px; border: 1px solid #ddd;">GET /auth/taihu/callback</td>
                <td style="padding: 10px; border: 1px solid #ddd;">太湖测试环境回调</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">GET /auth/taihu-production</td>
                <td style="padding: 10px; border: 1px solid #ddd;">GET /auth/taihu-production</td>
                <td style="padding: 10px; border: 1px solid #ddd;">太湖生产环境登录</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">GET /auth/taihu-production/callback</td>
                <td style="padding: 10px; border: 1px solid #ddd;">GET /auth/tencent/callback</td>
                <td style="padding: 10px; border: 1px solid #ddd;">太湖生产环境回调</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">GET /profile</td>
                <td style="padding: 10px; border: 1px solid #ddd;">GET /auth/profile</td>
                <td style="padding: 10px; border: 1px solid #ddd;">用户资料页面</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">GET /logout</td>
                <td style="padding: 10px; border: 1px solid #ddd;">GET /auth/logout</td>
                <td style="padding: 10px; border: 1px solid #ddd;">注销功能</td>
            </tr>
        </table>
    </div>

    <div class="container">
        <div class="highlight">
            <h3>🚨 重要说明</h3>
            <p><strong>完全按照您提供的Node.js代码参数实现：</strong></p>
            <ul>
                <li>✅ 所有配置参数与Node.js代码完全一致</li>
                <li>✅ 路由结构与Node.js代码保持对应</li>
                <li>✅ Session管理机制相同 (secret: 'keyboard cat')</li>
                <li>✅ 回调地址与Node.js代码一致</li>
                <li>✅ 用户认证流程与Passport.js逻辑相同</li>
            </ul>
            <p><strong>需要注册的回调地址：</strong></p>
            <ul>
                <li>太湖测试环境：<code>http://localhost:8001/auth/taihu/callback</code></li>
                <li>太湖生产环境：<code>https://api-docs.woa.com/auth/tencent/callback</code></li>
            </ul>
        </div>
    </div>

    <script>
        console.log('🎉 Node.js兼容的太湖登录功能已完成');
        console.log('📝 所有参数与Node.js代码完全一致');
        
        // 检查服务器状态
        fetch('http://localhost:8001/api/health')
            .then(response => response.json())
            .then(data => {
                console.log('✅ 后端服务器状态:', data);
            })
            .catch(error => {
                console.log('❌ 无法连接到后端服务器:', error);
            });
    </script>
</body>
</html>
