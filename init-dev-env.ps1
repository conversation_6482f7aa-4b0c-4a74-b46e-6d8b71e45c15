# 设置环境变量
$env:ENV = "development"

Write-Host "Initializing development environment..."

# 检查 Python 版本
$pythonVersion = (python --version 2>&1) | Out-String
if (-not ($pythonVersion -match "Python 3\.10\.*")) {
    Write-Host "Error: Python 3.10.x is required. Current version: $pythonVersion"
    Write-Host "Please install Python 3.10 and try again."
    exit 1
}

# 清理现有虚拟环境
if (Test-Path ".venv") {
    Write-Host "Removing existing virtual environment..."
    Remove-Item -Recurse -Force .venv
}

# 检查 uv 是否已安装
if (-not (Get-Command uv -ErrorAction SilentlyContinue)) {
    Write-Host "Installing uv package manager..."
    pip install uv
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to install uv!"
        exit 1
    }
}

# 创建新的虚拟环境
Write-Host "Creating new virtual environment with Python 3.10..."
python -m venv .venv --prompt api-docs-server

# 激活虚拟环境
Write-Host "Activating virtual environment..."
.\.venv\Scripts\Activate.ps1

# 验证虚拟环境的 Python 版本
$venvPythonVersion = (python --version 2>&1) | Out-String
if (-not ($venvPythonVersion -match "Python 3\.10\.*")) {
    Write-Host "Error: Virtual environment Python version mismatch."
    Write-Host "Expected: Python 3.10.x, Got: $venvPythonVersion"
    deactivate
    Remove-Item -Recurse -Force .venv
    exit 1
}

# 使用 uv 安装依赖
Write-Host "Installing dependencies using uv..."
uv pip install -r requirements.txt

if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to install dependencies!"
    exit 1
}

Write-Host "Development environment initialized successfully!"
Write-Host "You can now run 'start-dev-backend.ps1' to start the development server."
