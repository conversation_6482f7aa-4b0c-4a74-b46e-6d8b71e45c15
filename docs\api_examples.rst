API 使用示例
===========

本文档提供了常见 API 操作的代码示例。

文档管理示例
----------

创建文档
~~~~~~~
.. code-block:: typescript

   // 创建新文档
   const createDocument = async (doc: ApiDocument) => {
     try {
       const response = await fetch('/api/documents', {
         method: 'POST',
         headers: {
           'Content-Type': 'application/json',
         },
         body: JSON.stringify({
           title: doc.title,
           url: doc.url,
           description: doc.description,
           tags: doc.tags,
           type: 'API_DOC'
         })
       });

       if (!response.ok) {
         throw new Error(`HTTP error! status: ${response.status}`);
       }

       const result = await response.json();
       return result;
     } catch (error) {
       console.error('创建文档失败:', error);
       throw error;
     }
   }

   // 使用示例
   const newDoc = {
     title: 'API 文档示例',
     url: 'https://api.example.com/docs',
     description: '这是一个示例API文档',
     tags: ['api', 'example'],
   };

   try {
     const createdDoc = await createDocument(newDoc);
     console.log('文档创建成功:', createdDoc);
   } catch (error) {
     // 处理错误
   }

更新文档
~~~~~~~
.. code-block:: typescript

   // 更新现有文档
   const updateDocument = async (id: string, updates: Partial<ApiDocument>) => {
     try {
       const response = await fetch(`/api/documents/${id}`, {
         method: 'PATCH',
         headers: {
           'Content-Type': 'application/json',
         },
         body: JSON.stringify(updates)
       });

       if (!response.ok) {
         throw new Error(`HTTP error! status: ${response.status}`);
       }

       const result = await response.json();
       return result;
     } catch (error) {
       console.error('更新文档失败:', error);
       throw error;
     }
   }

   // 使用示例
   const docUpdates = {
     title: '更新后的标题',
     description: '更新后的描述'
   };

   try {
     const updatedDoc = await updateDocument('doc123', docUpdates);
     console.log('文档更新成功:', updatedDoc);
   } catch (error) {
     // 处理错误
   }

开发者注意事项
-----------

请求头设置
~~~~~~~~
* 始终设置 ``Content-Type: application/json``
* 如果需要认证，添加 ``Authorization`` 头

错误处理
~~~~~~~
* 所有请求都应该包含适当的错误处理
* 使用 try-catch 捕获网络错误
* 检查响应状态码
* 记录错误信息以便调试
