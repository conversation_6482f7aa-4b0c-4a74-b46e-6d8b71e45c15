<template>
  <Dialog
    :visible="modelValue"
    @update:visible="$emit('update:modelValue', $event)"
    modal
    :style="{ width: '90vw', maxWidth: '1400px' }"
    maximizable
    position="center"
    class="preview-dialog"
    :closable="false"
  >
    <template #header>
      <div class="preview-header">
        <div class="flex align-items-center gap-2">
          <i :class="getDocumentIcon(doc)" class="text-xl"></i>
          <span class="preview-title">{{ doc?.title || '未命名文档' }}</span>
        </div>
        <div class="preview-toolbar">
          <Button
            icon="pi pi-external-link"
            text
            plain
            class="preview-button"
            v-tooltip.bottom="'在新标签页打开'"
            @click="openInNewTab(doc?.url)"
          />
          <Button
            icon="pi pi-times"
            text
            plain
            class="preview-button"
            v-tooltip.bottom="'关闭'"
            @click="$emit('update:modelValue', false)"
          />
        </div>
      </div>
    </template>
    <div class="preview-container">
      <div v-if="props.loading" class="loading-spinner flex align-items-center justify-content-center">
        <i class="pi pi-spin pi-spinner" style="font-size: 2rem"></i>
      </div>
      <template v-else-if="doc?.url">
        <div class="preview-content">
          <i class="pi pi-external-link text-4xl mb-3"></i>
          <span>点击下方按钮在新标签页中打开文档</span>
          <Button
            label="在新标签页中打开"
            icon="pi pi-external-link"
            @click="openInNewTab(doc.url)"
            class="mt-3"
          />
        </div>
      </template>
      <div v-else class="preview-placeholder">
        <i class="pi pi-file-edit text-4xl mb-3"></i>
        <span>无效的文档地址</span>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { ApiDoc } from '@/types'

interface Props {
  modelValue: boolean;
  doc?: ApiDoc | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
}>()

function getDocumentIcon(doc: ApiDoc | null | undefined): string {
  if (!doc?.url) return 'pi pi-file-o';
  try {
    const url = new URL(doc.url);
    if (url.hostname.includes('swagger')) return 'pi pi-code';
    if (url.hostname.includes('openapi')) return 'pi pi-book';
    if (url.hostname.includes('github')) return 'pi pi-github';
    if (url.hostname.includes('gitlab')) return 'pi pi-code';
    return 'pi pi-file-o';
  } catch (error) {
    console.error('Error parsing URL:', error);
    return 'pi pi-file-o';
  }
}

function openInNewTab(url?: string): void {
  if (url) {
    window.open(url, '_blank')
  }
}
</script>

<style scoped>
.preview-dialog {
  :deep(.p-dialog-header) {
    padding: 1rem 1.5rem;
    background: var(--surface-section);
    border-bottom: 1px solid var(--surface-border);
  }

  :deep(.p-dialog-content) {
    padding: 0;
    background: var(--surface-ground);
  }
}

.preview-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.preview-title {
  font-size: 1.2rem;
  font-weight: 600;
}

.preview-toolbar {
  display: flex;
  gap: 0.5rem;
}

.preview-button {
  width: 2.5rem;
  height: 2.5rem;
}

.preview-container {
  height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--surface-ground);
}

.preview-content,
.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: var(--text-color-secondary);
}

.loading-spinner {
  height: 100%;
}
</style>
