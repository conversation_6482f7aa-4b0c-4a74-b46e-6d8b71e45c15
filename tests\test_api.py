import asyncio
import httpx
import json
from urllib.parse import urljoin

BASE_URL = "http://localhost:8000"

async def test_api():
    async with httpx.AsyncClient() as client:
        # 测试健康检查
        response = await client.get(urljoin(BASE_URL, "/api/health"))
        print("Health Check:", response.status_code, response.json())

        # 测试 URL 验证
        test_urls = [
            "https://www.google.com",
            "https://api.github.com",
            "https://invalid-url-test.com",
            "not-a-url"
        ]

        print("\n=== Testing URL Validation ===")
        for url in test_urls:
            try:
                response = await client.post(
                    urljoin(BASE_URL, "/api/docs/url/validate"),
                    json={"url": url}
                )
                print(f"\nURL: {url}")
                print("Status:", response.status_code)
                print("Response:", response.json() if response.status_code < 300 else response.text)
            except Exception as e:
                print(f"\nURL: {url}")
                print("Error:", str(e))

        # 测试文档 CRUD
        print("\n=== Testing Document CRUD ===")
        
        # 创建文档
        doc_data = {
            "url": "https://api.github.com",
            "title": "GitHub API",
            "description": "GitHub REST API documentation"
        }
        
        try:
            print("\nCreating document...")
            response = await client.post(
                urljoin(BASE_URL, "/api/docs"),
                json=doc_data
            )
            print("Status:", response.status_code)
            print("Response:", response.json() if response.status_code < 300 else response.text)
            
            if response.status_code < 300:
                doc_id = response.json()["id"]
                
                # 获取文档
                print("\nGetting document...")
                response = await client.get(urljoin(BASE_URL, f"/api/docs/{doc_id}"))
                print("Status:", response.status_code)
                print("Response:", response.json() if response.status_code < 300 else response.text)
                
                # 更新文档
                print("\nUpdating document...")
                update_data = {
                    "title": "Updated GitHub API",
                    "description": "Updated description"
                }
                response = await client.put(
                    urljoin(BASE_URL, f"/api/docs/{doc_id}"),
                    json=update_data
                )
                print("Status:", response.status_code)
                print("Response:", response.json() if response.status_code < 300 else response.text)
                
                # 删除文档
                print("\nDeleting document...")
                response = await client.delete(urljoin(BASE_URL, f"/api/docs/{doc_id}"))
                print("Status:", response.status_code)
                print("Response:", response.text)
                
        except Exception as e:
            print("Error:", str(e))

if __name__ == "__main__":
    asyncio.run(test_api())
