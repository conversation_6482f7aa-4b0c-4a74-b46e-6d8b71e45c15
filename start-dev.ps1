# 启动后端
$backendJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    .\start-dev-backend.ps1
}

# 启动前端
$frontendJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    cd frontend
    .\start-dev.ps1
}

Write-Host "Starting development servers..."
Write-Host "Backend: http://localhost:8000"
Write-Host "Frontend: http://localhost:3000"
Write-Host "Press Ctrl+C to stop all servers"

try {
    # 显示两个作业的输出
    while ($true) {
        Receive-Job $backendJob
        Receive-Job $frontendJob
        Start-Sleep -Seconds 1
    }
} finally {
    # 清理作业
    Stop-Job $backendJob, $frontendJob
    Remove-Job $backendJob, $frontendJob
}
