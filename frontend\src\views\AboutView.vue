<template>
  <div class="about">
    <h1>关于 API 文档服务</h1>
    <div class="content">
      <p>API 文档服务是一个用于管理和查看 API 文档的平台。它提供以下功能：</p>
      <ul>
        <li>API 文档的添加和管理</li>
        <li>文档内容的在线查看</li>
        <li>文档搜索功能</li>
        <li>支持多种文档格式</li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
.about {
  padding: 20px;
}

h1 {
  color: #2c3e50;
  margin-bottom: 20px;
}

.content {
  max-width: 800px;
  margin: 0 auto;
}

p {
  margin-bottom: 16px;
  line-height: 1.6;
}

ul {
  padding-left: 20px;
}

li {
  margin-bottom: 8px;
  line-height: 1.4;
}
</style>
