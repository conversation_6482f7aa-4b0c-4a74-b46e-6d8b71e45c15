入门指南
========

环境设置
--------

操作系统要求
~~~~~~~~~~
* 开发环境：Windows
* 命令行工具：PowerShell
* 路径格式：使用正斜杠 "C:/"

前置要求
~~~~~~~
开始之前，请确保已安装以下软件：

* Python 3.x
* Node.js 和 npm
* Git
* Visual Studio Code（推荐）

项目初始化
---------

1. 克隆代码仓库
~~~~~~~~~~~~~
.. code-block:: powershell

   git clone https://git.woa.com/lightbox/microservices/api_docs_server
   cd api_docs_server

2. 后端环境配置
~~~~~~~~~~~~~
按照以下步骤配置 Python 后端环境：

.. code-block:: powershell

   # 1. 初始化开发环境
   ./init-dev-env.ps1  # 这将在 .venv/ 目录创建虚拟环境

   # 2. 使用 uv 安装依赖
   ./.venv/Scripts/activate
   uv pip install -r requirements.txt

   # 3. 启动开发服务器
   ./start-dev-backend.ps1

3. 前端环境配置
~~~~~~~~~~~~~
配置 Vue.js 前端环境：

.. code-block:: powershell

   cd frontend
   
   # 安装依赖（使用精确版本）
   npm install --save-exact
   
   # 启动开发服务器
   npm run dev

开发工具配置
----------

IDE 设置
~~~~~~~
配置你的 IDE，确保：

* 安装并启用 pylint 进行代码检查
* 启用代码自动补全功能
* 配置代码自动格式化

版本控制规范
~~~~~~~~~~
* 进行小规模的增量更改
* 编写清晰的提交信息
* 使用功能分支进行开发
* 定期同步主分支代码

代码审查流程
~~~~~~~~~~
* 生成易于审查的代码
* 关注代码质量和一致性
* 验证功能完整性
* 检查测试覆盖率
