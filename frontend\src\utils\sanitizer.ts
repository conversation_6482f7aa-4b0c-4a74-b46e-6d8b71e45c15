/**
 * HTMLu5185u5bb9u6e05u7406u5de5u5177
 */
export class Sanitizer {
  /**
   * u6e05u7406HTMLu5185u5bb9uff0cu79fbu9664u6f5cu5728u7684XSSu653bu51fb
   * @param html u8f93u5165u7684HTMLu5185u5bb9
   * @returns u6e05u7406u540eu7684HTML
   */
  static sanitizeHtml(html: string): string {
    if (!html) return '';
    
    // u7b80u5355u7684HTMLu6e05u7406u5b9eu73b0
    // u5728u5b9eu9645u751fu4ea7u73afu5883u4e2du5e94u4f7fu7528u66f4u5b8cu5584u7684u5e93u5982DOMPurify
    return html
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }
}
