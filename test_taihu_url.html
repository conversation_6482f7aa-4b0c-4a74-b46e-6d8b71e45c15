<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>太湖登录URL测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            line-height: 1.6;
        }
        .url-box {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            word-break: break-all;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>太湖登录URL测试页面</h1>
    
    <div class="section">
        <h2>您提供的正确URL</h2>
        <p>这是您提供的能够显示太湖登录页面的URL：</p>
        <div class="url-box">
            https://tai.it.tencent.com/api/auth-center/oauth2/authorize?client_id=api-docs&response_type=code&scope=openid+offline&state=abcdefg123456&redirect_uri=https://api-docs.woa.com/taihu_callback
        </div>
        <a href="https://tai.it.tencent.com/api/auth-center/oauth2/authorize?client_id=api-docs&response_type=code&scope=openid+offline&state=abcdefg123456&redirect_uri=https://api-docs.woa.com/taihu_callback" 
           class="button" target="_blank">测试此URL</a>
    </div>

    <div class="section">
        <h2>我们系统生成的URL</h2>
        <p>这是我们的前端代码生成的URL：</p>
        <div class="url-box" id="generatedUrl">
            正在生成...
        </div>
        <button class="button" onclick="testGeneratedUrl()">测试生成的URL</button>
        <button class="button" onclick="generateUrl()">重新生成URL</button>
    </div>

    <div class="section">
        <h2>URL参数对比</h2>
        <table border="1" style="width: 100%; border-collapse: collapse;">
            <tr>
                <th style="padding: 8px;">参数</th>
                <th style="padding: 8px;">您的URL</th>
                <th style="padding: 8px;">我们的URL</th>
                <th style="padding: 8px;">状态</th>
            </tr>
            <tbody id="comparisonTable">
                <!-- 将通过JavaScript填充 -->
            </tbody>
        </table>
    </div>

    <script>
        // 您提供的正确URL参数
        const correctParams = {
            'client_id': 'api-docs',
            'response_type': 'code',
            'scope': 'openid offline',
            'state': 'abcdefg123456',
            'redirect_uri': 'https://api-docs.woa.com/taihu_callback'
        };

        function generateUrl() {
            // 模拟我们前端代码的URL生成逻辑
            const redirect = '/'; // 默认重定向
            const authUrl = new URL('https://tai.it.tencent.com/api/auth-center/oauth2/authorize');
            authUrl.searchParams.set('client_id', 'api-docs');
            authUrl.searchParams.set('response_type', 'code');
            authUrl.searchParams.set('scope', 'openid offline');
            authUrl.searchParams.set('state', redirect);
            authUrl.searchParams.set('redirect_uri', 'https://api-docs.woa.com/taihu_callback');
            
            const generatedUrl = authUrl.toString();
            document.getElementById('generatedUrl').textContent = generatedUrl;
            
            // 更新对比表格
            updateComparisonTable(generatedUrl);
            
            return generatedUrl;
        }

        function testGeneratedUrl() {
            const url = generateUrl();
            window.open(url, '_blank');
        }

        function updateComparisonTable(generatedUrl) {
            const generatedUrlObj = new URL(generatedUrl);
            const generatedParams = {};
            
            for (const [key, value] of generatedUrlObj.searchParams) {
                generatedParams[key] = value;
            }

            const tableBody = document.getElementById('comparisonTable');
            tableBody.innerHTML = '';

            for (const param in correctParams) {
                const row = tableBody.insertRow();
                const paramCell = row.insertCell(0);
                const correctCell = row.insertCell(1);
                const generatedCell = row.insertCell(2);
                const statusCell = row.insertCell(3);

                paramCell.textContent = param;
                correctCell.textContent = correctParams[param];
                generatedCell.textContent = generatedParams[param] || '缺失';
                
                const isMatch = correctParams[param] === generatedParams[param];
                statusCell.textContent = isMatch ? '✅ 匹配' : '❌ 不匹配';
                statusCell.style.color = isMatch ? 'green' : 'red';
                statusCell.style.padding = '8px';
                
                [paramCell, correctCell, generatedCell].forEach(cell => {
                    cell.style.padding = '8px';
                });
            }
        }

        // 页面加载时生成URL
        window.onload = function() {
            generateUrl();
        };
    </script>
</body>
</html>
