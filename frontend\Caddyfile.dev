{
    debug
    local_certs
}

:3000 {
    root * ./dist
    encode gzip

    # 前端健康检查路径
    handle /health {
        header Content-Type "text/plain"
        respond 200 "OK"
    }

    # API 请求代理到后端
    handle /backend/* {
        uri strip_prefix /backend
        reverse_proxy localhost:8000 {
            header_up Host {upstream_hostport}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
        }
    }

    # 其他路径
    handle {
        try_files {path} /index.html
        file_server
    }

    # 添加响应头
    header {
        Access-Control-Allow-Origin *
        Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
        Access-Control-Allow-Headers "*"
        Access-Control-Allow-Credentials "true"
        Access-Control-Max-Age "3600"
        # 确保这些头部被添加到所有响应中
        defer
    }

    # 错误处理
    handle_errors {
        respond "{err.status_code} {err.status_text}" {err.status_code}
    }
}
