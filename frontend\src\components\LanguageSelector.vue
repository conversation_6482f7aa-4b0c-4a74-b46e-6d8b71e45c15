<template>
  <div class="language-selector">
    <Button
      class="p-button-text p-button-rounded lang-button"
      @click="menu.toggle($event)"
      aria-haspopup="true"
      aria-controls="language-menu"
      v-tooltip.bottom="$t('language.switch')"
    >
      <template #icon>
        <span class="lang-icon">{{ getLocaleShort(selectedLocale) }}</span>
      </template>
    </Button>
    <Menu 
      id="language-menu"
      ref="menu" 
      :model="menuItems" 
      :popup="true"
      class="language-menu surface-card shadow-md border-round-lg"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useI18n } from '@/i18n'
import type { MenuItem } from 'primevue/menuitem'
import Menu from 'primevue/menu'
import Button from 'primevue/button'

const { setLocale, initLocale } = useI18n()
const menu = ref()

const locales = [
  { code: 'zh-CN', name: '简体中文', short: '中' },
  { code: 'en-US', name: 'English', short: 'EN' }
]

const selectedLocale = ref(localStorage.getItem('locale') || 'zh-CN')

const menuItems = computed<MenuItem[]>(() => 
  locales.map(locale => ({
    label: locale.name,
    class: selectedLocale.value === locale.code ? 'active-locale' : '',
    command: () => {
      selectedLocale.value = locale.code
      setLocale(locale.code)
    }
  }))
)

const getLocaleShort = (code: string) => {
  return locales.find(locale => locale.code === code)?.short || code
}

// 监听语言变化
watch(selectedLocale, (newLocale) => {
  setLocale(newLocale)
})

// 初始化语言
onMounted(() => {
  initLocale()
})
</script>

<style scoped>
.language-selector {
  display: inline-flex;
  align-items: center;
}

.lang-button {
  min-width: 36px;
  height: 36px;
  padding: 0 !important;
}

.lang-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color);
  background: var(--surface-ground);
  border-radius: 4px;
  transition: all 0.2s;
}

:deep(.p-button:hover) .lang-icon {
  color: var(--primary-color);
  background: var(--primary-50);
}

:deep(.p-button:focus) {
  box-shadow: none;
}

:deep(.p-button:focus) .lang-icon {
  color: var(--primary-color);
  background: var(--primary-50);
}

.language-menu {
  min-width: 150px;
}

:deep(.active-locale) {
  font-weight: 600;
  background: var(--primary-50);
  color: var(--primary-color);
}

:deep(.p-menu .p-menuitem-link .p-menuitem-text) {
  color: var(--text-color);
}

:deep(.p-menu .p-menuitem-link:not(.p-disabled):hover) {
  background: var(--surface-hover);
}

:deep(.p-menu .p-menuitem-link:focus) {
  box-shadow: inset 0 0 0 0.15rem var(--primary-200);
}

/* 移动端适配 */
@media screen and (max-width: 640px) {
  .lang-button {
    min-width: 32px;
    height: 32px;
  }

  .lang-icon {
    width: 22px;
    height: 22px;
    font-size: 0.8125rem;
  }
}
</style>
