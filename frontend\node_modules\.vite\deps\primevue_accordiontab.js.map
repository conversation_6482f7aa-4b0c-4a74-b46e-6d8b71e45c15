{"version": 3, "sources": ["../../src/accordiontab/style/AccordionTabStyle.js", "../../src/accordiontab/BaseAccordionTab.vue", "../../src/accordiontab/AccordionTab.vue", "../../src/accordiontab/AccordionTab.vue?vue&type=template&id=5ee2e540&lang.js"], "sourcesContent": ["import BaseStyle from '@primevue/core/base/style';\n\nexport default BaseStyle.extend({\n    name: 'accordiontab'\n});\n", "<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport AccordionTabStyle from 'primevue/accordiontab/style';\n\nexport default {\n    name: 'BaseAccordionTab',\n    extends: BaseComponent,\n    props: {\n        header: null,\n        headerStyle: null,\n        headerClass: null,\n        headerProps: null,\n        headerActionProps: null,\n        contentStyle: null,\n        contentClass: null,\n        contentProps: null,\n        disabled: Boolean\n    },\n    style: AccordionTabStyle,\n    provide() {\n        return {\n            $pcAccordionTab: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <slot></slot>\n</template>\n\n<script>\nimport BaseAccordionTab from './BaseAccordionTab.vue';\n\nexport default {\n    name: 'AccordionTab',\n    extends: BaseAccordionTab,\n    inheritAttrs: false,\n    mounted() {\n        console.warn('Deprecated since v4. Use the new structure of Accordion instead.');\n    }\n};\n</script>\n", "<template>\n    <slot></slot>\n</template>\n\n<script>\nimport BaseAccordionTab from './BaseAccordionTab.vue';\n\nexport default {\n    name: 'AccordionTab',\n    extends: BaseAccordionTab,\n    inheritAttrs: false,\n    mounted() {\n        console.warn('Deprecated since v4. Use the new structure of Accordion instead.');\n    }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;;AAEA,IAAA,oBAAeA,UAAUC,OAAO;EAC5BC,MAAM;AACV,CAAC;;;ACAD,IAAA,WAAe;EACXC,MAAM;EACN,WAASC;EACTC,OAAO;IACHC,QAAQ;IACRC,aAAa;IACbC,aAAa;IACbC,aAAa;IACbC,mBAAmB;IACnBC,cAAc;IACdC,cAAc;IACdC,cAAc;IACdC,UAAUC;;EAEdC,OAAOC;EACPC,SAAO,SAAPA,UAAU;AACN,WAAO;MACHC,iBAAiB;MACjBC,iBAAiB;;EAEzB;AACJ;AClBA,IAAAC,UAAe;EACXlB,MAAM;EACN,WAASmB;EACTC,cAAc;EACdC,SAAO,SAAPA,UAAU;AACNC,YAAQC,KAAK,kEAAkE;EACnF;AACJ;;SCbIC,WAAYC,KAAAC,QAAA,SAAA;;;", "names": ["BaseStyle", "extend", "name", "name", "BaseComponent", "props", "header", "headerStyle", "headerClass", "headerProps", "headerActionProps", "contentStyle", "contentClass", "contentProps", "disabled", "Boolean", "style", "AccordionTabStyle", "provide", "$pcAccordionTab", "$parentInstance", "script", "BaseAccordionTab", "inheritAttrs", "mounted", "console", "warn", "_renderSlot", "_ctx", "$slots"]}