"""API Documentation tests."""
import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from api_docs_server.models.api_doc import ApiDoc

pytestmark = pytest.mark.asyncio

async def test_create_api_doc(client: AsyncClient, db_session: AsyncSession):
    """测试创建API文档."""
    response = await client.post(
        "/api/docs",
        json={
            "url": "https://example.com",
            "title": "Example API",
            "description": "An example API",
            "icon_url": "https://example.com/icon.png",
            "auto_metadata": False
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["url"] == "https://example.com"
    assert data["title"] == "Example API"
    assert data["description"] == "An example API"
    assert data["icon_url"] == "https://example.com/icon.png"
    assert not data["auto_metadata"]

async def test_get_api_doc(client: AsyncClient, db_session: AsyncSession):
    """测试获取API文档."""
    # 创建测试文档
    api_doc = ApiDoc(
        url="https://example.com",
        title="Example API",
        description="An example API",
        icon_url="https://example.com/icon.png",
        auto_metadata=False
    )
    db_session.add(api_doc)
    await db_session.commit()
    await db_session.refresh(api_doc)

    response = await client.get(f"/api/docs/{api_doc.id}")
    assert response.status_code == 200
    data = response.json()
    assert data["url"] == "https://example.com"
    assert data["title"] == "Example API"
    assert data["description"] == "An example API"
    assert data["icon_url"] == "https://example.com/icon.png"
    assert not data["auto_metadata"]

async def test_list_api_docs(client: AsyncClient, db_session: AsyncSession):
    """测试获取API文档列表."""
    # 创建测试文档
    api_doc1 = ApiDoc(
        url="https://example1.com",
        title="Example API 1",
        auto_metadata=False
    )
    api_doc2 = ApiDoc(
        url="https://example2.com",
        title="Example API 2",
        auto_metadata=False
    )
    db_session.add(api_doc1)
    db_session.add(api_doc2)
    await db_session.commit()

    response = await client.get("/api/docs")
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 2
    assert data[0]["url"] == "https://example1.com"
    assert data[1]["url"] == "https://example2.com"

async def test_update_api_doc(client: AsyncClient, db_session: AsyncSession):
    """测试更新API文档."""
    # 创建测试文档
    api_doc = ApiDoc(
        url="https://example.com",
        title="Example API",
        auto_metadata=False
    )
    db_session.add(api_doc)
    await db_session.commit()
    await db_session.refresh(api_doc)

    response = await client.put(
        f"/api/docs/{api_doc.id}",
        json={
            "title": "Updated API",
            "description": "Updated description"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == "Updated API"
    assert data["description"] == "Updated description"

async def test_delete_api_doc(client: AsyncClient, db_session: AsyncSession):
    """测试删除API文档."""
    # 创建测试文档
    api_doc = ApiDoc(
        url="https://example.com",
        title="Example API",
        auto_metadata=False
    )
    db_session.add(api_doc)
    await db_session.commit()
    await db_session.refresh(api_doc)

    response = await client.delete(f"/api/docs/{api_doc.id}")
    assert response.status_code == 200

    # 验证文档已被删除
    result = await db_session.get(ApiDoc, api_doc.id)
    assert result is None

async def test_refresh_metadata(client: AsyncClient, db_session: AsyncSession):
    """测试刷新API文档元数据."""
    # 创建测试文档
    api_doc = ApiDoc(
        url="https://example.com",
        title="Example API",
        auto_metadata=True
    )
    db_session.add(api_doc)
    await db_session.commit()
    await db_session.refresh(api_doc)

    response = await client.post(f"/api/docs/{api_doc.id}/refresh")
    assert response.status_code == 200
