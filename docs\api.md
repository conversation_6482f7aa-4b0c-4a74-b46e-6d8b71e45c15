# API 文档

## 基础信息

- 基础URL: `http://localhost:8000` (开发环境)
- 内容类型: `application/json`
- 认证方式: <PERSON><PERSON> (如果需要)

## API 端点

### 文档管理

#### 创建文档

```http
POST /api/docs
Content-Type: application/json
Authorization: Bearer <token> (可选)

{
    "title": "文档标题",
    "url": "文档URL",
    "description": "文档描述",
    "tags": ["标签1", "标签2"]
}
```

响应:
```json
{
    "id": "文档ID",
    "title": "文档标题",
    "url": "文档URL",
    "description": "文档描述",
    "tags": ["标签1", "标签2"],
    "created_at": "创建时间",
    "updated_at": "更新时间"
}
```

#### 获取文档列表

```http
GET /api/docs
```

响应:
```json
[
    {
        "id": "文档ID",
        "title": "文档标题",
        "url": "文档URL",
        "description": "文档描述",
        "tags": ["标签1", "标签2"],
        "created_at": "创建时间",
        "updated_at": "更新时间"
    }
]
```

#### 搜索文档

```http
GET /api/docs/search?q=关键词
```

响应:
```json
[
    {
        "id": "文档ID",
        "title": "文档标题",
        "url": "文档URL",
        "description": "文档描述",
        "tags": ["标签1", "标签2"],
        "created_at": "创建时间",
        "updated_at": "更新时间"
    }
]
```

### 系统功能

#### 刷新通知

```http
POST /api/notify/refresh
```

响应:
```json
{
    "status": "success",
    "message": "refresh notification sent"
}
```

## 开发者指南

1. **直接调用后端 API**
   - 推荐开发者直接调用后端 API，而不是使用前端封装
   - 这样可以避免不必要的依赖，提高代码的可维护性

2. **错误处理**
   - 所有错误响应都会返回标准的 HTTP 状态码
   - 错误响应体包含详细的错误信息

3. **最佳实践**
   - 总是检查响应状态码
   - 实现适当的错误处理
   - 考虑添加重试机制
   - 在创建或更新文档后调用刷新通知端点
