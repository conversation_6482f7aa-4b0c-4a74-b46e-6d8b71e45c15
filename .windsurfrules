# -------------------------------
# Vue3 + TS + Vite + PrimeVue 规范
# 版本：2.0.0
# -------------------------------

[Project Structure]
# 目录结构
frontend/src/
  ├─ types/                  = 全局类型定义，按模块分文件（如 user.d.ts）
  ├─ plugins/
  │   └─ primevue.ts         = PrimeVue 初始化配置（必须导出 PrimeVue 实例类型）
  └─ vite-env.d.ts           = 必须存在且包含 Vite 环境变量类型扩展

[Vite Config]
# Vite 构建规则
vite.build.config = 必须包含以下配置：
  - `build.cssTarget: "chrome80"` (兼容 PrimeVue 主题)
  - `build.rollupOptions.output.manualChunks` 按路由分块
  - 禁用 `build.cssCodeSplit: false` (保持主题完整性)
vite.plugins = 必须包含 `vite-plugin-checker` 用于 TS 类型检查

[PrimeVue TS Rules]
# TypeScript 强化
primevue.component-type    = 使用 PrimeVue 组件时必须导入对应类型（如 ButtonProps）
props.type-definition      = 组件 props 必须使用 interface 定义，禁止内联类型
event.emits-type           = 自定义事件必须使用类型声明（通过 defineEmits<T>()）
global-components          = 全局注册的 PrimeVue 组件需在 src/types/components.d.ts 中扩展类型

[Dependency Management]
# npm 依赖规范
npm.dependencies    = PrimeVue 相关依赖必须固定版本（禁止使用 ^ 或 ~）
npm.install-mode    = 强制使用 `npm install --save-exact` 安装依赖
lockfile            = package-lock.json 必须提交且每周执行 npm audit
peer-deps           = 检查 PrimeVue 与 Vue/TypeScript 的版本兼容性

[Vite Optimization]
# 性能优化
dynamic-import-syntax = 必须使用 Vite 动态导入语法：`() => import(/* @vite-ignore */ '...')`
static-assets         = PrimeVue 图标必须通过 npm 导入，禁止使用本地复制

[Code Style]
# 代码规范
script-setup      = 强制使用 <script setup lang="ts"> 语法
type-imports      = 类型导入必须使用 import type 语法
no-any            = 禁止在 PrimeVue 事件处理函数中使用 any 类型（需精确类型）
utility-types     = 公共类型必须放在 src/types/ 目录下，禁止在组件内定义

[Security]
# 安全规则
xss.primevue      = 使用 PrimeVue 富文本组件时必须配置 :sanitize="false" 并手动调用 DOMPurify
v-html            = 使用 v-html 的组件必须添加 @vite-ignore 注释并提交安全审查

[Automation]
# 自动化工具
eslint.config = 必须包含以下规则：
  - @typescript-eslint/no-explicit-any: error
  - vue/require-prop-types: error
prettier.config = 必须与 .vscode/settings.json 中的格式化配置一致
commit.rule     = Commit 消息格式：[scope] description，scope 包含 [prime] 或 [vite] 时触发专项检查

[CI/CD]
# 流水线规则
ci.steps = GitHub Actions 必须包含以下步骤：
  1. npm run type-check
  2. npm run build -- --mode=production
  3. npx primevue-optimize-analyzer --bundle
  4. npm audit --audit-level=moderate

# -------------------------------
# 规则执行策略
# -------------------------------
[Enforcement]
stage = pre-commit,ci    # 在 git commit 前和 CI 阶段生效
blocking = true          # 违规直接阻断流程
error-level = strict      # 所有规则均为 error 级别
