"""Health check router."""
from fastapi import APIRouter
from datetime import datetime
from typing import Any, Dict
import logging
from fastapi import HTTPException

# 记录服务启动时间
SERVER_START_TIME = datetime.now()
router = APIRouter(
    tags=["health"]  # 移除prefix，让main.py处理前缀
)


@router.get("")  # 移除 /health，让主路由处理前缀
async def health_check() -> Dict[str, Any]:
    """健康检查端点，返回服务器状态和启动时间"""
    try:
        uptime = (datetime.now() - SERVER_START_TIME).total_seconds()
        return {
            "status": "ok",
            "start_time": SERVER_START_TIME.isoformat(),
            "uptime_seconds": uptime
        }
    except Exception as e:
        logging.error(f"Health check failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error during health check")
