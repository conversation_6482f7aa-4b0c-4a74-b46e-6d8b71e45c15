import { createRouter, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import ApiDocList from '../views/ApiDocList.vue'
import ApiDocView from '../views/ApiDocView.vue'
import Login from '../views/Login.vue'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'home',
    component: ApiDocList
  },
  {
    path: '/docs/:id',
    name: 'doc-view',
    component: ApiDocView,
    props: (route) => ({
      id: route.params.id as string
    })
  },
  {
    path: '/about',
    name: 'about',
    component: () => import('../views/AboutView.vue')
  },
  {
    path: '/login',
    name: 'login',
    component: Login
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router