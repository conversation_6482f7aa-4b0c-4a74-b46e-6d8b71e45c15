import { createRouter, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import ApiDocList from '../views/ApiDocList.vue'
import ApiDocView from '../views/ApiDocView.vue'
import Login from '../views/Login.vue'
import AuthCallback from '../views/AuthCallback.vue'
import { useAuthStore } from '@/stores/authStore'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'home',
    component: ApiDocList
  },
  {
    path: '/docs/:id',
    name: 'doc-view',
    component: ApiDocView,
    props: (route) => ({
      id: route.params.id as string
    })
  },
  {
    path: '/about',
    name: 'about',
    component: () => import('../views/AboutView.vue')
  },
  {
    path: '/login',
    name: 'login',
    component: Login
  },
  {
    path: '/auth/callback',
    name: 'auth-callback',
    component: AuthCallback
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  // 不需要认证的路由
  const publicRoutes = ['login', 'auth-callback']

  if (publicRoutes.includes(to.name as string)) {
    next()
    return
  }

  // 检查是否已登录
  if (!authStore.isLoggedIn) {
    next({
      name: 'login',
      query: { redirect: to.fullPath }
    })
    return
  }

  next()
})

export default router