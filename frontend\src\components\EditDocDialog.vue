<template>
  <Dialog
    :visible="visible"
    @update:visible="$emit('update:visible', $event)"
    :modal="true"
    :closable="true"
    :style="{ width: '500px' }"
    header="编辑文档"
    @keydown.esc="closeDialog"
    @keydown.ctrl.enter="handleSubmit"
  >
    <form @submit.prevent="handleSubmit" class="flex flex-column gap-3" ref="formRef">
      <!-- URL输入框 -->
      <EnhancedInput
        id="url"
        v-model="form.url"
        label="URL"
        type="url"
        :invalid="v$.url.$invalid"
        :dirty="v$.url.$dirty"
        :error-message="urlErrorMessage"
        :shortcuts="urlShortcuts"
        placeholder="https://example.com"
        @blur="validateUrl"
        @input="v$.url.$touch()"
        @paste="handleUrlPaste"
        @enter="focusNext"
        @tab="handleUrlTab"
        ref="urlInput"
        autofocus
      />

      <!-- 标题输入框 -->
      <EnhancedInput
        id="title"
        v-model="form.title"
        label="标题"
        :invalid="v$.title.$invalid"
        :dirty="v$.title.$dirty"
        :error-message="titleErrorMessage"
        :shortcuts="titleShortcuts"
        placeholder="文档标题"
        @blur="v$.title.$touch()"
        @enter="focusNext"
        ref="titleInput"
      />

      <!-- 描述输入框 -->
      <EnhancedInput
        id="description"
        v-model="form.description"
        label="描述（可选）"
        type="textarea"
        :shortcuts="descriptionShortcuts"
        placeholder="输入文档描述（可选）"
        auto-resize
        ref="descriptionInput"
      />
    </form>

    <template #footer>
      <div class="flex justify-content-end gap-2">
        <Button
          label="取消"
          icon="pi pi-times"
          @click="closeDialog"
          class="p-button-text"
          :disabled="isSubmitting"
        />
        <Button
          label="保存"
          icon="pi pi-check"
          @click="handleSubmit"
          :loading="isSubmitting"
          :disabled="v$.$invalid"
        />
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useVuelidate } from '@vuelidate/core'
import { required, url } from '@vuelidate/validators'
import type { ApiDoc } from '@/types'
import EnhancedInput from '@/components/common/EnhancedInput.vue'

const props = defineProps<{
  visible: boolean
  doc?: ApiDoc | null
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  save: [doc: ApiDoc]
}>()

const isSubmitting = ref(false)
const formRef = ref<HTMLFormElement>()
const urlInput = ref<InstanceType<typeof EnhancedInput>>()
const titleInput = ref<InstanceType<typeof EnhancedInput>>()
const descriptionInput = ref<InstanceType<typeof EnhancedInput>>()

// 表单数据
const form = ref({
  url: '',
  title: '',
  description: ''
})

// 当文档数据变化时更新表单
watch(
  () => props.doc,
  (newDoc) => {
    if (newDoc) {
      form.value = {
        url: newDoc.url,
        title: newDoc.title,
        description: newDoc.description || ''
      }
      v$.value.$reset()
    }
  },
  { immediate: true }
)

// 当对话框显示状态变化时更新表单
watch(() => props.visible, (newValue) => {
  if (newValue && props.doc) {
    // 当对话框打开时，使用当前文档数据
    form.value = {
      url: props.doc.url,
      title: props.doc.title,
      description: props.doc.description || ''
    }
    v$.value.$reset()
  } else if (!newValue) {
    // 当对话框关闭时重置表单
    closeDialog()
  }
})

// 验证规则
const rules = computed(() => ({
  url: { 
    required,
    url: (value: string) => {
      try {
        const urlObj = new URL(value)
        return urlObj.protocol === 'http:' || urlObj.protocol === 'https:'
      } catch {
        return false
      }
    }
  },
  title: { required }
}))

const v$ = useVuelidate(rules, form)

// 错误信息
const urlErrorMessage = computed(() => {
  if (v$.value.url.$dirty) {
    if (v$.value.url.required.$invalid) return '请输入URL'
    if (v$.value.url.url.$invalid) return '请输入有效的URL地址（必须包含 http:// 或 https://）'
  }
  return ''
})

const titleErrorMessage = computed(() => {
  if (v$.value.title.$dirty && v$.value.title.required.$invalid) {
    return '请输入标题'
  }
  return ''
})

// URL快捷键
const urlShortcuts = computed(() => [
  {
    key: 'Ctrl + V',
    description: '粘贴并自动清理URL'
  },
  {
    key: 'Tab',
    description: '自动补全协议'
  },
  {
    key: 'Enter',
    description: '移动到下一个输入框'
  }
])

// 标题快捷键
const titleShortcuts = computed(() => [
  {
    key: 'Enter',
    description: '移动到下一个输入框'
  },
  {
    key: 'Alt + S',
    description: '从URL中提取标题',
    handler: (event: KeyboardEvent) => {
      if (event.altKey && event.key.toLowerCase() === 's') {
        event.preventDefault()
        extractTitleFromUrl()
      }
    }
  }
])

// 描述快捷键
const descriptionShortcuts = computed(() => [
  {
    key: 'Ctrl + Enter',
    description: '保存文档',
    handler: (event: KeyboardEvent) => {
      if (event.ctrlKey && event.key === 'Enter') {
        event.preventDefault()
        handleSubmit()
      }
    }
  },
  {
    key: 'Alt + N',
    description: '清空内容',
    handler: (event: KeyboardEvent) => {
      if (event.altKey && event.key.toLowerCase() === 'n') {
        event.preventDefault()
        form.value.description = ''
      }
    }
  }
])

// 处理URL粘贴
const handleUrlPaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const pastedText = event.clipboardData?.getData('text')
  if (!pastedText) return

  // 清理URL
  let cleanUrl = pastedText.trim()
  try {
    const url = new URL(cleanUrl)
    form.value.url = url.toString()
  } catch {
    // 如果不是有效的URL，尝试添加协议
    if (!cleanUrl.startsWith('http://') && !cleanUrl.startsWith('https://')) {
      cleanUrl = 'https://' + cleanUrl
    }
    form.value.url = cleanUrl
  }
}

// 处理URL Tab键
const handleUrlTab = (event: KeyboardEvent) => {
  if (!form.value.url) return

  if (!form.value.url.startsWith('http://') && !form.value.url.startsWith('https://')) {
    event.preventDefault()
    form.value.url = 'https://' + form.value.url
    titleInput.value?.$el.querySelector('input')?.focus()
  }
}

// 从URL中提取标题
const extractTitleFromUrl = () => {
  try {
    const urlObj = new URL(form.value.url)
    const hostname = urlObj.hostname
    // 移除常见的域名后缀
    const domainParts = hostname.split('.')
    if (domainParts.length >= 2) {
      const name = domainParts[domainParts.length - 2]
      form.value.title = name.charAt(0).toUpperCase() + name.slice(1)
    }
  } catch {
    // 忽略错误
  }
}

// URL验证
const validateUrl = async () => {
  await v$.value.url.$validate()
}

// 焦点移动到下一个输入框
const focusNext = (event: KeyboardEvent) => {
  const target = event.target as HTMLElement
  const inputs = formRef.value?.querySelectorAll('input, textarea')
  if (!inputs) return

  const currentIndex = Array.from(inputs).indexOf(target)
  if (currentIndex < inputs.length - 1) {
    (inputs[currentIndex + 1] as HTMLElement).focus()
  }
}

// 提交表单
const handleSubmit = async () => {
  const isValid = await v$.value.$validate()
  if (!isValid || !props.doc) return

  isSubmitting.value = true
  try {
    const updatedDoc: ApiDoc = {
      ...props.doc,
      url: form.value.url.trim(),
      title: form.value.title.trim(),
      description: form.value.description?.trim() || ''
    }
    emit('save', updatedDoc)
    emit('update:visible', false)
  } finally {
    isSubmitting.value = false
  }
}

// 关闭对话框
const closeDialog = () => {
  form.value = {
    url: '',
    title: '',
    description: ''
  }
  v$.value.$reset()
  emit('update:visible', false)
}
</script>

<style scoped>
:deep(.p-dialog-content) {
  padding-bottom: 0;
}
</style>
