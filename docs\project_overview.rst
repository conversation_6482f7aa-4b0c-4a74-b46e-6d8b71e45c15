项目概述
========

简介
----
基于 FastAPI 和 Vue 3 构建的现代化 API 文档管理系统。提供直观的界面来管理和查看 API 文档，支持实时更新和搜索功能。

技术栈
------

后端技术
~~~~~~~
* FastAPI (Python Web 框架)
* PostgreSQL (数据库)
* SQLAlchemy (ORM)
* Alembic (数据库迁移)
* Python 3.8+
* Pydantic (数据验证)

前端技术
~~~~~~~
* Vue 3 (前端框架)
* Vite (构建工具)
* TypeScript (类型支持)
* PrimeVue (UI组件库)
* Axios (HTTP请求)
* Vue Router (路由管理)

项目特性
-------

后端功能
~~~~~~~
* RESTful API 设计
* 自动生成 OpenAPI/Swagger 文档
* 数据库迁移和版本控制
* CORS 跨域支持
* 错误处理和日志记录

前端功能
~~~~~~~
* 现代响应式设计
* 实时数据更新 (30秒自动刷新)
* 搜索和过滤功能
* 优雅的空状态和加载状态
* 完整的 CRUD 操作界面
* TypeScript 类型安全

项目结构
-------

.. code-block:: text

   api_docs_server/
   ├── api_docs_server/        # 后端代码
   │   ├── models/           # 数据模型
   │   ├── routes/           # API路由
   │   ├── schemas/          # Pydantic模型
   │   └── main.py          # FastAPI应用入口
   ├── frontend/             # 前端代码
   │   ├── src/
   │   │   ├── api/        # API 接口
   │   │   ├── components/ # Vue组件
   │   │   ├── config/     # 配置文件
   │   │   ├── hooks/      # Vue Hooks
   │   │   ├── pages/      # 页面组件
   │   │   └── types/      # TypeScript类型
   │   ├── index.html
   │   └── package.json
   ├── alembic/             # 数据库迁移
   ├── api_docs_server.yaml.tmpl  # K8s部署模板
   └── requirements.txt     # Python依赖

部署环境
-------

生产环境特性
~~~~~~~~~~
* Ingress 配置支持 HTTPS
* 前后端分离部署
* 自动 SSL 证书管理
* 健康检查和自动恢复
* 域名: https://api-docs.woa.com
