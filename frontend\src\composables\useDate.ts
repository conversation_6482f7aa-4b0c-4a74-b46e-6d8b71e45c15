export function useDate() {
  /**
   * 格式化日期
   * @param date ISO日期字符串或Date对象
   * @returns 格式化后的日期字符串，格式：YYYY-MM-DD HH:mm
   */
  function formatDate(date: string | Date): string {
    if (!date) return ''
    const d = new Date(date)
    if (isNaN(d.getTime())) return ''

    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}`
  }

  /**
   * 获取相对时间描述
   * @param date ISO日期字符串或Date对象
   * @returns 相对时间描述，如：刚刚、5分钟前、1小时前等
   */
  function getRelativeTime(date: string | Date): string {
    if (!date) return ''
    const d = new Date(date)
    if (isNaN(d.getTime())) return ''

    const now = new Date()
    const diff = now.getTime() - d.getTime()
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)

    if (minutes < 1) return '刚刚'
    if (minutes < 60) return `${minutes}分钟前`
    if (hours < 24) return `${hours}小时前`
    if (days < 30) return `${days}天前`
    
    return formatDate(date)
  }

  return {
    formatDate,
    getRelativeTime
  }
}
