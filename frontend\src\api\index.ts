import axios, { AxiosInstance } from 'axios';
import type { ApiDoc, ApiDocCreate, ApiDocUpdate } from '../types';

// API 路由配置
export const apiRoutes = {
    docs: {
        list: '/api/docs',
        get: (id: string) => `/api/docs/${id}`,
        create: '/api/docs',
        update: (id: string) => `/api/docs/${id}`,
        delete: (id: string) => `/api/docs/${id}`,
        refresh: (id: string) => `/api/docs/${id}/refresh`,
        view: (id: string) => `/api/docs/${id}/view`,
    },
    health: {
        check: '/api/health'
    }
};

// 创建 axios 实例
const api: AxiosInstance = axios.create({
    baseURL: '/api',  // 使用相对路径，让Vite代理处理
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    },
    withCredentials: true,
});

// 请求拦截器
api.interceptors.request.use(
    (config) => {
        // 在这里可以添加认证信息等
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// 响应拦截器
api.interceptors.response.use(
    (response) => {
        return response;
    },
    (error) => {
        if (error.response) {
            const status = error.response.status;
            const data = error.response.data as any;

            switch (status) {
                case 400:
                    throw new Error(data.detail || 'Bad Request');
                case 401:
                    throw new Error(data.detail || 'Unauthorized');
                case 403:
                    throw new Error(data.detail || 'Forbidden');
                case 404:
                    throw new Error(data.detail || 'Not Found');
                case 500:
                    throw new Error(data.detail || 'Internal Server Error');
                default:
                    throw new Error(data.detail || 'An error occurred');
            }
        }
        throw error;
    }
);

// API 服务
export const apiService = {
    docs: {
        list: async () => {
            const response = await api.get<ApiDoc[]>(apiRoutes.docs.list);
            return response.data;
        },
        get: async (id: string) => {
            const response = await api.get<ApiDoc>(apiRoutes.docs.get(id));
            return response.data;
        },
        create: async (data: ApiDocCreate) => {
            const response = await api.post<ApiDoc>(apiRoutes.docs.create, data);
            return response.data;
        },
        update: async (id: string, data: ApiDocUpdate) => {
            const response = await api.put<ApiDoc>(apiRoutes.docs.update(id), data);
            return response.data;
        },
        delete: async (id: string) => {
            await api.delete(apiRoutes.docs.delete(id));
        },
        refresh: async (id: string) => {
            const response = await api.post<ApiDoc>(apiRoutes.docs.refresh(id));
            return response.data;
        },
        incrementView: async (id: string) => {
            const response = await api.post<ApiDoc>(apiRoutes.docs.view(id));
            return response.data;
        }
    },
    health: {
        check: async () => {
            const response = await api.get(apiRoutes.health.check);
            return response.data;
        },
    },
};

import { ApiDocService, HealthService } from './apiService';
import axiosInstance, { API_ENDPOINTS } from './axios';

export {
    ApiDocService,
    HealthService,
    axiosInstance,
    API_ENDPOINTS,
    type ApiDoc,
    type ApiDocCreate,
    type ApiDocUpdate,
};
