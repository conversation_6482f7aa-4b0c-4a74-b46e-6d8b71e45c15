"""API Documentation routes."""
import logging
import traceback
from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from api_docs_server.database import get_db
from api_docs_server.models.api_doc import ApiDoc, ApiDocCreate, ApiDocUpdate, ApiDocResponse
from api_docs_server.services.metadata_fetcher import MetadataFetcher

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(tags=["docs"])

@router.post("", response_model=ApiDocResponse)
async def create_api_doc(api_doc: ApiDocCreate, db: AsyncSession = Depends(get_db)) -> ApiDocResponse:
    """创建API文档."""
    try:
        # 创建新文档
        db_api_doc = ApiDoc(
            url=str(api_doc.url),
            title=api_doc.title,
            description=api_doc.description,
            icon_url=str(api_doc.icon_url) if api_doc.icon_url else None,
            icon_data=api_doc.icon_data,
            auto_metadata=api_doc.auto_metadata
        )
        db.add(db_api_doc)
        await db.commit()
        await db.refresh(db_api_doc)

        # 如果启用了自动元数据，则获取元数据
        if db_api_doc.auto_metadata:
            metadata_fetcher = MetadataFetcher()
            metadata = await metadata_fetcher.fetch_metadata(str(api_doc.url))
            if metadata:
                db_api_doc.title = metadata.get("title", db_api_doc.title)
                db_api_doc.description = metadata.get("description", db_api_doc.description)
                db_api_doc.icon_url = metadata.get("icon_url", db_api_doc.icon_url)
                db_api_doc.icon_data = metadata.get("icon_data", db_api_doc.icon_data)
                await db.commit()
                await db.refresh(db_api_doc)

        logger.info(f"成功创建文档: {db_api_doc.id}")
        return ApiDocResponse.model_validate(db_api_doc)
    except Exception as e:
        error_msg = f"创建API文档失败: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)

@router.get("/{api_doc_id}", response_model=ApiDocResponse)
async def get_api_doc(api_doc_id: UUID, db: AsyncSession = Depends(get_db)) -> ApiDocResponse:
    """获取API文档."""
    try:
        result = await db.execute(select(ApiDoc).where(ApiDoc.id == api_doc_id))
        db_api_doc = result.scalar_one_or_none()
        if db_api_doc is None:
            raise HTTPException(status_code=404, detail="文档不存在")
        logger.info(f"成功获取文档: {api_doc_id}")
        return ApiDocResponse.model_validate(db_api_doc)
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"获取API文档失败: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)

@router.get("", response_model=List[ApiDocResponse])
async def list_api_docs(db: AsyncSession = Depends(get_db)) -> List[ApiDocResponse]:
    """获取API文档列表."""
    try:
        result = await db.execute(select(ApiDoc))
        docs = result.scalars().all()
        logger.info(f"成功获取 {len(docs)} 个文档")
        return [ApiDocResponse.model_validate(doc) for doc in docs]
    except Exception as e:
        error_msg = f"获取API文档列表失败: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)

@router.put("/{api_doc_id}", response_model=ApiDocResponse)
async def update_api_doc(
    api_doc_id: UUID,
    api_doc_update: ApiDocUpdate,
    db: AsyncSession = Depends(get_db)
) -> ApiDocResponse:
    """更新API文档."""
    try:
        result = await db.execute(select(ApiDoc).where(ApiDoc.id == api_doc_id))
        db_api_doc = result.scalar_one_or_none()
        if db_api_doc is None:
            raise HTTPException(status_code=404, detail="文档不存在")

        # 更新字段
        if api_doc_update.url is not None:
            db_api_doc.url = str(api_doc_update.url)
        if api_doc_update.title is not None:
            db_api_doc.title = api_doc_update.title
        if api_doc_update.description is not None:
            db_api_doc.description = api_doc_update.description
        if api_doc_update.icon_url is not None:
            db_api_doc.icon_url = str(api_doc_update.icon_url)
        if api_doc_update.icon_data is not None:
            db_api_doc.icon_data = api_doc_update.icon_data
        if api_doc_update.auto_metadata is not None:
            db_api_doc.auto_metadata = api_doc_update.auto_metadata

        await db.commit()
        await db.refresh(db_api_doc)
        logger.info(f"成功更新文档: {api_doc_id}")
        return ApiDocResponse.model_validate(db_api_doc)
    except Exception as e:
        error_msg = f"更新API文档失败: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)

@router.delete("/{api_doc_id}")
async def delete_api_doc(api_doc_id: UUID, db: AsyncSession = Depends(get_db)):
    """删除API文档."""
    try:
        result = await db.execute(select(ApiDoc).where(ApiDoc.id == api_doc_id))
        db_api_doc = result.scalar_one_or_none()
        if db_api_doc is None:
            raise HTTPException(status_code=404, detail="文档不存在")

        await db.delete(db_api_doc)
        await db.commit()
        logger.info(f"成功删除文档: {api_doc_id}")
        return {"message": "文档已删除"}
    except Exception as e:
        error_msg = f"删除API文档失败: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)

@router.post("/{api_doc_id}/refresh", response_model=ApiDocResponse)
async def refresh_metadata(api_doc_id: UUID, db: AsyncSession = Depends(get_db)) -> ApiDocResponse:
    """刷新API文档元数据."""
    try:
        result = await db.execute(select(ApiDoc).where(ApiDoc.id == api_doc_id))
        db_api_doc = result.scalar_one_or_none()
        if db_api_doc is None:
            raise HTTPException(status_code=404, detail="文档不存在")

        # 获取元数据
        metadata_fetcher = MetadataFetcher()
        metadata = await metadata_fetcher.fetch_metadata(db_api_doc.url)
        if metadata:
            db_api_doc.title = metadata.get("title", db_api_doc.title)
            db_api_doc.description = metadata.get("description", db_api_doc.description)
            db_api_doc.icon_url = metadata.get("icon_url", db_api_doc.icon_url)
            db_api_doc.icon_data = metadata.get("icon_data", db_api_doc.icon_data)
            await db.commit()
            await db.refresh(db_api_doc)

        logger.info(f"成功刷新文档元数据: {api_doc_id}")
        return ApiDocResponse.model_validate(db_api_doc)
    except Exception as e:
        error_msg = f"刷新API文档元数据失败: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)

@router.post("/{doc_id}/view", response_model=ApiDocResponse)
async def increment_view_count(
    doc_id: UUID,
    db: AsyncSession = Depends(get_db)
) -> ApiDocResponse:
    """增加文档的访问计数。"""
    try:
        result = await db.execute(select(ApiDoc).where(ApiDoc.id == doc_id))
        db_api_doc = result.scalar_one_or_none()
        if db_api_doc is None:
            raise HTTPException(status_code=404, detail="文档不存在")

        # 增加访问计数
        db_api_doc.view_count += 1
        await db.commit()
        await db.refresh(db_api_doc)

        logger.info(f"成功增加文档访问计数: {doc_id}")
        return ApiDocResponse.model_validate(db_api_doc)
    except Exception as e:
        error_msg = f"增加访问计数失败: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)
