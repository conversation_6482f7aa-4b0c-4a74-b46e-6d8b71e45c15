<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>太湖登录功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 50px auto;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.success {
            background: #28a745;
        }
        .button.success:hover {
            background: #1e7e34;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.running {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .config {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-line;
        }
    </style>
</head>
<body>
    <h1>🎉 太湖登录功能测试页面</h1>
    
    <div class="container">
        <h2>📊 服务状态</h2>
        <div class="status running">
            ✅ 后端服务器: http://localhost:8001 (运行中)
        </div>
        <div class="status running">
            ✅ 前端服务器: http://localhost:3005 (运行中)
        </div>
    </div>

    <div class="container">
        <h2>🔐 太湖登录测试</h2>
        <p>点击下面的按钮测试不同环境的太湖登录功能：</p>
        
        <h3>前端登录页面</h3>
        <a href="http://localhost:3005/login" class="button" target="_blank">
            🌐 前端登录页面
        </a>
        <p>包含测试环境和生产环境两个登录按钮</p>

        <h3>后端API直接测试</h3>
        <a href="http://localhost:8001/auth/taihu" class="button" target="_blank">
            🧪 太湖测试环境登录
        </a>
        <a href="http://localhost:8001/auth/taihu-production" class="button success" target="_blank">
            🏭 太湖生产环境登录
        </a>
        
        <h3>其他端点</h3>
        <a href="http://localhost:8001" class="button" target="_blank">
            📋 API根路径
        </a>
        <a href="http://localhost:8001/api/health" class="button" target="_blank">
            ❤️ 健康检查
        </a>
    </div>

    <div class="container">
        <h2>⚙️ 配置信息</h2>
        
        <h3>测试环境配置</h3>
        <div class="config">认证服务器: https://test-odc.it.woa.com/api/auth-center/oauth2
Client ID: test-app
Client Secret: ilX3uqWyJRbK
回调地址: http://127.0.0.1:8001/auth/tencent/callback</div>

        <h3>生产环境配置</h3>
        <div class="config">认证服务器: https://tai.it.tencent.com/api/auth-center/oauth2
Client ID: api-docs
Client Secret: WNEK28UBJHHMN0D81GMPBHAWWZ4GEL2U
回调地址: https://api-docs.woa.com/auth/tencent/callback</div>
    </div>

    <div class="container">
        <h2>🔄 登录流程</h2>
        <ol>
            <li><strong>用户选择环境</strong> → 在前端页面选择测试或生产环境</li>
            <li><strong>跳转到后端</strong> → 前端跳转到对应的后端登录端点</li>
            <li><strong>重定向到太湖</strong> → 后端构建OAuth URL并重定向到太湖认证服务器</li>
            <li><strong>用户登录</strong> → 在太湖页面完成身份验证</li>
            <li><strong>回调处理</strong> → 太湖重定向到后端回调地址</li>
            <li><strong>Token交换</strong> → 后端用授权码换取访问令牌</li>
            <li><strong>获取用户信息</strong> → 后端调用用户信息接口</li>
            <li><strong>前端重定向</strong> → 后端重定向到前端，携带用户信息</li>
            <li><strong>登录完成</strong> → 前端保存用户状态</li>
        </ol>
    </div>

    <div class="container">
        <h2>📁 实现的功能</h2>
        <ul>
            <li>✅ 支持太湖测试环境和生产环境</li>
            <li>✅ 标准OIDC授权码流程</li>
            <li>✅ 自动环境检测和配置</li>
            <li>✅ 完整的错误处理</li>
            <li>✅ 用户信息获取</li>
            <li>✅ 前后端分离架构</li>
            <li>✅ CORS跨域支持</li>
            <li>✅ Session管理</li>
        </ul>
    </div>

    <div class="container">
        <h2>🚨 重要提醒</h2>
        <div class="status error">
            ⚠️ 生产环境需要联系 <strong>v_zhixqiu</strong> 注册回调地址：
            <br>• 测试环境：http://127.0.0.1:8001/auth/tencent/callback
            <br>• 生产环境：https://api-docs.woa.com/auth/tencent/callback
        </div>
    </div>

    <script>
        // 检查服务器状态
        async function checkServerStatus() {
            try {
                const response = await fetch('http://localhost:8001/api/health');
                if (response.ok) {
                    console.log('✅ 后端服务器运行正常');
                } else {
                    console.log('❌ 后端服务器响应异常');
                }
            } catch (error) {
                console.log('❌ 无法连接到后端服务器');
            }
        }

        // 页面加载时检查状态
        window.onload = function() {
            checkServerStatus();
            console.log('🎉 太湖登录功能测试页面已加载');
            console.log('📝 请查看上方的测试按钮和配置信息');
        };
    </script>
</body>
</html>
