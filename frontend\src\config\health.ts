// Health check configuration
const isDevelopment = import.meta.env.DEV;
const apiPort = import.meta.env.VITE_API_PORT;
const frontendPort = import.meta.env.VITE_FRONTEND_PORT;
const apiHost = import.meta.env.VITE_API_HOST;
const frontendHost = import.meta.env.VITE_FRONTEND_HOST;

// 构建基础 URL
const FRONTEND_BASE_URL = isDevelopment
    ? `http://${frontendHost}:${frontendPort}`
    : '';

import { API_BASE_URL } from './env'

// 健康检查配置
export const HEALTH_CONFIG = {
    endpoint: `${API_BASE_URL}/backend/api/health`,
    interval: 30000,  // 30秒
    timeout: 5000,    // 5秒超时
    retries: 3        // 重试次数
} as const

// 健康状态类型
export type HealthStatus = {
    status: 'healthy' | 'unhealthy'
    timestamp: string
    details?: {
        [key: string]: {
            status: 'up' | 'down'
            message?: string
        }
    }
}

// API配置
export const API_CONFIG = {
    baseUrl: API_BASE_URL,
    timeout: 30000,
    retries: 3,
}
