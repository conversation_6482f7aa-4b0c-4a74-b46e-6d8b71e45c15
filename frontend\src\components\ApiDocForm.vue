<template>
  <form @submit.prevent="handleSubmit" class="form-container">
    <div class="field">
      <label for="title" class="form-label">标题</label>
      <InputText
        id="title"
        v-model="form.title"
        class="w-full"
        :class="{ 'p-invalid': errors.title }"
        placeholder="输入文档标题"
        @blur="validateField('title')"
      />
      <small v-if="errors.title" class="p-error">{{ errors.title }}</small>
    </div>

    <div class="field">
      <label for="url" class="form-label">文档 URL</label>
      <div class="p-inputgroup">
        <InputText
          id="url"
          v-model="form.url"
          class="w-full"
          :class="{ 'p-invalid': errors.url }"
          placeholder="https://example.com/api-docs"
          @blur="validateField('url')"
        />
        <Button
          icon="pi pi-link"
          severity="secondary"
          outlined
          type="button"
          @click="validateUrl"
        />
      </div>
      <small v-if="errors.url" class="p-error">{{ errors.url }}</small>
    </div>

    <div class="field">
      <label for="description" class="form-label">描述</label>
      <Textarea
        id="description"
        v-model="form.description"
        class="w-full"
        rows="3"
        placeholder="输入文档描述（可选）"
      />
    </div>

    <div class="form-footer">
      <Button
        type="button"
        label="取消"
        icon="pi pi-times"
        text
        @click="handleCancel"
      />
      <Button
        type="submit"
        label="保存"
        icon="pi pi-check"
        :loading="loading"
      />
    </div>
  </form>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { ApiDoc } from '@/types/api'
import { useToast } from 'primevue/usetoast'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import Button from 'primevue/button'
import { ApiDocService } from '@/api/apiService'

const toast = useToast()

interface Props {
  modelValue: boolean
  doc: ApiDoc | null
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  doc: null
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'save': [doc: ApiDoc]
  'cancel': []
}>()

const form = ref({
  title: '',
  url: '',
  description: ''
})

const loading = ref(false)
const errors = ref<Record<string, string>>({})

// 监听 doc 变化，更新表单
watch(() => props.doc, (newDoc) => {
  if (newDoc) {
    form.value = {
      title: newDoc.title || '',
      url: newDoc.url || '',
      description: newDoc.description || ''
    }
  } else {
    form.value = {
      title: '',
      url: '',
      description: ''
    }
  }
}, { immediate: true })

// 保存文档
async function handleSubmit() {
  if (!validateForm()) return;

  try {
    let savedDoc: ApiDoc;
    if (props.doc) {
      savedDoc = await ApiDocService.updateDoc(props.doc.id, form.value);
    } else {
      savedDoc = await ApiDocService.createDoc(form.value);
    }

    emit('save', savedDoc);
    emit('update:modelValue', false);
    toast.add({
      severity: 'success',
      summary: props.doc ? '更新成功' : '创建成功',
      detail: `文档 "${savedDoc.title}" 已${props.doc ? '更新' : '创建'}`,
      life: 3000,
    });
  } catch (error) {
    console.error('Error saving document:', error);
    toast.add({
      severity: 'error',
      summary: props.doc ? '更新失败' : '创建失败',
      detail: `${props.doc ? '更新' : '创建'}文档时发生错误，请重试`,
      life: 3000,
    });
  }
}

// 取消编辑
function handleCancel() {
  emit('cancel')
  emit('update:modelValue', false)
}

// 验证 URL
async function validateUrl() {
  if (!form.value.url) {
    return
  }

  try {
    loading.value = true
    const result = await ApiDocService.validateUrl(form.value.url)
    if (result.is_valid) {
      toast.add({
        severity: 'success',
        summary: 'URL 有效',
        detail: '链接可以访问',
        life: 3000
      })
    }
  } catch (error: any) {
    toast.add({
      severity: 'error',
      summary: 'URL 无效',
      detail: error.message || '链接无法访问',
      life: 5000
    })
  } finally {
    loading.value = false
  }
}

// 验证 URL 格式
function isValidUrl(string: string): boolean {
  try {
    new URL(string)
    return true
  } catch (_) {
    return false
  }
}

// 验证单个字段
function validateField(field: string): void {
  errors.value[field] = ''

  switch (field) {
    case 'title':
      if (!form.value.title?.trim()) {
        errors.value.title = '标题不能为空'
      } else if (form.value.title.length > 100) {
        errors.value.title = '标题不能超过100个字符'
      }
      break
    case 'url':
      if (!form.value.url?.trim()) {
        errors.value.url = 'URL不能为空'
      } else if (!isValidUrl(form.value.url)) {
        errors.value.url = '请输入有效的URL'
      }
      break
  }
}

// 验证所有字段
function validateForm(): boolean {
  validateField('title')
  validateField('url')
  return !Object.values(errors.value).some(Boolean)
}
</script>

<style scoped>
.form-container {
  padding: 1.5rem;
}

.field {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.p-inputgroup {
  display: flex;
  align-items: center;
}

.p-inputgroup .p-button {
  margin-left: 0.5rem;
}

.form-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.p-error {
  color: var(--red-500);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

:deep(.p-invalid) {
  border-color: var(--red-500) !important;
}

:deep(.p-inputtext),
:deep(.p-textarea) {
  width: 100%;
}
</style>
