"""API Documentation Server."""
import logging

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from api_docs_server.routers import docs
from api_docs_server.routers import healthcheck
import os
from dotenv import load_dotenv

load_dotenv()  # 加载环境变量

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="API Documentation Service",
    description="A service for managing API documentation.",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("ALLOWED_ORIGINS", "").split(","),  # 允许的源列表
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(docs.router, prefix="/api/docs")  # 添加 /api 前缀
app.include_router(healthcheck.router, prefix="/api/health")

@app.on_event("startup")
async def startup_event():
    """应用程序启动时执行."""
    logger.info("Starting up API Documentation Server...")

@app.on_event("shutdown")
async def shutdown_event():
    """应用程序关闭时执行."""
    logger.info("Shutting down API Documentation Server...")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
