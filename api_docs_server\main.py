"""API Documentation Server."""
import logging
import os
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.sessions import SessionMiddleware

# 导入路由
from api_docs_server.routers import docs, healthcheck, auth, taihu_auth

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="API Documentation Service",
    description="A service for managing API documentation.",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3005", "http://localhost:3000", "https://api-docs.woa.com"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加Session中间件
app.add_middleware(
    SessionMiddleware,
    secret_key=os.getenv("SESSION_SECRET_KEY", "keyboard cat"),
    max_age=3600
)

# 注册路由
app.include_router(docs.router, prefix="/api/docs")
app.include_router(healthcheck.router, prefix="/api/health")
app.include_router(auth.router, prefix="/api/auth")
app.include_router(taihu_auth.router, prefix="/auth")

@app.on_event("startup")
async def startup_event():
    """应用程序启动时执行."""
    logger.info("Starting up API Documentation Server...")

@app.on_event("shutdown")
async def shutdown_event():
    """应用程序关闭时执行."""
    logger.info("Shutting down API Documentation Server...")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
