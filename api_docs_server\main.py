"""API Documentation Server."""
import logging

from fastapi import FastAPI, HTTPException
from starlette.middleware.sessions import SessionMiddleware

# 导入auth路由
from api_docs_server.routers import auth

app = FastAPI(
    title="API Documentation Service",
    description="A service for managing API documentation.",
    version="1.0.0"
)

# 添加Session中间件
app.add_middleware(
    SessionMiddleware,
    secret_key=os.getenv("SESSION_SECRET_KEY", "keyboard cat"),  # 使用环境变量配置secret key
    max_age=3600  # Session有效期（秒）
)

# 注册路由
app.include_router(docs.router, prefix="/api/docs")  # 添加 /api 前缀
app.include_router(healthcheck.router, prefix="/api/health")

# 导入auth路由
from api_docs_server.routers import auth
app.include_router(auth.router, prefix="/api/auth")  # 添加auth路由

@app.on_event("startup")
async def startup_event():
    """应用程序启动时执行."""
    logger.info("Starting up API Documentation Server...")

@app.on_event("shutdown")
async def shutdown_event():
    """应用程序关闭时执行."""
    logger.info("Shutting down API Documentation Server...")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
