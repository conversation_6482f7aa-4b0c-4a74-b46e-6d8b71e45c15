<template>
  <div class="api-doc-list">
    <!-- 搜索框 -->
    <div class="search-container mb-4">
      <span class="p-input-icon-left w-full">
        <i class="pi pi-search" />
        <InputText 
          v-model="searchQuery" 
          placeholder="搜索 API 文档..." 
          class="w-full"
          @input="handleSearch"
        />
      </span>
    </div>

    <!-- 文档列表 -->
    <div v-if="heartbeatService.isServerOnline.value && localData.length > 0" class="grid">
      <div v-for="doc in localData" :key="doc.id" class="col-12 md:col-6 lg:col-4 xl:col-3">
        <ApiDocCard :doc="doc" @click="handleDocClick(doc)" />
      </div>
    </div>

    <!-- 空状态 -->
    <EmptyState 
      v-else-if="!loading && (!heartbeatService.isServerOnline.value || localData.length === 0)"
      icon="pi pi-file-o"
      title="暂无文档"
      description="请检查网络连接或稍后再试"
    />

    <!-- 加载状态 -->
    <LoadingOverlay 
      :show="loading" 
      message="正在加载文档..."
      transparent
    />

    <!-- 连接状态 -->
    <div 
      v-if="!heartbeatService.isServerOnline.value" 
      class="connection-status"
      :class="{ 'connection-status--syncing': isSyncing }"
    >
      <div class="connection-status__icon">
        <i class="pi pi-sync" :class="{ 'pi-spin': isSyncing }"></i>
      </div>
      <div class="connection-status__content">
        <span class="connection-status__title">
          {{ isSyncing ? '正在同步数据...' : '正在连接服务器...' }}
        </span>
        <span class="connection-status__subtitle">
          {{ isSyncing ? '请稍候，马上就好' : '可以继续浏览已加载的内容' }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { usePolling } from '../hooks/usePolling'
import { heartbeatService } from '../services/heartbeat'
import api from '../api'
import { useRouter } from 'vue-router'
import ApiDocCard from './ApiDocCard.vue'
import EmptyState from './EmptyState.vue'
import LoadingOverlay from './LoadingOverlay.vue'
import InputText from 'primevue/inputtext'
import type { ApiDoc } from '@/types/api'

const router = useRouter()
const searchQuery = ref('')
const isSyncing = ref(false)
const retryCount = ref(0)
const maxRetries = 5
const retryDelay = ref(1000)
const localData = ref<ApiDoc[]>([])
const lastSuccessfulFetch = ref<Date | null>(null)
const loading = ref(false)

// 获取文档列表的函数
const fetchDocs = async (): Promise<ApiDoc[]> => {
  try {
    console.log('Fetching docs...')
    loading.value = true
    const response = await api.get<ApiDoc[]>('/docs')
    console.log('Docs fetched successfully:', response.data)
    
    if (!Array.isArray(response.data)) {
      console.error('Invalid response data format:', response.data)
      localData.value = []
      return []
    }
    
    lastSuccessfulFetch.value = new Date()
    retryCount.value = 0
    retryDelay.value = 1000
    return response.data
  } catch (error: any) {
    console.error('Error fetching docs:', error)
    
    if (error.message?.includes('Network Error')) {
      heartbeatService.isServerOnline.value = false
      localData.value = []
    }
    
    retryCount.value++
    if (retryCount.value <= maxRetries) {
      retryDelay.value = Math.min(retryDelay.value * 2, 30000)
      console.log(`Retrying fetch docs (${retryCount.value}/${maxRetries}) after ${retryDelay.value}ms...`)
      await new Promise(resolve => setTimeout(resolve, retryDelay.value))
      return await fetchDocs()
    }
    localData.value = []
    return []
  } finally {
    loading.value = false
  }
}

// 使用轮询 hook
const { data, error, refresh, startPolling, stopPolling } = usePolling(fetchDocs, {
  interval: heartbeatService.isServerOnline.value ? 30000 : 60000,
  maxRetries: 5,
  retryDelay: 5000,
  immediate: true,
  onError: (err: Error) => {
    console.error('Polling error:', err)
    if (err.message?.includes('Network Error')) {
      heartbeatService.isServerOnline.value = false
    }
  }
})

// 监听数据变化
watch(data, async (newData) => {
  if (newData) {
    console.log('Data updated:', newData)
    if (Array.isArray(newData)) {
      const hasChanges = JSON.stringify(localData.value) !== JSON.stringify(newData)
      if (hasChanges) {
        localData.value = [...newData]
        await nextTick()
        console.log('Local data updated:', localData.value)
      } else {
        console.log('Data unchanged, skipping update')
      }
    } else {
      console.error('Invalid data format:', newData)
      localData.value = []
    }
  } else {
    console.log('Data is null or undefined')
    localData.value = []
  }
}, { deep: true })

// 数据刷新函数
const refreshData = async () => {
  console.log('Refreshing data...')
  isSyncing.value = true
  retryCount.value = 0
  retryDelay.value = 1000
  
  try {
    stopPolling()
    const freshData = await fetchDocs()
    console.log('Fresh data fetched:', freshData)
    
    if (Array.isArray(freshData)) {
      const hasChanges = JSON.stringify(localData.value) !== JSON.stringify(freshData)
      if (hasChanges) {
        localData.value = [...freshData]
        await nextTick()
        console.log('Local data updated after refresh')
      } else {
        console.log('Data unchanged after refresh')
      }
    } else {
      console.error('Invalid fresh data format:', freshData)
      localData.value = []
    }
    
    startPolling()
    console.log('Data refresh completed')
  } catch (error: any) {
    console.error('Data refresh failed:', error)
    if (error.message?.includes('Network Error')) {
      heartbeatService.isServerOnline.value = false
    }
  } finally {
    isSyncing.value = false
  }
}

// 搜索处理
const handleSearch = async () => {
  if (!heartbeatService.isServerOnline.value) {
    console.log('Server is offline, search is disabled')
    return
  }
  
  try {
    if (searchQuery.value) {
      const response = await api.get(`/docs/search?q=${encodeURIComponent(searchQuery.value)}`)
      if (Array.isArray(response.data)) {
        const hasChanges = JSON.stringify(localData.value) !== JSON.stringify(response.data)
        if (hasChanges) {
          localData.value = [...response.data]
          await nextTick()
          console.log('Search results updated:', localData.value)
        } else {
          console.log('Search results unchanged')
        }
      } else {
        console.error('Invalid search results format:', response.data)
      }
    } else {
      refreshData()
    }
  } catch (error: any) {
    console.error('Search error:', error)
    if (error.message?.includes('Network Error')) {
      heartbeatService.isServerOnline.value = false
    }
  }
}

// 点击文档处理
const handleDocClick = (doc: ApiDoc) => {
  router.push(`/docs/${doc.id}`)
}

// 在组件挂载时获取数据
onMounted(async () => {
  console.log('Component mounted, initializing data...')
  await refreshData()
  
  const handleStatusChange = async (isOnline: boolean) => {
    console.log('Server status changed:', isOnline ? 'online' : 'offline')
    if (isOnline) {
      const timeSinceLastFetch = lastSuccessfulFetch.value
        ? Date.now() - lastSuccessfulFetch.value.getTime()
        : Infinity
        
      if (timeSinceLastFetch > 30000) {
        console.log('Last successful fetch was too long ago, refreshing data...')
        await refreshData()
      } else {
        console.log('Recent successful fetch, skipping refresh')
      }
    }
  }
  
  heartbeatService.addStatusCallback(handleStatusChange)
  
  onUnmounted(() => {
    heartbeatService.removeStatusCallback(handleStatusChange)
  })
})
</script>

<style scoped>
.api-doc-list {
  padding: 1rem;
  min-height: 100vh;
  position: relative;
}

.search-container {
  max-width: 600px;
  margin: 0 auto 2rem;
}

.connection-status {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  background: var(--surface-card);
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  box-shadow: var(--card-shadow);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  z-index: 1000;
  transition: all 0.3s ease;
  border: 1px solid var(--surface-border);
  max-width: 300px;
}

.connection-status--syncing {
  background: var(--primary-color);
  color: var(--primary-color-text);
}

.connection-status__icon {
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.connection-status__content {
  display: flex;
  flex-direction: column;
}

.connection-status__title {
  font-weight: 500;
  line-height: 1.2;
}

.connection-status__subtitle {
  font-size: 0.8rem;
  opacity: 0.8;
  margin-top: 0.2rem;
}

.pi-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
