{"version": 3, "sources": ["../../src/chip/style/ChipStyle.js", "../../src/chip/BaseChip.vue", "../../src/chip/Chip.vue", "../../src/chip/Chip.vue?vue&type=template&id=16c78839&lang.js"], "sourcesContent": ["import BaseStyle from '@primevue/core/base/style';\n\nconst theme = ({ dt }) => `\n.p-chip {\n    display: inline-flex;\n    align-items: center;\n    background: ${dt('chip.background')};\n    color: ${dt('chip.color')};\n    border-radius: ${dt('chip.border.radius')};\n    padding-block: ${dt('chip.padding.y')};\n    padding-inline: ${dt('chip.padding.x')};\n    gap: ${dt('chip.gap')};\n}\n\n.p-chip-icon {\n    color: ${dt('chip.icon.color')};\n    font-size: ${dt('chip.icon.font.size')};\n    width: ${dt('chip.icon.size')};\n    height: ${dt('chip.icon.size')};\n}\n\n.p-chip-image {\n    border-radius: 50%;\n    width: ${dt('chip.image.width')};\n    height: ${dt('chip.image.height')};\n    margin-inline-start: calc(-1 * ${dt('chip.padding.y')});\n}\n\n.p-chip:has(.p-chip-remove-icon) {\n    padding-inline-end: ${dt('chip.padding.y')};\n}\n\n.p-chip:has(.p-chip-image) {\n    padding-block-start: calc(${dt('chip.padding.y')} / 2);\n    padding-block-end: calc(${dt('chip.padding.y')} / 2);\n}\n\n.p-chip-remove-icon {\n    cursor: pointer;\n    font-size: ${dt('chip.remove.icon.size')};\n    width: ${dt('chip.remove.icon.size')};\n    height: ${dt('chip.remove.icon.size')};\n    color: ${dt('chip.remove.icon.color')};\n    border-radius: 50%;\n    transition: outline-color ${dt('chip.transition.duration')}, box-shadow ${dt('chip.transition.duration')};\n    outline-color: transparent;\n}\n\n.p-chip-remove-icon:focus-visible {\n    box-shadow: ${dt('chip.remove.icon.focus.ring.shadow')};\n    outline: ${dt('chip.remove.icon.focus.ring.width')} ${dt('chip.remove.icon.focus.ring.style')} ${dt('chip.remove.icon.focus.ring.color')};\n    outline-offset: ${dt('chip.remove.icon.focus.ring.offset')};\n}\n`;\n\nconst classes = {\n    root: 'p-chip p-component',\n    image: 'p-chip-image',\n    icon: 'p-chip-icon',\n    label: 'p-chip-label',\n    removeIcon: 'p-chip-remove-icon'\n};\n\nexport default BaseStyle.extend({\n    name: 'chip',\n    theme,\n    classes\n});\n", "<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport ChipStyle from 'primevue/chip/style';\n\nexport default {\n    name: 'BaseChip',\n    extends: BaseComponent,\n    props: {\n        label: {\n            type: String,\n            default: null\n        },\n        icon: {\n            type: String,\n            default: null\n        },\n        image: {\n            type: String,\n            default: null\n        },\n        removable: {\n            type: Boolean,\n            default: false\n        },\n        removeIcon: {\n            type: String,\n            default: undefined\n        }\n    },\n    style: ChipStyle,\n    provide() {\n        return {\n            $pcChip: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <div v-if=\"visible\" :class=\"cx('root')\" :aria-label=\"label\" v-bind=\"ptmi('root')\">\n        <slot>\n            <img v-if=\"image\" :src=\"image\" v-bind=\"ptm('image')\" :class=\"cx('image')\" />\n            <component v-else-if=\"$slots.icon\" :is=\"$slots.icon\" :class=\"cx('icon')\" v-bind=\"ptm('icon')\" />\n            <span v-else-if=\"icon\" :class=\"[cx('icon'), icon]\" v-bind=\"ptm('icon')\" />\n            <div v-if=\"label\" :class=\"cx('label')\" v-bind=\"ptm('label')\">{{ label }}</div>\n        </slot>\n        <slot v-if=\"removable\" name=\"removeicon\" :removeCallback=\"close\" :keydownCallback=\"onKeydown\">\n            <component :is=\"removeIcon ? 'span' : 'TimesCircleIcon'\" :class=\"[cx('removeIcon'), removeIcon]\" @click=\"close\" @keydown=\"onKeydown\" v-bind=\"ptm('removeIcon')\"></component>\n        </slot>\n    </div>\n</template>\n\n<script>\nimport TimesCircleIcon from '@primevue/icons/timescircle';\nimport BaseChip from './BaseChip.vue';\n\nexport default {\n    name: 'Chip',\n    extends: BaseChip,\n    inheritAttrs: false,\n    emits: ['remove'],\n    data() {\n        return {\n            visible: true\n        };\n    },\n    methods: {\n        onKeydown(event) {\n            if (event.key === 'Enter' || event.key === 'Backspace') {\n                this.close(event);\n            }\n        },\n        close(event) {\n            this.visible = false;\n            this.$emit('remove', event);\n        }\n    },\n    components: {\n        TimesCircleIcon\n    }\n};\n</script>\n", "<template>\n    <div v-if=\"visible\" :class=\"cx('root')\" :aria-label=\"label\" v-bind=\"ptmi('root')\">\n        <slot>\n            <img v-if=\"image\" :src=\"image\" v-bind=\"ptm('image')\" :class=\"cx('image')\" />\n            <component v-else-if=\"$slots.icon\" :is=\"$slots.icon\" :class=\"cx('icon')\" v-bind=\"ptm('icon')\" />\n            <span v-else-if=\"icon\" :class=\"[cx('icon'), icon]\" v-bind=\"ptm('icon')\" />\n            <div v-if=\"label\" :class=\"cx('label')\" v-bind=\"ptm('label')\">{{ label }}</div>\n        </slot>\n        <slot v-if=\"removable\" name=\"removeicon\" :removeCallback=\"close\" :keydownCallback=\"onKeydown\">\n            <component :is=\"removeIcon ? 'span' : 'TimesCircleIcon'\" :class=\"[cx('removeIcon'), removeIcon]\" @click=\"close\" @keydown=\"onKeydown\" v-bind=\"ptm('removeIcon')\"></component>\n        </slot>\n    </div>\n</template>\n\n<script>\nimport TimesCircleIcon from '@primevue/icons/timescircle';\nimport BaseChip from './BaseChip.vue';\n\nexport default {\n    name: 'Chip',\n    extends: BaseChip,\n    inheritAttrs: false,\n    emits: ['remove'],\n    data() {\n        return {\n            visible: true\n        };\n    },\n    methods: {\n        onKeydown(event) {\n            if (event.key === 'Enter' || event.key === 'Backspace') {\n                this.close(event);\n            }\n        },\n        close(event) {\n            this.visible = false;\n            this.$emit('remove', event);\n        }\n    },\n    components: {\n        TimesCircleIcon\n    }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAMA,QAAQ,SAARA,OAAKC,MAAA;AAAA,MAAMC,KAAED,KAAFC;AAAE,SAAA,qFAAAC,OAIDD,GAAG,iBAAiB,GAAC,gBAAA,EAAAC,OAC1BD,GAAG,YAAY,GAACC,wBAAAA,EAAAA,OACRD,GAAG,oBAAoB,GAAC,wBAAA,EAAAC,OACxBD,GAAG,gBAAgB,GAAC,yBAAA,EAAAC,OACnBD,GAAG,gBAAgB,GAACC,cAAAA,EAAAA,OAC/BD,GAAG,UAAU,GAAC,qCAAA,EAAAC,OAIZD,GAAG,iBAAiB,GAAC,oBAAA,EAAAC,OACjBD,GAAG,qBAAqB,GAACC,gBAAAA,EAAAA,OAC7BD,GAAG,gBAAgB,GAAC,iBAAA,EAAAC,OACnBD,GAAG,gBAAgB,GAAC,+DAAA,EAAAC,OAKrBD,GAAG,kBAAkB,GAACC,iBAAAA,EAAAA,OACrBD,GAAG,mBAAmB,GAAC,wCAAA,EAAAC,OACAD,GAAG,gBAAgB,GAAC,uEAAA,EAAAC,OAI/BD,GAAG,gBAAgB,GAACC,sEAAAA,EAAAA,OAIdD,GAAG,gBAAgB,GAAC,sCAAA,EAAAC,OACtBD,GAAG,gBAAgB,GAAC,2EAAA,EAAAC,OAKjCD,GAAG,uBAAuB,GAACC,gBAAAA,EAAAA,OAC/BD,GAAG,uBAAuB,GAAC,iBAAA,EAAAC,OAC1BD,GAAG,uBAAuB,GAAC,gBAAA,EAAAC,OAC5BD,GAAG,wBAAwB,GAACC,4DAAAA,EAAAA,OAETD,GAAG,0BAA0B,GAAC,eAAA,EAAAC,OAAgBD,GAAG,0BAA0B,GAAC,gGAAA,EAAAC,OAK1FD,GAAG,oCAAoC,GAACC,kBAAAA,EAAAA,OAC3CD,GAAG,mCAAmC,GAAC,GAAA,EAAAC,OAAID,GAAG,mCAAmC,GAAC,GAAA,EAAAC,OAAID,GAAG,mCAAmC,GAACC,yBAAAA,EAAAA,OACtHD,GAAG,oCAAoC,GAAC,QAAA;AAAA;AAI9D,IAAME,UAAU;EACZC,MAAM;EACNC,OAAO;EACPC,MAAM;EACNC,OAAO;EACPC,YAAY;AAChB;AAEA,IAAA,YAAeC,UAAUC,OAAO;EAC5BC,MAAM;EACNZ;EACAI;AACJ,CAAC;;;AC/DD,IAAA,WAAe;EACXS,MAAM;EACN,WAASC;EACTC,OAAO;IACHC,OAAO;MACHC,MAAMC;MACN,WAAS;;IAEbC,MAAM;MACFF,MAAMC;MACN,WAAS;;IAEbE,OAAO;MACHH,MAAMC;MACN,WAAS;;IAEbG,WAAW;MACPJ,MAAMK;MACN,WAAS;;IAEbC,YAAY;MACRN,MAAMC;MACN,WAASM;IACb;;EAEJC,OAAOC;EACPC,SAAO,SAAPA,UAAU;AACN,WAAO;MACHC,SAAS;MACTC,iBAAiB;;EAEzB;AACJ;AClBA,IAAAC,UAAe;EACXjB,MAAM;EACN,WAASkB;EACTC,cAAc;EACdC,OAAO,CAAC,QAAQ;EAChBC,MAAI,SAAJA,OAAO;AACH,WAAO;MACHC,SAAS;;;EAGjBC,SAAS;IACLC,WAAAA,SAAAA,UAAUC,OAAO;AACb,UAAIA,MAAMC,QAAQ,WAAWD,MAAMC,QAAQ,aAAa;AACpD,aAAKC,MAAMF,KAAK;MACpB;;IAEJE,OAAAA,SAAAA,MAAMF,OAAO;AACT,WAAKH,UAAU;AACf,WAAKM,MAAM,UAAUH,KAAK;IAC9B;;EAEJI,YAAY;IACRC,iBAAAA;EACJ;AACJ;;;;SCzCeC,MAAOT,WAAlBU,UAAA,GAAAC,mBAUK,OAVLC,WAUK;;IAVgB,SAAOC,KAAEC,GAAA,MAAA;IAAW,cAAYD,KAAKhC;KAAUgC,KAAIE,KAAA,MAAA,CAAA,GAAA,CACpEC,WAKMH,KAAAA,QAAAA,WAAAA,CAAAA,GALN,WAAA;AAAA,WAKM,CAJSA,KAAK5B,SAAhByB,UAAA,GAAAC,mBAA2E,OAA3EC,WAA2E;;MAAxDK,KAAKJ,KAAK5B;OAAU4B,KAAGK,IAAA,OAAA,GAAA;MAAY,SAAOL,KAAEC,GAAA,OAAA;iCACzCD,KAAAM,OAAOnC,QAA7B0B,UAAA,GAAAU,YAA+FC,wBAAvDR,KAAAM,OAAOnC,IAAI,GAAnD4B,WAA+F;;MAAzC,SAAOC,KAAEC,GAAA,MAAA;OAAkBD,KAAGK,IAAA,MAAA,CAAA,GAAA,MAAA,IAAA,CAAA,OAAA,CAAA,KACnEL,KAAI7B,QAArB0B,UAAA,GAAAC,mBAAyE,QAAzEC,WAAyE;;MAAjD,SAAK,CAAGC,KAAEC,GAAA,MAAA,GAAUD,KAAI7B,IAAA;OAAW6B,KAAGK,IAAA,MAAA,CAAA,GAAA,MAAA,EAAA,KAAA,mBAAA,IAAA,IAAA,GACnDL,KAAKhC,SAAhB6B,UAAA,GAAAC,mBAA6E,OAA7EC,WAA6E;;MAA1D,SAAOC,KAAEC,GAAA,OAAA;OAAmBD,KAAAK,IAAG,OAAA,CAAA,GAAA,gBAAcL,KAAIhC,KAAA,GAAA,EAAA,KAAA,mBAAA,IAAA,IAAA,CAAA;MAE5DgC,KAAS3B,YAArB8B,WAEMH,KAAAM,QAAA,cAAA;;IAFoCG,gBAAgBC,SAAKlB;IAAGmB,iBAAiBD,SAASrB;KAA5F,WAAA;AAAA,WAEM,EAAA,UAAA,GADFkB,YAA2KC,wBAA3JR,KAAWzB,aAAA,SAAA,iBAAA,GAA3BwB,WAA2K;MAAjH,SAAK,CAAGC,KAAEC,GAAA,YAAA,GAAgBD,KAAUzB,UAAA;MAAIqC,SAAOF,SAAKlB;MAAGH,WAASqB,SAASrB;OAAUW,KAAGK,IAAA,YAAA,CAAA,GAAA,MAAA,IAAA,CAAA,SAAA,WAAA,WAAA,CAAA,EAAA;;;;", "names": ["theme", "_ref", "dt", "concat", "classes", "root", "image", "icon", "label", "removeIcon", "BaseStyle", "extend", "name", "name", "BaseComponent", "props", "label", "type", "String", "icon", "image", "removable", "Boolean", "removeIcon", "undefined", "style", "ChipStyle", "provide", "$pcChip", "$parentInstance", "script", "BaseChip", "inheritAttrs", "emits", "data", "visible", "methods", "onKeydown", "event", "key", "close", "$emit", "components", "TimesCircleIcon", "$data", "_openBlock", "_createElementBlock", "_mergeProps", "_ctx", "cx", "ptmi", "_renderSlot", "src", "ptm", "$slots", "_createBlock", "_resolveDynamicComponent", "removeCallback", "$options", "keydownCallback", "onClick"]}