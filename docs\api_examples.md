# API 使用示例

## API 文档管理接口

### 1. 创建文档

```typescript
// 创建新文档
const createDocument = async (doc: ApiDocument) => {
  try {
    const response = await fetch('/api/documents', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        title: doc.title,
        url: doc.url,
        description: doc.description,
        tags: doc.tags,
        type: 'API_DOC'
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('创建文档失败:', error);
    throw error;
  }
}

// 使用示例
const newDoc = {
  title: 'API 文档示例',
  url: 'https://api.example.com/docs',
  description: '这是一个示例API文档',
  tags: ['api', 'example'],
};

try {
  const createdDoc = await createDocument(newDoc);
  console.log('文档创建成功:', createdDoc);
} catch (error) {
  // 处理错误
}
```

### 2. 更新文档

```typescript
// 更新现有文档
const updateDocument = async (id: string, updates: Partial<ApiDocument>) => {
  try {
    const response = await fetch(`/api/documents/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('更新文档失败:', error);
    throw error;
  }
}

// 使用示例
const docUpdates = {
  title: '更新后的标题',
  description: '更新后的描述'
};

try {
  const updatedDoc = await updateDocument('doc123', docUpdates);
  console.log('文档更新成功:', updatedDoc);
} catch (error) {
  // 处理错误
}
```

### 3. 删除文档

```typescript
// 删除文档
const deleteDocument = async (id: string) => {
  try {
    const response = await fetch(`/api/documents/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return true;
  } catch (error) {
    console.error('删除文档失败:', error);
    throw error;
  }
}

// 使用示例
try {
  await deleteDocument('doc123');
  console.log('文档删除成功');
} catch (error) {
  // 处理错误
}
```

### 4. 错误处理最佳实践

```typescript
// 统一的错误处理
interface ApiError {
  code: string;
  message: string;
  details?: any;
}

class DocumentApiError extends Error {
  constructor(
    public code: string,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'DocumentApiError';
  }
}

// 统一的响应处理
const handleApiResponse = async (response: Response) => {
  if (!response.ok) {
    const error: ApiError = await response.json();
    throw new DocumentApiError(
      error.code,
      error.message,
      error.details
    );
  }
  return response.json();
};

// 使用示例
const createDocumentWithErrorHandling = async (doc: ApiDocument) => {
  try {
    const response = await fetch('/api/documents', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(doc)
    });

    return await handleApiResponse(response);
  } catch (error) {
    if (error instanceof DocumentApiError) {
      switch (error.code) {
        case 'DUPLICATE_URL':
          console.error('文档URL已存在');
          break;
        case 'INVALID_INPUT':
          console.error('输入数据无效:', error.details);
          break;
        default:
          console.error('未知错误:', error.message);
      }
    }
    throw error;
  }
};
```

### 5. 批量操作示例

```typescript
// 批量创建文档
const createDocuments = async (docs: ApiDocument[]) => {
  try {
    const response = await fetch('/api/documents/batch', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ documents: docs })
    });

    return await handleApiResponse(response);
  } catch (error) {
    console.error('批量创建文档失败:', error);
    throw error;
  }
};

// 批量删除文档
const deleteDocuments = async (ids: string[]) => {
  try {
    const response = await fetch('/api/documents/batch', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ids })
    });

    return await handleApiResponse(response);
  } catch (error) {
    console.error('批量删除文档失败:', error);
    throw error;
  }
};
```

## 注意事项

1. 请求头设置
   - 始终设置 `Content-Type: application/json`
   - 如果需要认证，添加 `Authorization` 头

2. 错误处理
   - 使用统一的错误处理机制
   - 区分不同类型的错误
   - 提供有意义的错误信息

3. 响应处理
   - 检查响应状态码
   - 解析响应数据
   - 处理异常情况

4. 类型定义
   - 使用 TypeScript 类型定义
   - 定义请求和响应的接口
   - 使用泛型提高代码复用性

5. 安全性
   - 验证输入数据
   - 使用 HTTPS
   - 实现适当的认证和授权

## 使用建议

1. 在实际项目中，建议：
   - 封装API调用到专门的service层
   - 使用环境变量配置API基础URL
   - 实现请求拦截器处理通用逻辑
   - 实现响应拦截器统一处理错误

2. 代码组织：
   ```typescript
   // services/api.ts
   export const DocumentService = {
     create: createDocument,
     update: updateDocument,
     delete: deleteDocument,
     createBatch: createDocuments,
     deleteBatch: deleteDocuments,
   };
   ```

3. 使用示例：
   ```typescript
   import { DocumentService } from '@/services/api';

   // 在组件中使用
   const handleCreateDocument = async () => {
     try {
       const newDoc = await DocumentService.create({
         title: 'New API Doc',
         url: 'https://example.com',
       });
       // 处理成功响应
     } catch (error) {
       // 处理错误
     }
   };
   ```
