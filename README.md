# API Documentation Server

## 概述
基于 FastAPI 和 Vue 3 构建的现代化 API 文档管理系统。提供直观的界面来管理和查看 API 文档，支持实时更新和搜索功能。

## 技术栈

### 后端
- FastAPI (Python Web 框架)
- PostgreSQL (数据库)
- SQLAlchemy (ORM)
- Alembic (数据库迁移)
- Python 3.8+
- Pydantic (数据验证)

### 前端
- Vue 3 (前端框架)
- Vite (构建工具)
- TypeScript (类型支持)
- PrimeVue (UI组件库)
- Axios (HTTP请求)
- Vue Router (路由管理)

## 项目特性

### 后端功能
- RESTful API 设计
- 自动生成 OpenAPI/Swagger 文档
- 数据库迁移和版本控制
- CORS 跨域支持
- 错误处理和日志记录

### 前端功能
- 现代响应式设计
- 实时数据更新 (30秒自动刷新)
- 搜索和过滤功能
- 优雅的空状态和加载状态
- 完整的 CRUD 操作界面
- TypeScript 类型安全

## 项目结构
```
api_docs_server/
├── api_docs_server/        # 后端代码
│   ├── models/           # 数据模型
│   ├── routes/           # API路由
│   ├── schemas/          # Pydantic模型
│   └── main.py          # FastAPI应用入口
├── frontend/             # 前端代码
│   ├── src/
│   │   ├── api/        # API 接口
│   │   ├── components/ # Vue组件
│   │   ├── config/     # 配置文件
│   │   ├── hooks/      # Vue Hooks
│   │   ├── pages/      # 页面组件
│   │   └── types/      # TypeScript类型
│   ├── index.html
│   └── package.json
├── alembic/             # 数据库迁移
├── api_docs_server.yaml.tmpl  # K8s部署模板
└── requirements.txt     # Python依赖
```

## 部署说明

### 本地开发

1. 后端设置:
```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 启动后端服务
uvicorn api_docs_server.main:app --reload --port 8000
```

2. 前端设置:
```bash
# 进入前端目录
cd frontend

# 安装依赖
bun install

# 启动开发服务器
bun run dev
```

### 生产环境

项目使用 Kubernetes 进行部署，主要配置包括：

- Ingress 配置支持 HTTPS
- 前后端分离部署
- 自动 SSL 证书管理
- 健康检查和自动恢复
- 域名: https://api-docs.woa.com

#### Kubernetes 部署配置

1. 服务配置:
```yaml
# 前端服务
- ClusterIP 类型服务
- 端口: 80
- 选择器: app=<project>-frontend

# 后端服务
- ClusterIP 类型服务
- 端口: 8000
- 选择器: app=<project>-backend
```

2. Ingress 配置:
```yaml
# 路由规则
- /api(/|$)(.*) -> 后端服务 (8000端口)
- /assets/(.*) -> 前端服务 (80端口)
- /(.*) -> 前端服务 (80端口)

# 重要注解
nginx.ingress.kubernetes.io/rewrite-target: /$2
nginx.ingress.kubernetes.io/use-regex: "true"
```

3. 前端 Nginx 配置:
```nginx
# MIME 类型支持
types {
    application/javascript js mjs;
    text/javascript js mjs;
}

# JavaScript 模块处理
location ~ \.m?js$ {
    add_header Content-Type application/javascript;
}

# 静态资源缓存
location /assets/ {
    expires 1y;
    add_header Cache-Control "public, no-transform";
}
```

4. Vite 构建配置:
```typescript
export default defineConfig({
  base: '/',
  build: {
    assetsDir: 'assets',
    rollupOptions: {
      output: {
        manualChunks: undefined
      }
    }
  }
})
```

### 故障排除指南

#### 1. 502 Bad Gateway
可能的原因：
- 后端服务未就绪
- 服务配置错误
- 网络策略限制

解决方案：
- 检查 Pod 状态和日志
- 验证服务端点配置
- 检查网络策略

#### 2. JavaScript 模块加载失败
症状：
- "Failed to load module script: Expected a JavaScript module script but the server responded with a MIME type of text/html"

解决方案：
- 确保 Nginx 配置了正确的 MIME 类型
- 检查 Vite 构建配置
- 验证静态资源路径

#### 3. 静态资源 404
解决方案：
- 检查 Ingress 路径配置
- 验证资源文件位置
- 确认构建输出目录

### 本地测试

使用 Podman 进行本地测试：
```bash
# 构建镜像
"C:\Program Files\RedHat\Podman\podman.exe" build -t api-docs-server .

# 运行容器
"C:\Program Files\RedHat\Podman\podman.exe" run -p 8000:8000 api-docs-server
```

### 常用运维命令

```bash
# 查看服务状态
kubectl get svc -n <namespace>

# 查看 Pod 状态
kubectl get pods -n <namespace>

# 查看 Pod 日志
kubectl logs -n <namespace> <pod-name>

# 查看 Ingress 配置
kubectl get ingress -n <namespace>
```

## 环境要求

- Node.js 16+
- Python 3.8+
- PostgreSQL 12+
- Kubernetes 1.18+

## 开发指南

1. 代码规范
   - 后端遵循 PEP 8 规范
   - 前端使用 ESLint 和 Prettier
   - TypeScript 严格模式

2. 提交规范
   - 使用清晰的提交信息
   - 保持提交粒度合适
   - 提交前进行代码格式化

3. 分支管理
   - 主分支: main
   - 开发分支: dev
   - 功能分支: feature/*
   - 修复分支: hotfix/*

## 监控和日志

- 错误日志记录在控制台
- 支持 Kubernetes 原生监控
- 健康检查端点: /health

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起合并请求

## 许可证

[MIT License](LICENSE)
