import { describe, it, expect, beforeEach, vi } from 'vitest'
import axios from '../axios'
import { ApiDocService } from '../apiService'

vi.mock('../axios', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    interceptors: {
      request: { use: vi.fn() },
      response: { use: vi.fn() }
    }
  },
  API_ENDPOINTS: {
    DOCS: '/api/docs',
    VALIDATE_URL: '/api/docs/url/validate',
    HEALTH: '/api/health'
  }
}))

describe('API Integration Tests', () => {
  const mockDoc = {
    id: '1',
    title: 'Test API Doc',
    url: 'https://example.com/api',
    type: 'swagger'
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Document Management', () => {
    it('should handle the complete document lifecycle', async () => {
      // Create
      const createResponse = { data: mockDoc }
      vi.mocked(axios.post).mockResolvedValueOnce(createResponse)

      const created = await ApiDocService.createDoc({
        title: mockDoc.title,
        url: mockDoc.url
      })

      expect(created).toEqual(mockDoc)
      expect(axios.post).toHaveBeenCalledWith('/api/docs', {
        title: mockDoc.title,
        url: mockDoc.url
      })

      // Fetch
      const fetchResponse = { data: [mockDoc] }
      vi.mocked(axios.get).mockResolvedValueOnce(fetchResponse)

      const fetched = await ApiDocService.getDocs()
      expect(fetched).toEqual([mockDoc])
      expect(axios.get).toHaveBeenCalledWith('/api/docs', { params: undefined })

      // Update
      const updatedDoc = { ...mockDoc, title: 'Updated Title' }
      const updateResponse = { data: updatedDoc }
      vi.mocked(axios.put).mockResolvedValueOnce(updateResponse)

      const updated = await ApiDocService.updateDoc(mockDoc.id, {
        title: updatedDoc.title
      })

      expect(updated).toEqual(updatedDoc)
      expect(axios.put).toHaveBeenCalledWith(
        `/api/docs/${mockDoc.id}`,
        { title: updatedDoc.title }
      )

      // Delete
      vi.mocked(axios.delete).mockResolvedValueOnce({})

      await ApiDocService.deleteDoc(mockDoc.id)
      expect(axios.delete).toHaveBeenCalledWith(`/api/docs/${mockDoc.id}`)
    })

    it('should handle validation and error cases', async () => {
      // URL Validation - Success
      const validationSuccess = { data: { valid: true } }
      vi.mocked(axios.post).mockResolvedValueOnce(validationSuccess)

      const validationResult = await ApiDocService.validateUrl('https://valid-url.com')
      expect(validationResult).toEqual({ valid: true })

      // URL Validation - Error
      const validationError = {
        response: {
          status: 400,
          data: { detail: 'Invalid URL format' }
        }
      }
      vi.mocked(axios.post).mockRejectedValueOnce(validationError)

      await expect(
        ApiDocService.validateUrl('invalid-url')
      ).rejects.toThrow('Invalid URL format')

      // Network Error
      const networkError = new Error('Network Error')
      vi.mocked(axios.get).mockRejectedValueOnce(networkError)

      await expect(
        ApiDocService.getDocs()
      ).rejects.toThrow('Network Error')

      // Not Found Error
      const notFoundError = {
        response: {
          status: 404,
          data: { detail: 'Document not found' }
        }
      }
      vi.mocked(axios.get).mockRejectedValueOnce(notFoundError)

      await expect(
        ApiDocService.getDocs()
      ).rejects.toThrow('Document not found')
    })
  })
})
