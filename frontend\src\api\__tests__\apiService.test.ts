import { describe, it, expect, beforeEach, vi } from 'vitest'
import { ApiDocService } from '../apiService'
import axios from '../axios'

vi.mock('../axios', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    interceptors: {
      request: { use: vi.fn() },
      response: { use: vi.fn() }
    }
  },
  API_ENDPOINTS: {
    DOCS: '/api/docs',
    VALIDATE_URL: '/api/docs/url/validate',
    HEALTH: '/api/health'
  }
}))

describe('ApiDocService', () => {
  const mockDoc = {
    id: '1',
    title: 'Test API Doc',
    url: 'https://example.com/api',
    type: 'swagger'
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('CRUD Operations', () => {
    it('should create a new document', async () => {
      const response = { data: mockDoc }
      vi.mocked(axios.post).mockResolvedValueOnce(response)

      const result = await ApiDocService.createDoc({
        title: mockDoc.title,
        url: mockDoc.url
      })

      expect(result).toEqual(mockDoc)
      expect(axios.post).toHaveBeenCalledWith(
        '/api/docs',
        {
          title: mockDoc.title,
          url: mockDoc.url
        }
      )
    })

    it('should fetch documents', async () => {
      const response = { data: [mockDoc] }
      vi.mocked(axios.get).mockResolvedValueOnce(response)

      const result = await ApiDocService.getDocs()

      expect(result).toEqual([mockDoc])
      expect(axios.get).toHaveBeenCalledWith('/api/docs', { params: undefined })
    })

    it('should update a document', async () => {
      const updatedDoc = { ...mockDoc, title: 'Updated Title' }
      const response = { data: updatedDoc }
      vi.mocked(axios.put).mockResolvedValueOnce(response)

      const result = await ApiDocService.updateDoc(mockDoc.id, {
        title: updatedDoc.title
      })

      expect(result).toEqual(updatedDoc)
      expect(axios.put).toHaveBeenCalledWith(
        `/api/docs/${mockDoc.id}`,
        { title: updatedDoc.title }
      )
    })

    it('should delete a document', async () => {
      vi.mocked(axios.delete).mockResolvedValueOnce({})

      await ApiDocService.deleteDoc(mockDoc.id)

      expect(axios.delete).toHaveBeenCalledWith(`/api/docs/${mockDoc.id}`)
    })

    it('should validate document URL', async () => {
      const validationResult = { valid: true }
      const response = { data: validationResult }
      vi.mocked(axios.post).mockResolvedValueOnce(response)

      const result = await ApiDocService.validateUrl(mockDoc.url)

      expect(result).toEqual(validationResult)
      expect(axios.post).toHaveBeenCalledWith('/api/docs/url/validate', {
        url: mockDoc.url
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle network errors', async () => {
      const error = new Error('Network Error')
      vi.mocked(axios.get).mockRejectedValueOnce(error)

      await expect(ApiDocService.getDocs()).rejects.toThrow('Network Error')
    })

    it('should handle 404 errors', async () => {
      const error = {
        response: {
          status: 404,
          data: { detail: 'Not Found' }
        }
      }
      vi.mocked(axios.get).mockRejectedValueOnce(error)

      await expect(ApiDocService.getDocs()).rejects.toThrow('Not Found')
    })

    it('should handle validation errors', async () => {
      const error = {
        response: {
          status: 400,
          data: { detail: 'Invalid URL' }
        }
      }
      vi.mocked(axios.post).mockRejectedValueOnce(error)

      await expect(
        ApiDocService.validateUrl('invalid-url')
      ).rejects.toThrow('Invalid URL')
    })
  })
})
