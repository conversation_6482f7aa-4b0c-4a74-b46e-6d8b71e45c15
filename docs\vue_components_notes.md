# Vue 组件开发笔记

## 组件重构经验

### 1. 对话框组件的 v-model 绑定

在开发 `ApiDocForm` 和 `ApiDocPreview` 对话框组件时，遇到了 v-model 绑定的问题。主要涉及以下几点：

#### 问题描述

1. 使用 `v-model:visible` 直接绑定 prop 时报错：
   ```
   v-model cannot be used on a prop, because local prop bindings are not writable.
   Use a v-bind binding combined with a v-on listener that emits update:x event instead.
   ```

2. 这是因为在 Vue 中，props 是单向数据流，子组件不能直接修改父组件传递的 prop。

#### 解决方案

1. 使用 Vue 3 的标准 v-model 实现：
   ```vue
   <!-- 子组件内部 -->
   <template>
     <Dialog
       :visible="modelValue"
       @update:visible="$emit('update:modelValue', $event)"
       ...
     >
   </template>

   <script setup>
   const props = defineProps({
     modelValue: {
       type: Boolean,
       required: true
     }
   })

   const emit = defineEmits(['update:modelValue'])
   </script>
   ```

2. 在父组件中的使用方式：
   ```vue
   <ApiDocForm
     v-model="formDialogVisible"
     :doc="editingDoc"
     @save="saveDocument"
   />
   ```

#### 最佳实践

1. 在 Vue 3 中，推荐使用 `modelValue` 作为默认的 v-model prop 名
2. 使用 `update:modelValue` 作为更新事件名
3. 在子组件中，通过 `:visible` 和 `@update:visible` 来实现与第三方组件的双向绑定
4. 在父组件中，使用简单的 `v-model` 而不是 `v-model:visible`

### 2. 组件拆分原则

在重构过程中，我们将大型组件拆分为多个小组件：

1. `ApiDocView.vue` - 作为路由容器
2. `ApiDocList.vue` - 文档列表和主要业务逻辑
3. `ApiDocHero.vue` - 顶部区域
4. `ApiDocCard.vue` - 文档卡片
5. `ApiDocPreview.vue` - 预览对话框
6. `ApiDocForm.vue` - 表单对话框
7. `EmptyState.vue` - 空状态提示

#### 拆分原则：

1. 单一职责：每个组件只负责一个特定的功能
2. 高内聚低耦合：组件之间通过 props 和 events 通信
3. 可重用性：通用的 UI 组件可以在其他地方复用
4. 可维护性：每个组件的代码量保持在合理范围内

### 3. 状态管理

1. 使用 `reactive` 管理复杂的表单状态
2. 使用 `ref` 管理简单的响应式数据
3. 通过 `props` 和 `emit` 实现组件间的数据流动
4. 使用 `watch` 监听 props 变化并更新本地状态

### 4. 组件通信

1. 父子组件：
   - Props 向下传递数据
   - Events 向上传递事件
   - v-model 实现双向绑定

2. 兄弟组件：
   - 通过父组件作为中间层
   - 使用 emit/props 链路传递数据和事件

## 注意事项

1. 避免直接修改 props
2. 使用 emits 声明所有事件
3. 保持组件的独立性和可测试性
4. 遵循 Vue 3 的组合式 API 最佳实践
5. 使用 TypeScript 类型标注提高代码可维护性

## 待优化点

1. 添加组件的 TypeScript 类型定义
2. 实现组件的单元测试
3. 添加组件文档和示例
4. 优化组件的性能
5. 添加错误处理和加载状态
