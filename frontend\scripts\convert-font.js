import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import ttf2woff2 from 'ttf2woff2';
import chalk from 'chalk';
import ora from 'ora';
import { spawn } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const FONTS_DIR = path.join(__dirname, '../src/assets/fonts');
const TEMP_DIR = path.join(__dirname, '../.temp/fonts');

// 日志工具
const log = {
    info: (msg) => console.log(chalk.blue('ℹ'), chalk.blue(msg)),
    success: (msg) => console.log(chalk.green('✓'), chalk.green(msg)),
    warn: (msg) => console.log(chalk.yellow('⚠'), chalk.yellow(msg)),
    error: (msg) => console.log(chalk.red('✖'), chalk.red(msg))
};

// 确保目录存在
async function ensureDir(dir) {
    await fs.mkdir(dir, { recursive: true });
}

// 获取文件大小的可读字符串
function formatFileSize(bytes) {
    const units = ['B', 'KB', 'MB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
}

// 使用 woff2_compress 优化字体
async function optimizeFont(inputFile) {
    const fontName = path.basename(inputFile);
    const outputFile = inputFile.replace(/\.(otf|ttf|woff2)$/, '.opt.woff2');
    
    const spinner = ora({
        text: `Optimizing ${fontName}...`,
        spinner: 'dots'
    }).start();
    
    try {
        // 使用 woff2_compress 命令行工具
        const subprocess = spawn('woff2_compress', [inputFile]);
        
        subprocess.on('close', async (code) => {
            if (code !== 0) {
                spinner.fail(`Failed to optimize ${fontName}`);
                return;
            }
            
            try {
                const originalStats = await fs.stat(inputFile);
                const originalSize = originalStats.size;
                const optimizedStats = await fs.stat(outputFile);
                const optimizedSize = optimizedStats.size;
                const reduction = ((originalSize - optimizedSize) / originalSize * 100).toFixed(2);
                
                // 用优化后的文件替换原文件
                await fs.rename(outputFile, inputFile);
                
                spinner.succeed(
                    `Optimized ${fontName}: ` +
                    `${formatFileSize(originalSize)} → ${formatFileSize(optimizedSize)} ` +
                    `(${reduction}% reduction)`
                );
            } catch (error) {
                spinner.fail(`Error processing ${fontName}: ${error.message}`);
            }
        });
    } catch (error) {
        spinner.fail(`Failed to start optimization: ${error.message}`);
    }
}

// 转换字体文件
async function convertFont(inputFile) {
    const inputBasename = path.basename(inputFile);
    const outputFile = inputFile.replace(/\.(otf|ttf)$/, '.woff2');
    
    // 检查输出文件是否存在
    if (await fs.stat(outputFile).catch(() => null)) {
        log.info(`Skipping ${inputBasename}: WOFF2 file already exists`);
        return false;
    }
    
    const spinner = ora({
        text: `Converting ${inputBasename} to WOFF2...`,
        spinner: 'dots'
    }).start();
    
    try {
        const input = await fs.readFile(inputFile);
        const startTime = Date.now();
        const output = ttf2woff2(input);
        const endTime = Date.now();
        
        await fs.writeFile(outputFile, output);
        spinner.succeed(`Converted ${inputBasename} to ${path.basename(outputFile)} (${((endTime - startTime)/1000).toFixed(2)}s)`);
        
        // 优化转换后的文件
        await optimizeFont(outputFile);
        return true;
    } catch (error) {
        spinner.fail(`Failed to convert ${inputBasename}: ${error.message}`);
        return false;
    }
}

// 处理所有字体文件
async function processAllFonts() {
    const fontFiles = await fs.readdir(FONTS_DIR)
        .then(files => files.filter(file => /\.(otf|ttf)$/i.test(file)))
        .then(files => files.map(file => path.join(FONTS_DIR, file)));

    if (fontFiles.length === 0) {
        log.warn('No OTF/TTF fonts found in fonts directory');
        return;
    }

    log.info(`Found ${fontFiles.length} font file(s) to process`);
    const results = await Promise.all(fontFiles.map(convertFont));
    const processed = results.filter(Boolean).length;
    
    if (processed > 0) {
        log.success(`Processed ${processed} font(s)`);
    } else {
        log.info('No fonts needed processing');
    }
}

// 主函数
async function main() {
    try {
        await ensureDir(FONTS_DIR);
        if (process.argv[2]) {
            await convertFont(process.argv[2]);
        } else {
            await processAllFonts();
        }
    } catch (error) {
        log.error(`Unexpected error: ${error.message}`);
        process.exit(1);
    }
}

main();
