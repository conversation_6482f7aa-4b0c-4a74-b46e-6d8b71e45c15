{"version": 3, "sources": ["../../src/ripple/style/RippleStyle.js", "../../src/ripple/BaseRipple.js", "../../src/ripple/Ripple.js"], "sourcesContent": ["import BaseStyle from '@primevue/core/base/style';\n\nconst theme = ({ dt }) => `\n.p-ink {\n    display: block;\n    position: absolute;\n    background: ${dt('ripple.background')};\n    border-radius: 100%;\n    transform: scale(0);\n    pointer-events: none;\n}\n\n.p-ink-active {\n    animation: ripple 0.4s linear;\n}\n\n@keyframes ripple {\n    100% {\n        opacity: 0;\n        transform: scale(2.5);\n    }\n}\n`;\n\nconst classes = {\n    root: 'p-ink'\n};\n\nexport default BaseStyle.extend({\n    name: 'ripple-directive',\n    theme,\n    classes\n});\n", "import BaseDirective from '@primevue/core/basedirective';\nimport RippleStyle from 'primevue/ripple/style';\n\nconst BaseRipple = BaseDirective.extend({\n    style: RippleStyle\n});\n\nexport default BaseRipple;\n", "import { addClass, createElement, getAttribute, getHeight, getOffset, getOuterHeight, getOuterWidth, getWidth, removeClass } from '@primeuix/utils/dom';\nimport BaseRipple from './BaseRipple';\n\nconst Ripple = BaseRipple.extend('ripple', {\n    watch: {\n        'config.ripple'(newValue) {\n            if (newValue) {\n                this.createRipple(this.$host);\n                this.bindEvents(this.$host);\n\n                this.$host.setAttribute('data-pd-ripple', true);\n                this.$host.style['overflow'] = 'hidden';\n                this.$host.style['position'] = 'relative';\n            } else {\n                this.remove(this.$host);\n                this.$host.removeAttribute('data-pd-ripple');\n            }\n        }\n    },\n    unmounted(el) {\n        this.remove(el);\n    },\n    timeout: undefined,\n    methods: {\n        bindEvents(el) {\n            el.addEventListener('mousedown', this.onMouseDown.bind(this));\n        },\n        unbindEvents(el) {\n            el.removeEventListener('mousedown', this.onMouseDown.bind(this));\n        },\n        createRipple(el) {\n            const ink = createElement('span', {\n                role: 'presentation',\n                'aria-hidden': true,\n                'data-p-ink': true,\n                'data-p-ink-active': false,\n                class: !this.isUnstyled() && this.cx('root'),\n                onAnimationEnd: this.onAnimationEnd.bind(this),\n                [this.$attrSelector]: '',\n                'p-bind': this.ptm('root')\n            });\n\n            el.appendChild(ink);\n\n            this.$el = ink;\n        },\n        remove(el) {\n            let ink = this.getInk(el);\n\n            if (ink) {\n                this.$host.style['overflow'] = '';\n                this.$host.style['position'] = '';\n\n                this.unbindEvents(el);\n                ink.removeEventListener('animationend', this.onAnimationEnd);\n                ink.remove();\n            }\n        },\n        onMouseDown(event) {\n            let target = event.currentTarget;\n            let ink = this.getInk(target);\n\n            if (!ink || getComputedStyle(ink, null).display === 'none') {\n                return;\n            }\n\n            !this.isUnstyled() && removeClass(ink, 'p-ink-active');\n            ink.setAttribute('data-p-ink-active', 'false');\n\n            if (!getHeight(ink) && !getWidth(ink)) {\n                let d = Math.max(getOuterWidth(target), getOuterHeight(target));\n\n                ink.style.height = d + 'px';\n                ink.style.width = d + 'px';\n            }\n\n            let offset = getOffset(target);\n            let x = event.pageX - offset.left + document.body.scrollTop - getWidth(ink) / 2;\n            let y = event.pageY - offset.top + document.body.scrollLeft - getHeight(ink) / 2;\n\n            ink.style.top = y + 'px';\n            ink.style.left = x + 'px';\n\n            !this.isUnstyled() && addClass(ink, 'p-ink-active');\n            ink.setAttribute('data-p-ink-active', 'true');\n\n            this.timeout = setTimeout(() => {\n                if (ink) {\n                    !this.isUnstyled() && removeClass(ink, 'p-ink-active');\n                    ink.setAttribute('data-p-ink-active', 'false');\n                }\n            }, 401);\n        },\n        onAnimationEnd(event) {\n            if (this.timeout) {\n                clearTimeout(this.timeout);\n            }\n\n            !this.isUnstyled() && removeClass(event.currentTarget, 'p-ink-active');\n            event.currentTarget.setAttribute('data-p-ink-active', 'false');\n        },\n        getInk(el) {\n            return el && el.children ? [...el.children].find((child) => getAttribute(child, 'data-pc-name') === 'ripple') : undefined;\n        }\n    }\n});\n\nexport default Ripple;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAEA,IAAMA,QAAQ,SAARA,OAAKC,MAAA;AAAA,MAAMC,KAAED,KAAFC;AAAE,SAAA,6EAAAC,OAIDD,GAAG,mBAAmB,GAAC,mPAAA;AAAA;AAkBzC,IAAME,UAAU;EACZC,MAAM;AACV;AAEA,IAAA,cAAeC,UAAUC,OAAO;EAC5BC,MAAM;EACNR;EACAI;AACJ,CAAC;;;AC7BD,IAAMK,aAAaC,cAAcC,OAAO;EACpCC,OAAOC;AACX,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACFD,IAAMC,SAASL,WAAWE,OAAO,UAAU;EACvCI,OAAO;IACH,iBAAAC,SAAAA,aAAgBC,UAAU;AACtB,UAAIA,UAAU;AACV,aAAKC,aAAa,KAAKC,KAAK;AAC5B,aAAKC,WAAW,KAAKD,KAAK;AAE1B,aAAKA,MAAME,aAAa,kBAAkB,IAAI;AAC9C,aAAKF,MAAMP,MAAM,UAAU,IAAI;AAC/B,aAAKO,MAAMP,MAAM,UAAU,IAAI;MACnC,OAAO;AACH,aAAKU,OAAO,KAAKH,KAAK;AACtB,aAAKA,MAAMI,gBAAgB,gBAAgB;MAC/C;IACJ;;EAEJC,WAAAA,SAAAA,UAAUC,IAAI;AACV,SAAKH,OAAOG,EAAE;;EAElBC,SAASC;EACTC,SAAS;IACLR,YAAAA,SAAAA,WAAWK,IAAI;AACXA,SAAGI,iBAAiB,aAAa,KAAKC,YAAYC,KAAK,IAAI,CAAC;;IAEhEC,cAAAA,SAAAA,aAAaP,IAAI;AACbA,SAAGQ,oBAAoB,aAAa,KAAKH,YAAYC,KAAK,IAAI,CAAC;;IAEnEb,cAAAA,SAAAA,aAAaO,IAAI;AACb,UAAMS,MAAMC,cAAc,QAAMC,gBAAAA,gBAAA;QAC5BC,MAAM;QACN,eAAe;QACf,cAAc;QACd,qBAAqB;QACrB,SAAO,CAAC,KAAKC,WAAU,KAAM,KAAKC,GAAG,MAAM;QAC3CC,gBAAgB,KAAKA,eAAeT,KAAK,IAAI;MAAC,GAC7C,KAAKU,eAAgB,EAAE,GACxB,UAAU,KAAKC,IAAI,MAAM,CAAC,CAC7B;AAEDjB,SAAGkB,YAAYT,GAAG;AAElB,WAAKU,MAAMV;;IAEfZ,QAAAA,SAAAA,OAAOG,IAAI;AACP,UAAIS,MAAM,KAAKW,OAAOpB,EAAE;AAExB,UAAIS,KAAK;AACL,aAAKf,MAAMP,MAAM,UAAU,IAAI;AAC/B,aAAKO,MAAMP,MAAM,UAAU,IAAI;AAE/B,aAAKoB,aAAaP,EAAE;AACpBS,YAAID,oBAAoB,gBAAgB,KAAKO,cAAc;AAC3DN,YAAIZ,OAAM;MACd;;IAEJQ,aAAAA,SAAAA,YAAYgB,OAAO;AAAA,UAAAC,QAAA;AACf,UAAIC,SAASF,MAAMG;AACnB,UAAIf,MAAM,KAAKW,OAAOG,MAAM;AAE5B,UAAI,CAACd,OAAOgB,iBAAiBhB,KAAK,IAAI,EAAEiB,YAAY,QAAQ;AACxD;MACJ;AAEA,OAAC,KAAKb,WAAU,KAAMc,YAAYlB,KAAK,cAAc;AACrDA,UAAIb,aAAa,qBAAqB,OAAO;AAE7C,UAAI,CAACgC,UAAUnB,GAAG,KAAK,CAACoB,SAASpB,GAAG,GAAG;AACnC,YAAIqB,IAAIC,KAAKC,IAAIC,cAAcV,MAAM,GAAGW,eAAeX,MAAM,CAAC;AAE9Dd,YAAItB,MAAMgD,SAASL,IAAI;AACvBrB,YAAItB,MAAMiD,QAAQN,IAAI;MAC1B;AAEA,UAAIO,SAASC,UAAUf,MAAM;AAC7B,UAAIgB,IAAIlB,MAAMmB,QAAQH,OAAOI,OAAOC,SAASC,KAAKC,YAAYf,SAASpB,GAAG,IAAI;AAC9E,UAAIoC,IAAIxB,MAAMyB,QAAQT,OAAOU,MAAML,SAASC,KAAKK,aAAapB,UAAUnB,GAAG,IAAI;AAE/EA,UAAItB,MAAM4D,MAAMF,IAAI;AACpBpC,UAAItB,MAAMsD,OAAOF,IAAI;AAErB,OAAC,KAAK1B,WAAU,KAAMoC,SAASxC,KAAK,cAAc;AAClDA,UAAIb,aAAa,qBAAqB,MAAM;AAE5C,WAAKK,UAAUiD,WAAW,WAAM;AAC5B,YAAIzC,KAAK;AACL,WAACa,MAAKT,WAAU,KAAMc,YAAYlB,KAAK,cAAc;AACrDA,cAAIb,aAAa,qBAAqB,OAAO;QACjD;SACD,GAAG;;IAEVmB,gBAAAA,SAAAA,eAAeM,OAAO;AAClB,UAAI,KAAKpB,SAAS;AACdkD,qBAAa,KAAKlD,OAAO;MAC7B;AAEA,OAAC,KAAKY,WAAU,KAAMc,YAAYN,MAAMG,eAAe,cAAc;AACrEH,YAAMG,cAAc5B,aAAa,qBAAqB,OAAO;;IAEjEwB,QAAAA,SAAAA,OAAOpB,IAAI;AACP,aAAOA,MAAMA,GAAGoD,WAAWC,mBAAIrD,GAAGoD,QAAQ,EAAEE,KAAK,SAACC,OAAK;AAAA,eAAKC,aAAaD,OAAO,cAAc,MAAM;MAAQ,CAAA,IAAIrD;IACpH;EACJ;AACJ,CAAC;", "names": ["theme", "_ref", "dt", "concat", "classes", "root", "BaseStyle", "extend", "name", "BaseRipple", "BaseDirective", "extend", "style", "RippleStyle", "<PERSON><PERSON><PERSON>", "watch", "config<PERSON><PERSON><PERSON>", "newValue", "createRipple", "$host", "bindEvents", "setAttribute", "remove", "removeAttribute", "unmounted", "el", "timeout", "undefined", "methods", "addEventListener", "onMouseDown", "bind", "unbindEvents", "removeEventListener", "ink", "createElement", "_defineProperty", "role", "isUnstyled", "cx", "onAnimationEnd", "$attrSelector", "ptm", "append<PERSON><PERSON><PERSON>", "$el", "getInk", "event", "_this", "target", "currentTarget", "getComputedStyle", "display", "removeClass", "getHeight", "getWidth", "d", "Math", "max", "getOuterWidth", "getOuterHeight", "height", "width", "offset", "getOffset", "x", "pageX", "left", "document", "body", "scrollTop", "y", "pageY", "top", "scrollLeft", "addClass", "setTimeout", "clearTimeout", "children", "_toConsumableArray", "find", "child", "getAttribute"]}