"""API Documentation CRUD operations."""
import logging
from datetime import datetime
from typing import List, Optional, Dict, Any

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from api_docs_server.models.api_doc import ApiDoc
from api_docs_server.services.metadata_fetcher import MetadataFetcher

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def create_api_doc(
    db: AsyncSession,
    url: str,
    title: Optional[str] = None,
    description: Optional[str] = None,
    icon_url: Optional[str] = None,
    icon_data: Optional[str] = None,
    auto_metadata: bool = True
) -> ApiDoc:
    """创建API文档."""
    try:
        api_doc = ApiDoc(
            url=url,
            title=title,
            description=description,
            icon_url=icon_url,
            icon_data=icon_data,
            auto_metadata=auto_metadata
        )

        if auto_metadata:
            async with MetadataFetcher() as fetcher:
                metadata = await fetcher.fetch_metadata(url)
                if metadata:
                    api_doc.title = metadata.get("title") or api_doc.title
                    api_doc.description = metadata.get("description") or api_doc.description
                    api_doc.icon_url = metadata.get("icon_url") or api_doc.icon_url
                    api_doc.icon_data = metadata.get("icon_data") or api_doc.icon_data
                    api_doc.metadata_updated_at = datetime.utcnow()

        db.add(api_doc)
        await db.commit()
        await db.refresh(api_doc)
        return api_doc

    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to create API doc: {str(e)}")
        raise

async def get_api_doc(db: AsyncSession, api_doc_id: int) -> Optional[ApiDoc]:
    """获取API文档."""
    try:
        result = await db.execute(
            select(ApiDoc).where(ApiDoc.id == api_doc_id)
        )
        return result.scalars().first()
    except Exception as e:
        logger.error(f"Failed to get API doc: {str(e)}")
        raise

async def get_api_docs(
    db: AsyncSession,
    skip: int = 0,
    limit: int = 100
) -> List[ApiDoc]:
    """获取API文档列表."""
    try:
        result = await db.execute(
            select(ApiDoc)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    except Exception as e:
        logger.error(f"Failed to get API docs: {str(e)}")
        raise

async def update_api_doc(
    db: AsyncSession,
    api_doc_id: int,
    url: Optional[str] = None,
    title: Optional[str] = None,
    description: Optional[str] = None,
    icon_url: Optional[str] = None,
    icon_data: Optional[str] = None,
    auto_metadata: Optional[bool] = None
) -> Optional[ApiDoc]:
    """更新API文档."""
    try:
        api_doc = await get_api_doc(db, api_doc_id)
        if not api_doc:
            return None

        update_data: Dict[str, Any] = {}
        if url is not None:
            update_data["url"] = url
        if title is not None:
            update_data["title"] = title
        if description is not None:
            update_data["description"] = description
        if icon_url is not None:
            update_data["icon_url"] = icon_url
        if icon_data is not None:
            update_data["icon_data"] = icon_data
        if auto_metadata is not None:
            update_data["auto_metadata"] = auto_metadata

        if update_data:
            if "url" in update_data and api_doc.auto_metadata:
                async with MetadataFetcher() as fetcher:
                    metadata = await fetcher.fetch_metadata(update_data["url"])
                    if metadata:
                        update_data["title"] = metadata.get("title") or update_data.get("title")
                        update_data["description"] = metadata.get("description") or update_data.get("description")
                        update_data["icon_url"] = metadata.get("icon_url") or update_data.get("icon_url")
                        update_data["icon_data"] = metadata.get("icon_data") or update_data.get("icon_data")
                        update_data["metadata_updated_at"] = datetime.utcnow()

            for key, value in update_data.items():
                setattr(api_doc, key, value)

            await db.commit()
            await db.refresh(api_doc)

        return api_doc

    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to update API doc: {str(e)}")
        raise

async def delete_api_doc(db: AsyncSession, api_doc_id: int) -> bool:
    """删除API文档."""
    try:
        api_doc = await get_api_doc(db, api_doc_id)
        if not api_doc:
            return False

        await db.delete(api_doc)
        await db.commit()
        return True

    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to delete API doc: {str(e)}")
        raise

async def refresh_metadata(db: AsyncSession, api_doc_id: int) -> Optional[ApiDoc]:
    """刷新API文档元数据."""
    try:
        api_doc = await get_api_doc(db, api_doc_id)
        if not api_doc or not api_doc.auto_metadata:
            return None

        async with MetadataFetcher() as fetcher:
            metadata = await fetcher.fetch_metadata(api_doc.url)
            if metadata:
                api_doc.title = metadata.get("title") or api_doc.title
                api_doc.description = metadata.get("description") or api_doc.description
                api_doc.icon_url = metadata.get("icon_url") or api_doc.icon_url
                api_doc.icon_data = metadata.get("icon_data") or api_doc.icon_data
                api_doc.metadata_updated_at = datetime.utcnow()

                await db.commit()
                await db.refresh(api_doc)

        return api_doc

    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to refresh metadata: {str(e)}")
        raise
