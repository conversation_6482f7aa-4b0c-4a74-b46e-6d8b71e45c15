<template>
  <div class="doc-list-item" @click="$emit('preview', doc)">
    <div class="doc-icon">
      <i class="pi pi-file-o"></i>
    </div>
    <div class="doc-content">
      <div class="doc-title">
        <span v-if="searchQuery" v-html="highlightText(doc.title, searchQuery)"></span>
        <span v-else>{{ doc.title }}</span>
      </div>
      <div class="doc-url">
        <span v-if="searchQuery" v-html="highlightText(doc.url, searchQuery)"></span>
        <span v-else>{{ doc.url }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ApiDoc } from '@/types/api'

interface Props {
  doc: ApiDoc
  searchQuery?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'preview', doc: ApiDoc): void
  (e: 'menu', event: MouseEvent, doc: ApiDoc): void
}>()

function formatDate(date: string | undefined): string {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

function highlightText(text: string, query: string): string {
  if (!text || !query) return text

  const terms = query.toLowerCase().split(/\s+/).filter(Boolean)
  let result = text

  terms.forEach(term => {
    const regex = new RegExp(`(${term})`, 'gi')
    result = result.replace(regex, '<mark>$1</mark>')
  })

  return result
}
</script>

<style scoped>
.doc-list-item {
  padding: 0.75rem 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

.doc-list-item:hover {
  background: rgba(30, 41, 59, 0.7);
  border-color: rgba(255, 255, 255, 0.2);
}

.doc-icon {
  flex: 0 0 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
}

.doc-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.doc-title {
  font-size: 0.9375rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.doc-url {
  font-size: 0.8125rem;
  color: rgba(255, 255, 255, 0.5);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(mark) {
  background-color: rgba(var(--primary-color-rgb), 0.3);
  color: #fff;
  padding: 0 2px;
  border-radius: 2px;
}

@media screen and (max-width: 768px) {
  .doc-list-item {
    padding: 0.625rem 0.875rem;
  }
}
</style>
