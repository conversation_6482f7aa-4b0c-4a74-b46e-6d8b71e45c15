app_name: api_docs_server
label_name: api-docs-server
template: deployment
output_yaml_basename: api_docs_server
startup_command: ""
components:
  backend:
    name: api_docs_server-backend
    label: api-docs-server-backend
  frontend:
    name: api_docs_server-frontend
    label: api-docs-server-frontend
docker_image:
  backend:
    name: api_docs_server-backend
  frontend:
    name: api_docs_server-frontend
branches:
  sxz:
    env:
      - env_key: POSTGRES_USER
        env_value: 'api_docs'
      - env_key: POSTGRES_HOST
        env_value: '************'
      - env_key: POSTGRES_PORT
        env_value: '5432'
      - env_key: POSTGRES_DB
        env_value: 'api_docs'
domain: api-docs.woa.com
