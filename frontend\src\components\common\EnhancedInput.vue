<template>
  <div class="field" :class="{ 'search-input': type === 'search' }">
    <label v-if="label" :for="id" class="font-bold block mb-2">
      {{ label }}
      <Tooltip v-if="shortcuts.length > 0" position="right">
        <template #target>
          <i class="pi pi-info-circle text-sm ml-2" />
        </template>
        <div class="text-sm">
          支持的快捷键：<br/>
          <template v-for="shortcut in shortcuts" :key="shortcut.key">
            - {{ shortcut.key }}：{{ shortcut.description }}<br/>
          </template>
        </div>
      </Tooltip>
    </label>

    <div class="p-input-wrapper">
      <!-- 搜索图标 -->
      <i v-if="type === 'search'" class="pi pi-search input-icon" />

      <component
        :is="inputComponent"
        :id="id"
        :ref="inputRef"
        v-model="inputValue"
        :type="type === 'search' ? 'text' : type"
        :class="[
          'w-full',
          { 'p-invalid': invalid && dirty },
          { 'pl-4': type === 'search' },
          inputClass
        ]"
        :rows="rows"
        :placeholder="placeholder"
        :autoResize="autoResize"
        @blur="handleBlur"
        @input="handleInput"
        @paste="handlePaste"
        @keydown="handleKeydown"
        v-bind="$attrs"
      />

      <!-- 清除按钮 -->
      <i
        v-if="type === 'search' && inputValue"
        class="pi pi-times input-icon-right clickable"
        @click="clearInput"
      />
    </div>

    <small v-if="errorMessage && dirty" class="p-error block mt-1">
      {{ errorMessage }}
    </small>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import Tooltip from 'primevue/tooltip'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'

interface Shortcut {
  key: string
  description: string
  handler?: (event: KeyboardEvent) => void
}

interface Props {
  modelValue: string
  id?: string
  label?: string
  type?: 'text' | 'url' | 'search' | 'textarea'
  placeholder?: string
  shortcuts?: Shortcut[]
  invalid?: boolean
  dirty?: boolean
  errorMessage?: string
  rows?: number
  autoResize?: boolean
  inputClass?: string
  autofocus?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  placeholder: '',
  shortcuts: () => [],
  invalid: false,
  dirty: false,
  rows: 3,
  autoResize: false,
  inputClass: ''
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'blur': [event: FocusEvent]
  'input': [event: Event]
  'enter': [event: KeyboardEvent]
  'paste': [event: ClipboardEvent]
  'tab': [event: KeyboardEvent]
}>()

const inputRef = ref<HTMLInputElement | HTMLTextAreaElement>()
const inputValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 根据类型选择输入组件
const inputComponent = computed(() => {
  return props.type === 'textarea' ? Textarea : InputText
})

// 处理输入事件
const handleInput = (event: Event) => {
  emit('input', event)
}

// 处理失焦事件
const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}

// 处理粘贴事件
const handlePaste = (event: ClipboardEvent) => {
  emit('paste', event)
}

// 处理按键事件
const handleKeydown = (event: KeyboardEvent) => {
  // 处理快捷键
  for (const shortcut of props.shortcuts) {
    if (shortcut.handler) {
      shortcut.handler(event)
    }
  }

  // 处理回车键
  if (event.key === 'Enter' && !event.shiftKey) {
    emit('enter', event)
  }

  // 处理Tab键
  if (event.key === 'Tab') {
    emit('tab', event)
  }

  // 处理 Esc 键清空搜索
  if (event.key === 'Escape' && props.type === 'search') {
    clearInput()
  }
}

// 清空输入
const clearInput = () => {
  inputValue.value = ''
  inputRef.value?.focus()
}

// 自动聚焦
watch(
  () => props.autofocus,
  (newValue) => {
    if (newValue && inputRef.value) {
      inputRef.value.focus()
    }
  },
  { immediate: true }
)
</script>

<style scoped>
.field {
  margin-bottom: 1rem;
}

.p-invalid {
  border-color: var(--red-500) !important;
}

.p-error {
  color: var(--red-500);
  font-size: 0.875rem;
}

:deep(.p-inputtext) {
  font-family: var(--font-family);
}

:deep(.p-tooltip) {
  max-width: 250px;
}

.p-input-wrapper {
  position: relative;
  display: inline-flex;
  width: 100%;
}

.input-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-color-secondary);
  z-index: 1;
}

.input-icon-right {
  left: auto;
  right: 0.75rem;
}

.clickable {
  cursor: pointer;
  
  &:hover {
    color: var(--text-color);
  }
}

.search-input {
  :deep(.p-inputtext) {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }
}
</style>
