"""Metadata fetcher service."""
import logging
from typing import Dict, Optional
from urllib.parse import urljoin, urlparse

import aiohttp
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MetadataFetcher:
    """元数据获取服务."""

    async def fetch_metadata(self, url: str) -> Optional[Dict[str, str]]:
        """获取URL的元数据.

        Args:
            url: 要获取元数据的URL

        Returns:
            包含元数据的字典，如果获取失败则返回None
        """
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=10) as response:
                    if response.status != 200:
                        logger.error(f"获取URL失败: {url}, 状态码: {response.status}")
                        return None

                    html = await response.text()
                    soup = BeautifulSoup(html, "html.parser")

                    metadata = {
                        "title": self._get_title(soup),
                        "description": self._get_description(soup),
                        "icon_url": await self._get_icon_url(soup, url, session),
                        "icon_data": None  # 暂时不获取图标数据
                    }

                    return metadata
        except aiohttp.ClientError as e:
            logger.error(f"HTTP请求失败: {url}, 错误: {e}")
            return None
        except Exception as e:
            logger.error(f"获取元数据失败: {url}, 错误: {e}")
            return None

    def _get_title(self, soup: BeautifulSoup) -> Optional[str]:
        """获取页面标题."""
        # 尝试从meta标签获取
        og_title = soup.find("meta", property="og:title")
        if og_title and og_title.get("content"):
            return og_title["content"]

        # 尝试从title标签获取
        title = soup.find("title")
        if title and title.string:
            return title.string.strip()

        return None

    def _get_description(self, soup: BeautifulSoup) -> Optional[str]:
        """获取页面描述."""
        # 尝试从meta标签获取
        og_desc = soup.find("meta", property="og:description")
        if og_desc and og_desc.get("content"):
            return og_desc["content"]

        meta_desc = soup.find("meta", attrs={"name": "description"})
        if meta_desc and meta_desc.get("content"):
            return meta_desc["content"]

        return None

    async def _get_icon_url(self, soup: BeautifulSoup, base_url: str, session: aiohttp.ClientSession) -> Optional[str]:
        """获取图标URL.
        
        Args:
            soup: BeautifulSoup对象
            base_url: 基础URL
            session: aiohttp会话
            
        Returns:
            图标URL或None
        """
        try:
            # 1. 尝试从link标签获取favicon
            for rel in ['icon', 'shortcut icon', 'apple-touch-icon']:
                icon_link = soup.find('link', rel=rel)
                if icon_link and icon_link.get('href'):
                    icon_url = urljoin(base_url, icon_link['href'])
                    try:
                        async with session.head(icon_url, timeout=5, allow_redirects=True) as response:
                            if response.status == 200:
                                logger.info(f"Found icon from link tag: {icon_url}")
                                return icon_url
                    except Exception as e:
                        logger.warning(f"Failed to verify icon URL {icon_url}: {e}")
                        continue

            # 2. 尝试从meta标签获取
            meta_icon = soup.find('meta', property='og:image')
            if meta_icon and meta_icon.get('content'):
                icon_url = urljoin(base_url, meta_icon['content'])
                try:
                    async with session.head(icon_url, timeout=5, allow_redirects=True) as response:
                        if response.status == 200:
                            logger.info(f"Found icon from meta tag: {icon_url}")
                            return icon_url
                except Exception as e:
                    logger.warning(f"Failed to verify meta icon URL {icon_url}: {e}")

            # 3. 尝试访问根目录的favicon.ico
            parsed_url = urlparse(base_url)
            favicon_url = f"{parsed_url.scheme}://{parsed_url.netloc}/favicon.ico"
            try:
                async with session.head(favicon_url, timeout=5, allow_redirects=True) as response:
                    if response.status == 200:
                        logger.info(f"Found default favicon.ico: {favicon_url}")
                        return favicon_url
            except Exception as e:
                logger.warning(f"Failed to verify default favicon.ico {favicon_url}: {e}")

            logger.warning(f"No valid icon found for {base_url}")
            return None

        except Exception as e:
            logger.error(f"Error while getting icon URL for {base_url}: {e}")
            return None
