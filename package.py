"""Contains information and properties pertaining to Rez package."""
name = "api_docs_server"
authors = ["Hallong"]
uuid = "7f793158-0709-4593-8ba9-85ec9ebb7201"
description = "Collection of API docs."
homepage = "https://git.woa.com/lightbox/microservices/api_docs_server"
tools = []
build_requires = []
private_build_requires = [
    "rez_builder-0",
    "setuptools_scm-1.15",
]
requires = [
    "python-3.10",
    "lightbox_config-1",
    "setuptools-41..100",
    "fastapi",
    "sqlalchemy",
    "uvicorn",
    "python_dotenv",
    "httpx",
    "pydantic_settings",
    "beautifulsoup4",
]
# Internal variable used by `rezolve_me` for dev environment.
# Overrides `requires`. Uncomment this only if needed.
dev_requires = requires + ["pytest-7.2", "pytest_cov-4.0", "python-3.10", "nodejs", "caddy", "kubectl", "uv"]
variants = []
tests = {
}


def commands():
    """Set up package."""
    env.PYTHONPATH.prepend("{this.root}/site-packages")  # noqa: F821
