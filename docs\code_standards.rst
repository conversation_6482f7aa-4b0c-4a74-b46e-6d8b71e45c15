开发规范
========

核心开发原则
----------

代码质量与可维护性
~~~~~~~~~~~~~~~
* 优先考虑干净、可读和可维护的代码
* 追求简单、清晰的解决方案
* 应用适当的设计模式
* 编写模块化代码

性能与效率
~~~~~~~~
* 使用高效的算法和数据结构
* 关注代码性能和资源使用
* 优化查询和数据处理

安全与稳定性
~~~~~~~~~~
* 实现健壮的错误处理和日志记录
* 优先考虑安全编码实践
* 进行输入验证和数据清理

测试与质量保证
~~~~~~~~~~~
* 为所有关键功能编写单元测试
* 确保测试覆盖率达到项目标准
* 定期运行自动化测试套件

编码规范
-------

通用规范
~~~~~~~
* 行长度限制：120 字符
* 使用制表符进行缩进
* 保持代码格式一致性
* 添加清晰、简洁的英文注释

命名约定
~~~~~~~
* 变量：snake_case
* 类名：PascalCase
* 函数：snake_case
* 常量：全大写 UPPER_CASE
* 文件名：小写，使用下划线分隔

Python 特定规范
~~~~~~~~~~~~
* 严格遵循 Google Python 风格指南
* 使用类型提示（Type Hints）
* 编写清晰的 docstrings
* 按类型分组导入语句：
    1. 标准库
    2. 第三方库
    3. 本地模块
* 使用绝对导入路径

项目组织
-------

文件组织
~~~~~~~
* 模块化组织代码
* 相关功能放在同一目录
* 使用清晰的目录结构
* 保持文件大小合理

测试组织
~~~~~~~
* 测试文件位于 tests/ 目录
* 测试文件命名：test_*.py
* 按功能模块组织测试
* 维护测试数据和夹具

文档管理
~~~~~~~
* 项目文档位于 docs/ 目录
* 保持文档及时更新
* 包含必要的示例和说明
* 记录重要的设计决策
